# Chapter 11: Performance Tracking and Evaluation

## Overview
In this chapter, we'll implement a comprehensive performance tracking and evaluation system for hospital employees. This system will include KPI tracking, performance reviews, goal setting, and evaluation workflows with Indonesian healthcare performance standards.

## Learning Objectives
- Create performance tracking system with healthcare KPIs
- Implement performance review workflows
- Build goal setting and tracking functionality
- Create performance analytics and reporting
- Implement Indonesian healthcare performance standards
- Build responsive React components for performance management

## Prerequisites
- Completed MVP Phase (Chapters 1-10)
- Understanding of performance management concepts
- Familiarity with healthcare performance indicators

## Duration
120-150 minutes

---

## Step 1: Create Performance Models

### 1.1 Create Performance Tracking Models

```bash
php artisan make:model PerformanceReview -m
php artisan make:model PerformanceGoal -m
php artisan make:model PerformanceMetric -m
php artisan make:model PerformanceEvaluation -m
```

Edit `database/migrations/create_performance_reviews_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('performance_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->foreignId('reviewer_id')->constrained('employees')->onDelete('cascade');
            $table->string('review_period'); // 'quarterly', 'semi_annual', 'annual'
            $table->date('review_start_date');
            $table->date('review_end_date');
            $table->date('due_date');
            $table->enum('status', ['draft', 'in_progress', 'completed', 'approved', 'rejected'])
                  ->default('draft');
            
            // Overall ratings (1-5 scale)
            $table->decimal('overall_rating', 3, 2)->nullable();
            $table->decimal('clinical_competency_rating', 3, 2)->nullable();
            $table->decimal('communication_rating', 3, 2)->nullable();
            $table->decimal('teamwork_rating', 3, 2)->nullable();
            $table->decimal('professionalism_rating', 3, 2)->nullable();
            $table->decimal('leadership_rating', 3, 2)->nullable();
            
            // Comments and feedback
            $table->text('strengths')->nullable();
            $table->text('areas_for_improvement')->nullable();
            $table->text('development_plan')->nullable();
            $table->text('employee_comments')->nullable();
            $table->text('reviewer_comments')->nullable();
            
            // Indonesian healthcare specific
            $table->decimal('patient_safety_rating', 3, 2)->nullable();
            $table->decimal('infection_control_rating', 3, 2)->nullable();
            $table->decimal('documentation_quality_rating', 3, 2)->nullable();
            $table->decimal('continuing_education_rating', 3, 2)->nullable();
            
            // Approval workflow
            $table->foreignId('approved_by')->nullable()->constrained('employees');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_comments')->nullable();
            
            $table->timestamps();
            
            $table->index(['employee_id', 'review_period']);
            $table->index(['reviewer_id', 'status']);
            $table->index(['due_date', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('performance_reviews');
    }
};
```

Edit `database/migrations/create_performance_goals_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('performance_goals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->foreignId('set_by')->constrained('employees')->onDelete('cascade');
            $table->foreignId('performance_review_id')->nullable()->constrained()->onDelete('cascade');
            
            $table->string('title');
            $table->text('description');
            $table->enum('category', [
                'clinical_skills',
                'patient_care',
                'communication',
                'teamwork',
                'leadership',
                'professional_development',
                'quality_improvement',
                'safety',
                'efficiency'
            ]);
            
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('status', ['not_started', 'in_progress', 'completed', 'cancelled'])
                  ->default('not_started');
            
            // Target and measurement
            $table->text('success_criteria');
            $table->decimal('target_value', 10, 2)->nullable();
            $table->string('measurement_unit')->nullable();
            $table->decimal('current_value', 10, 2)->nullable();
            $table->decimal('completion_percentage', 5, 2)->default(0);
            
            // Timeline
            $table->date('start_date');
            $table->date('target_date');
            $table->date('completed_date')->nullable();
            
            // Progress tracking
            $table->text('progress_notes')->nullable();
            $table->json('milestones')->nullable(); // Array of milestone objects
            $table->json('resources_needed')->nullable(); // Array of resources
            
            // Indonesian healthcare specific
            $table->boolean('is_mandatory_training')->default(false);
            $table->string('certification_required')->nullable();
            $table->boolean('affects_license')->default(false);
            
            $table->timestamps();
            
            $table->index(['employee_id', 'status']);
            $table->index(['category', 'priority']);
            $table->index(['target_date', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('performance_goals');
    }
};
```

Edit `database/migrations/create_performance_metrics_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('performance_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->foreignId('department_id')->constrained()->onDelete('cascade');
            
            $table->string('metric_name');
            $table->string('metric_code')->unique();
            $table->text('description');
            $table->enum('category', [
                'productivity',
                'quality',
                'safety',
                'patient_satisfaction',
                'attendance',
                'compliance',
                'efficiency',
                'clinical_outcomes'
            ]);
            
            // Measurement details
            $table->string('measurement_unit');
            $table->enum('measurement_frequency', ['daily', 'weekly', 'monthly', 'quarterly', 'annually']);
            $table->decimal('target_value', 10, 2);
            $table->decimal('minimum_acceptable', 10, 2)->nullable();
            $table->decimal('maximum_acceptable', 10, 2)->nullable();
            
            // Current period data
            $table->decimal('current_value', 10, 2)->nullable();
            $table->decimal('previous_value', 10, 2)->nullable();
            $table->decimal('variance_percentage', 5, 2)->nullable();
            $table->date('last_measured_date')->nullable();
            
            // Trend analysis
            $table->enum('trend', ['improving', 'stable', 'declining', 'unknown'])->default('unknown');
            $table->json('historical_data')->nullable(); // Array of historical measurements
            
            // Indonesian healthcare specific
            $table->boolean('is_regulatory_requirement')->default(false);
            $table->string('regulatory_body')->nullable(); // e.g., 'KARS', 'Kemenkes'
            $table->boolean('affects_accreditation')->default(false);
            $table->string('accreditation_standard')->nullable();
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['employee_id', 'category']);
            $table->index(['department_id', 'is_active']);
            $table->index(['measurement_frequency', 'last_measured_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('performance_metrics');
    }
};
```

Edit `database/migrations/create_performance_evaluations_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('performance_evaluations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('performance_review_id')->constrained()->onDelete('cascade');
            $table->foreignId('evaluator_id')->constrained('employees')->onDelete('cascade');
            $table->enum('evaluator_type', ['supervisor', 'peer', 'subordinate', 'self', 'patient']);
            
            // Evaluation criteria (1-5 scale)
            $table->json('evaluation_scores'); // Flexible scoring system
            $table->decimal('weighted_score', 5, 2)->nullable();
            
            // Specific feedback
            $table->text('strengths_feedback')->nullable();
            $table->text('improvement_feedback')->nullable();
            $table->text('specific_examples')->nullable();
            $table->text('recommendations')->nullable();
            
            // Indonesian healthcare specific evaluations
            $table->decimal('patient_interaction_score', 3, 2)->nullable();
            $table->decimal('cultural_sensitivity_score', 3, 2)->nullable();
            $table->decimal('bahasa_proficiency_score', 3, 2)->nullable();
            $table->decimal('local_protocol_adherence_score', 3, 2)->nullable();
            
            $table->enum('status', ['pending', 'completed', 'reviewed'])->default('pending');
            $table->timestamp('completed_at')->nullable();
            
            $table->timestamps();
            
            $table->index(['performance_review_id', 'evaluator_type']);
            $table->index(['evaluator_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('performance_evaluations');
    }
};
```

---

## Step 2: Create Performance Models

### 2.1 Create PerformanceReview Model

Edit `app/Models/PerformanceReview.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class PerformanceReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'reviewer_id',
        'review_period',
        'review_start_date',
        'review_end_date',
        'due_date',
        'status',
        'overall_rating',
        'clinical_competency_rating',
        'communication_rating',
        'teamwork_rating',
        'professionalism_rating',
        'leadership_rating',
        'patient_safety_rating',
        'infection_control_rating',
        'documentation_quality_rating',
        'continuing_education_rating',
        'strengths',
        'areas_for_improvement',
        'development_plan',
        'employee_comments',
        'reviewer_comments',
        'approved_by',
        'approved_at',
        'approval_comments',
    ];

    protected $casts = [
        'review_start_date' => 'date',
        'review_end_date' => 'date',
        'due_date' => 'date',
        'approved_at' => 'datetime',
        'overall_rating' => 'decimal:2',
        'clinical_competency_rating' => 'decimal:2',
        'communication_rating' => 'decimal:2',
        'teamwork_rating' => 'decimal:2',
        'professionalism_rating' => 'decimal:2',
        'leadership_rating' => 'decimal:2',
        'patient_safety_rating' => 'decimal:2',
        'infection_control_rating' => 'decimal:2',
        'documentation_quality_rating' => 'decimal:2',
        'continuing_education_rating' => 'decimal:2',
    ];

    // Relationships
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'reviewer_id');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    public function goals(): HasMany
    {
        return $this->hasMany(PerformanceGoal::class);
    }

    public function evaluations(): HasMany
    {
        return $this->hasMany(PerformanceEvaluation::class);
    }

    // Accessors
    public function getIndonesianStatusAttribute(): string
    {
        return match($this->status) {
            'draft' => 'Draft',
            'in_progress' => 'Sedang Berlangsung',
            'completed' => 'Selesai',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            default => 'Tidak Diketahui',
        };
    }

    public function getIndonesianReviewPeriodAttribute(): string
    {
        return match($this->review_period) {
            'quarterly' => 'Triwulan',
            'semi_annual' => 'Semester',
            'annual' => 'Tahunan',
            default => 'Tidak Diketahui',
        };
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date < now() && !in_array($this->status, ['completed', 'approved']);
    }

    public function getDaysUntilDueAttribute(): int
    {
        return now()->diffInDays($this->due_date, false);
    }

    // Business Logic Methods
    public function calculateOverallRating(): float
    {
        $ratings = [
            $this->clinical_competency_rating,
            $this->communication_rating,
            $this->teamwork_rating,
            $this->professionalism_rating,
            $this->patient_safety_rating,
            $this->infection_control_rating,
            $this->documentation_quality_rating,
        ];

        $validRatings = array_filter($ratings, fn($rating) => $rating !== null);
        
        if (empty($validRatings)) {
            return 0;
        }

        return round(array_sum($validRatings) / count($validRatings), 2);
    }

    public function getPerformanceLevel(): string
    {
        $rating = $this->overall_rating ?? $this->calculateOverallRating();
        
        return match(true) {
            $rating >= 4.5 => 'Sangat Baik',
            $rating >= 3.5 => 'Baik',
            $rating >= 2.5 => 'Cukup',
            $rating >= 1.5 => 'Kurang',
            default => 'Sangat Kurang',
        };
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'completed' && $this->overall_rating !== null;
    }

    public function approve(Employee $approver, string $comments = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_comments' => $comments,
        ]);

        return true;
    }

    // Scopes
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeByReviewer($query, $reviewerId)
    {
        return $query->where('reviewer_id', $reviewerId);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['completed', 'approved']);
    }

    public function scopeByPeriod($query, $period)
    {
        return $query->where('review_period', $period);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
```

---

## Step 3: Create Controllers and Services

### 3.1 Create Performance Controllers

```bash
php artisan make:controller Api/PerformanceController --api
php artisan make:controller Api/PerformanceReviewController --api
php artisan make:controller PerformanceController
php artisan make:controller PerformanceReviewController
```

Create `app/Http/Controllers/Api/PerformanceController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PerformanceService;
use App\Http\Requests\PerformanceRequest;
use App\Http\Resources\PerformanceResource;
use App\Models\Employee;
use App\Models\Department;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PerformanceController extends Controller
{
    protected PerformanceService $performanceService;

    public function __construct(PerformanceService $performanceService)
    {
        $this->performanceService = $performanceService;
    }

    /**
     * Get performance dashboard data
     */
    public function dashboard(Request $request): JsonResponse
    {
        $request->validate([
            'department_id' => 'nullable|exists:departments,id',
            'period' => 'nullable|in:month,quarter,year',
            'year' => 'nullable|integer|min:2020|max:2030'
        ]);

        $data = $this->performanceService->getDashboardData(
            $request->department_id,
            $request->get('period', 'month'),
            $request->get('year', now()->year)
        );

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get employee performance overview
     */
    public function employeeOverview(Employee $employee): JsonResponse
    {
        $overview = $this->performanceService->getEmployeePerformanceOverview($employee);

        return response()->json([
            'success' => true,
            'data' => $overview
        ]);
    }

    /**
     * Get department performance statistics
     */
    public function departmentStats(Request $request): JsonResponse
    {
        $request->validate([
            'department_id' => 'nullable|exists:departments,id',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from'
        ]);

        $stats = $this->performanceService->getDepartmentStats(
            $request->department_id,
            $request->date_from,
            $request->date_to
        );

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get performance trends
     */
    public function trends(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'nullable|exists:employees,id',
            'department_id' => 'nullable|exists:departments,id',
            'metric_type' => 'nullable|string',
            'period' => 'required|in:week,month,quarter,year',
            'periods_count' => 'nullable|integer|min:1|max:24'
        ]);

        $trends = $this->performanceService->getPerformanceTrends(
            $request->only(['employee_id', 'department_id', 'metric_type']),
            $request->period,
            $request->get('periods_count', 12)
        );

        return response()->json([
            'success' => true,
            'data' => $trends
        ]);
    }

    /**
     * Get KPI summary
     */
    public function kpiSummary(Request $request): JsonResponse
    {
        $request->validate([
            'department_id' => 'nullable|exists:departments,id',
            'employee_type' => 'nullable|in:doctor,nurse,admin,technician,support'
        ]);

        $summary = $this->performanceService->getKPISummary(
            $request->department_id,
            $request->employee_type
        );

        return response()->json([
            'success' => true,
            'data' => $summary
        ]);
    }

    /**
     * Update employee KPI
     */
    public function updateKPI(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'metric_type' => 'required|string',
            'value' => 'required|numeric',
            'target' => 'nullable|numeric',
            'period' => 'required|date',
            'notes' => 'nullable|string|max:1000'
        ]);

        $kpi = $this->performanceService->updateKPI(
            $request->employee_id,
            $request->metric_type,
            $request->value,
            $request->target,
            $request->period,
            $request->notes
        );

        return response()->json([
            'success' => true,
            'data' => new PerformanceResource($kpi),
            'message' => 'KPI berhasil diperbarui'
        ]);
    }
}
```

Create `app/Http/Controllers/Api/PerformanceReviewController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PerformanceReviewService;
use App\Http\Requests\StorePerformanceReviewRequest;
use App\Http\Requests\UpdatePerformanceReviewRequest;
use App\Http\Resources\PerformanceReviewResource;
use App\Models\PerformanceReview;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PerformanceReviewController extends Controller
{
    protected PerformanceReviewService $reviewService;

    public function __construct(PerformanceReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    /**
     * Get performance reviews
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'nullable|exists:employees,id',
            'reviewer_id' => 'nullable|exists:users,id',
            'status' => 'nullable|in:draft,pending,in_review,completed,cancelled',
            'review_type' => 'nullable|in:annual,quarterly,monthly,probation,special',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from'
        ]);

        $reviews = $this->reviewService->getReviews($request->all());

        return response()->json([
            'success' => true,
            'data' => PerformanceReviewResource::collection($reviews)
        ]);
    }

    /**
     * Store new performance review
     */
    public function store(StorePerformanceReviewRequest $request): JsonResponse
    {
        $review = $this->reviewService->createReview($request->validated());

        return response()->json([
            'success' => true,
            'data' => new PerformanceReviewResource($review),
            'message' => 'Review kinerja berhasil dibuat'
        ], 201);
    }

    /**
     * Get specific performance review
     */
    public function show(PerformanceReview $performanceReview): JsonResponse
    {
        $review = $this->reviewService->getReviewDetails($performanceReview);

        return response()->json([
            'success' => true,
            'data' => new PerformanceReviewResource($review)
        ]);
    }

    /**
     * Update performance review
     */
    public function update(UpdatePerformanceReviewRequest $request, PerformanceReview $performanceReview): JsonResponse
    {
        $review = $this->reviewService->updateReview($performanceReview, $request->validated());

        return response()->json([
            'success' => true,
            'data' => new PerformanceReviewResource($review),
            'message' => 'Review kinerja berhasil diperbarui'
        ]);
    }

    /**
     * Submit review for approval
     */
    public function submit(PerformanceReview $performanceReview): JsonResponse
    {
        $review = $this->reviewService->submitReview($performanceReview);

        return response()->json([
            'success' => true,
            'data' => new PerformanceReviewResource($review),
            'message' => 'Review berhasil disubmit untuk persetujuan'
        ]);
    }

    /**
     * Approve performance review
     */
    public function approve(Request $request, PerformanceReview $performanceReview): JsonResponse
    {
        $request->validate([
            'approval_notes' => 'nullable|string|max:1000'
        ]);

        $review = $this->reviewService->approveReview(
            $performanceReview,
            $request->approval_notes
        );

        return response()->json([
            'success' => true,
            'data' => new PerformanceReviewResource($review),
            'message' => 'Review kinerja berhasil disetujui'
        ]);
    }

    /**
     * Reject performance review
     */
    public function reject(Request $request, PerformanceReview $performanceReview): JsonResponse
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000'
        ]);

        $review = $this->reviewService->rejectReview(
            $performanceReview,
            $request->rejection_reason
        );

        return response()->json([
            'success' => true,
            'data' => new PerformanceReviewResource($review),
            'message' => 'Review kinerja ditolak'
        ]);
    }

    /**
     * Get review templates
     */
    public function templates(): JsonResponse
    {
        $templates = $this->reviewService->getReviewTemplates();

        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }
}
```

### 3.2 Create Performance Services

```bash
php artisan make:service PerformanceService
php artisan make:service PerformanceReviewService
php artisan make:service PerformanceCalculationService
```

Create `app/Services/PerformanceService.php`:

```php
<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\Department;
use App\Models\PerformanceMetric;
use App\Models\PerformanceReview;
use App\Models\PerformanceGoal;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class PerformanceService
{
    protected PerformanceCalculationService $calculationService;

    public function __construct(PerformanceCalculationService $calculationService)
    {
        $this->calculationService = $calculationService;
    }

    /**
     * Get performance dashboard data
     */
    public function getDashboardData(?int $departmentId = null, string $period = 'month', int $year = null): array
    {
        $year = $year ?? now()->year;

        $query = Employee::with(['department', 'performanceMetrics', 'performanceReviews']);

        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        $employees = $query->where('status', 'active')->get();

        return [
            'overview' => $this->getOverviewStats($employees, $period, $year),
            'kpi_summary' => $this->getKPISummary($departmentId),
            'recent_reviews' => $this->getRecentReviews($departmentId),
            'top_performers' => $this->getTopPerformers($departmentId, $period, $year),
            'improvement_needed' => $this->getImprovementNeeded($departmentId, $period, $year),
            'trends' => $this->getPerformanceTrends(['department_id' => $departmentId], $period, 6)
        ];
    }

    /**
     * Get employee performance overview
     */
    public function getEmployeePerformanceOverview(Employee $employee): array
    {
        $currentYear = now()->year;

        return [
            'employee' => [
                'id' => $employee->id,
                'name' => $employee->full_name,
                'employee_number' => $employee->employee_number,
                'department' => $employee->department?->name,
                'position' => $employee->position?->name,
                'hire_date' => $employee->hire_date?->format('Y-m-d')
            ],
            'current_metrics' => $this->getCurrentMetrics($employee),
            'goals' => $this->getEmployeeGoals($employee, $currentYear),
            'recent_reviews' => $this->getEmployeeReviews($employee, 5),
            'performance_trend' => $this->getEmployeePerformanceTrend($employee, 12),
            'achievements' => $this->getEmployeeAchievements($employee, $currentYear),
            'development_areas' => $this->getDevelopmentAreas($employee)
        ];
    }

    /**
     * Get department performance statistics
     */
    public function getDepartmentStats(?int $departmentId = null, ?string $dateFrom = null, ?string $dateTo = null): array
    {
        $query = Employee::with(['performanceMetrics', 'performanceReviews']);

        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        $employees = $query->where('status', 'active')->get();

        $dateFrom = $dateFrom ? Carbon::parse($dateFrom) : now()->startOfYear();
        $dateTo = $dateTo ? Carbon::parse($dateTo) : now();

        return [
            'total_employees' => $employees->count(),
            'reviewed_employees' => $this->getReviewedEmployeesCount($employees, $dateFrom, $dateTo),
            'average_rating' => $this->getAverageRating($employees, $dateFrom, $dateTo),
            'goal_completion_rate' => $this->getGoalCompletionRate($employees, $dateFrom, $dateTo),
            'performance_distribution' => $this->getPerformanceDistribution($employees, $dateFrom, $dateTo),
            'kpi_averages' => $this->getKPIAverages($employees, $dateFrom, $dateTo),
            'improvement_trends' => $this->getImprovementTrends($employees, $dateFrom, $dateTo)
        ];
    }

    /**
     * Get performance trends
     */
    public function getPerformanceTrends(array $filters, string $period, int $periodsCount): array
    {
        $trends = [];
        $endDate = now();

        for ($i = 0; $i < $periodsCount; $i++) {
            $startDate = match ($period) {
                'week' => $endDate->copy()->subWeeks($i + 1),
                'month' => $endDate->copy()->subMonths($i + 1),
                'quarter' => $endDate->copy()->subQuarters($i + 1),
                'year' => $endDate->copy()->subYears($i + 1),
                default => $endDate->copy()->subMonths($i + 1)
            };

            $periodEnd = match ($period) {
                'week' => $startDate->copy()->addWeek(),
                'month' => $startDate->copy()->addMonth(),
                'quarter' => $startDate->copy()->addQuarter(),
                'year' => $startDate->copy()->addYear(),
                default => $startDate->copy()->addMonth()
            };

            $metrics = $this->getMetricsForPeriod($filters, $startDate, $periodEnd);

            $trends[] = [
                'period' => $startDate->format('Y-m-d'),
                'period_label' => $this->getPeriodLabel($startDate, $period),
                'metrics' => $metrics
            ];
        }

        return array_reverse($trends);
    }

    /**
     * Get KPI summary
     */
    public function getKPISummary(?int $departmentId = null, ?string $employeeType = null): array
    {
        $query = PerformanceMetric::with(['employee.department'])
            ->whereHas('employee', function ($q) {
                $q->where('status', 'active');
            });

        if ($departmentId) {
            $query->whereHas('employee', function ($q) use ($departmentId) {
                $q->where('department_id', $departmentId);
            });
        }

        if ($employeeType) {
            $query->whereHas('employee', function ($q) use ($employeeType) {
                $q->where('employee_type', $employeeType);
            });
        }

        $metrics = $query->where('period', '>=', now()->startOfYear())->get();

        $kpiTypes = [
            'patient_satisfaction' => 'Kepuasan Pasien',
            'quality_score' => 'Skor Kualitas',
            'efficiency_rating' => 'Rating Efisiensi',
            'attendance_rate' => 'Tingkat Kehadiran',
            'safety_compliance' => 'Kepatuhan Keselamatan',
            'teamwork_score' => 'Skor Kerjasama Tim',
            'professional_development' => 'Pengembangan Profesional'
        ];

        $summary = [];

        foreach ($kpiTypes as $type => $label) {
            $typeMetrics = $metrics->where('metric_type', $type);

            if ($typeMetrics->isNotEmpty()) {
                $summary[$type] = [
                    'label' => $label,
                    'average' => round($typeMetrics->avg('value'), 2),
                    'target' => round($typeMetrics->avg('target'), 2),
                    'achievement_rate' => $this->calculateAchievementRate($typeMetrics),
                    'trend' => $this->calculateTrend($typeMetrics),
                    'count' => $typeMetrics->count()
                ];
            }
        }

        return $summary;
    }

    /**
     * Update employee KPI
     */
    public function updateKPI(int $employeeId, string $metricType, float $value, ?float $target, string $period, ?string $notes): PerformanceMetric
    {
        return PerformanceMetric::updateOrCreate(
            [
                'employee_id' => $employeeId,
                'metric_type' => $metricType,
                'period' => Carbon::parse($period)->format('Y-m-d')
            ],
            [
                'value' => $value,
                'target' => $target,
                'notes' => $notes,
                'updated_by' => auth()->id()
            ]
        );
    }

    // Private helper methods
    private function getOverviewStats(Collection $employees, string $period, int $year): array
    {
        $totalEmployees = $employees->count();
        $reviewedEmployees = $employees->filter(function ($employee) use ($year) {
            return $employee->performanceReviews()
                ->whereYear('review_period_start', $year)
                ->exists();
        })->count();

        return [
            'total_employees' => $totalEmployees,
            'reviewed_employees' => $reviewedEmployees,
            'review_completion_rate' => $totalEmployees > 0 ? round(($reviewedEmployees / $totalEmployees) * 100, 1) : 0,
            'average_rating' => $this->getAverageRating($employees, now()->startOfYear(), now()),
            'goals_on_track' => $this->getGoalsOnTrack($employees, $year)
        ];
    }

    private function getCurrentMetrics(Employee $employee): array
    {
        return $employee->performanceMetrics()
            ->where('period', '>=', now()->startOfMonth())
            ->get()
            ->groupBy('metric_type')
            ->map(function ($metrics) {
                $latest = $metrics->sortByDesc('period')->first();
                return [
                    'value' => $latest->value,
                    'target' => $latest->target,
                    'achievement_rate' => $latest->target > 0 ? round(($latest->value / $latest->target) * 100, 1) : 0,
                    'last_updated' => $latest->updated_at->format('Y-m-d')
                ];
            })
            ->toArray();
    }

    private function getEmployeeGoals(Employee $employee, int $year): Collection
    {
        return $employee->performanceGoals()
            ->whereYear('target_date', $year)
            ->with(['assignedBy'])
            ->orderBy('target_date')
            ->get();
    }

    private function getEmployeeReviews(Employee $employee, int $limit): Collection
    {
        return $employee->performanceReviews()
            ->with(['reviewer'])
            ->orderByDesc('review_period_start')
            ->limit($limit)
            ->get();
    }

    private function calculateAchievementRate(Collection $metrics): float
    {
        $achieved = $metrics->filter(function ($metric) {
            return $metric->target > 0 && $metric->value >= $metric->target;
        })->count();

        return $metrics->count() > 0 ? round(($achieved / $metrics->count()) * 100, 1) : 0;
    }

    private function calculateTrend(Collection $metrics): string
    {
        if ($metrics->count() < 2) {
            return 'stable';
        }

        $sorted = $metrics->sortBy('period');
        $first = $sorted->first()->value;
        $last = $sorted->last()->value;

        if ($last > $first * 1.05) {
            return 'up';
        } elseif ($last < $first * 0.95) {
            return 'down';
        }

        return 'stable';
    }
}
```

Create `app/Services/PerformanceReviewService.php`:

```php
<?php

namespace App\Services;

use App\Models\PerformanceReview;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class PerformanceReviewService
{
    /**
     * Get performance reviews with filters
     */
    public function getReviews(array $filters): LengthAwarePaginator
    {
        $query = PerformanceReview::with(['employee', 'reviewer', 'goals'])
            ->orderByDesc('created_at');

        if (!empty($filters['employee_id'])) {
            $query->where('employee_id', $filters['employee_id']);
        }

        if (!empty($filters['reviewer_id'])) {
            $query->where('reviewer_id', $filters['reviewer_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['review_type'])) {
            $query->where('review_type', $filters['review_type']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('review_period_start', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('review_period_end', '<=', $filters['date_to']);
        }

        return $query->paginate(15);
    }

    /**
     * Create new performance review
     */
    public function createReview(array $data): PerformanceReview
    {
        $review = PerformanceReview::create([
            'employee_id' => $data['employee_id'],
            'reviewer_id' => auth()->id(),
            'review_type' => $data['review_type'],
            'review_period_start' => $data['review_period_start'],
            'review_period_end' => $data['review_period_end'],
            'status' => 'draft',
            'overall_rating' => $data['overall_rating'] ?? null,
            'strengths' => $data['strengths'] ?? null,
            'areas_for_improvement' => $data['areas_for_improvement'] ?? null,
            'goals_achieved' => $data['goals_achieved'] ?? null,
            'new_goals' => $data['new_goals'] ?? null,
            'development_plan' => $data['development_plan'] ?? null,
            'reviewer_comments' => $data['reviewer_comments'] ?? null,
            'employee_comments' => $data['employee_comments'] ?? null
        ]);

        // Create associated goals if provided
        if (!empty($data['goals'])) {
            $this->createReviewGoals($review, $data['goals']);
        }

        return $review->load(['employee', 'reviewer', 'goals']);
    }

    /**
     * Get review details with related data
     */
    public function getReviewDetails(PerformanceReview $review): PerformanceReview
    {
        return $review->load([
            'employee.department',
            'employee.position',
            'reviewer',
            'goals',
            'employee.performanceMetrics' => function ($query) use ($review) {
                $query->whereBetween('period', [
                    $review->review_period_start,
                    $review->review_period_end
                ]);
            }
        ]);
    }

    /**
     * Update performance review
     */
    public function updateReview(PerformanceReview $review, array $data): PerformanceReview
    {
        // Only allow updates if review is in draft or pending status
        if (!in_array($review->status, ['draft', 'pending'])) {
            throw new \Exception('Review tidak dapat diubah karena sudah dalam proses atau selesai');
        }

        $review->update([
            'overall_rating' => $data['overall_rating'] ?? $review->overall_rating,
            'strengths' => $data['strengths'] ?? $review->strengths,
            'areas_for_improvement' => $data['areas_for_improvement'] ?? $review->areas_for_improvement,
            'goals_achieved' => $data['goals_achieved'] ?? $review->goals_achieved,
            'new_goals' => $data['new_goals'] ?? $review->new_goals,
            'development_plan' => $data['development_plan'] ?? $review->development_plan,
            'reviewer_comments' => $data['reviewer_comments'] ?? $review->reviewer_comments,
            'employee_comments' => $data['employee_comments'] ?? $review->employee_comments
        ]);

        // Update goals if provided
        if (isset($data['goals'])) {
            $review->goals()->delete();
            $this->createReviewGoals($review, $data['goals']);
        }

        return $review->load(['employee', 'reviewer', 'goals']);
    }

    /**
     * Submit review for approval
     */
    public function submitReview(PerformanceReview $review): PerformanceReview
    {
        if ($review->status !== 'draft') {
            throw new \Exception('Hanya review dengan status draft yang dapat disubmit');
        }

        $review->update([
            'status' => 'pending',
            'submitted_at' => now()
        ]);

        // Send notification to HR or manager
        // $this->sendReviewSubmissionNotification($review);

        return $review->load(['employee', 'reviewer']);
    }

    /**
     * Approve performance review
     */
    public function approveReview(PerformanceReview $review, ?string $approvalNotes = null): PerformanceReview
    {
        if (!in_array($review->status, ['pending', 'in_review'])) {
            throw new \Exception('Review tidak dapat disetujui dengan status saat ini');
        }

        $review->update([
            'status' => 'completed',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
            'approval_notes' => $approvalNotes
        ]);

        // Update employee performance metrics based on review
        $this->updateEmployeeMetricsFromReview($review);

        return $review->load(['employee', 'reviewer']);
    }

    /**
     * Reject performance review
     */
    public function rejectReview(PerformanceReview $review, string $rejectionReason): PerformanceReview
    {
        if (!in_array($review->status, ['pending', 'in_review'])) {
            throw new \Exception('Review tidak dapat ditolak dengan status saat ini');
        }

        $review->update([
            'status' => 'draft',
            'rejection_reason' => $rejectionReason,
            'rejected_at' => now(),
            'rejected_by' => auth()->id()
        ]);

        return $review->load(['employee', 'reviewer']);
    }

    /**
     * Get review templates
     */
    public function getReviewTemplates(): array
    {
        return [
            'annual' => [
                'name' => 'Review Tahunan',
                'sections' => [
                    'overall_performance' => 'Kinerja Keseluruhan',
                    'job_knowledge' => 'Pengetahuan Pekerjaan',
                    'quality_of_work' => 'Kualitas Kerja',
                    'productivity' => 'Produktivitas',
                    'communication' => 'Komunikasi',
                    'teamwork' => 'Kerjasama Tim',
                    'leadership' => 'Kepemimpinan',
                    'professional_development' => 'Pengembangan Profesional'
                ],
                'rating_scale' => [
                    1 => 'Sangat Kurang',
                    2 => 'Kurang',
                    3 => 'Cukup',
                    4 => 'Baik',
                    5 => 'Sangat Baik'
                ]
            ],
            'quarterly' => [
                'name' => 'Review Kuartalan',
                'sections' => [
                    'goal_achievement' => 'Pencapaian Target',
                    'quality_metrics' => 'Metrik Kualitas',
                    'patient_satisfaction' => 'Kepuasan Pasien',
                    'team_collaboration' => 'Kolaborasi Tim',
                    'continuous_improvement' => 'Perbaikan Berkelanjutan'
                ],
                'rating_scale' => [
                    1 => 'Di Bawah Ekspektasi',
                    2 => 'Memenuhi Sebagian Ekspektasi',
                    3 => 'Memenuhi Ekspektasi',
                    4 => 'Melebihi Ekspektasi',
                    5 => 'Jauh Melebihi Ekspektasi'
                ]
            ],
            'probation' => [
                'name' => 'Review Masa Percobaan',
                'sections' => [
                    'job_adaptation' => 'Adaptasi Pekerjaan',
                    'skill_development' => 'Pengembangan Keterampilan',
                    'policy_compliance' => 'Kepatuhan Kebijakan',
                    'attendance_punctuality' => 'Kehadiran dan Ketepatan Waktu',
                    'integration' => 'Integrasi dengan Tim'
                ],
                'rating_scale' => [
                    1 => 'Tidak Memenuhi Standar',
                    2 => 'Perlu Perbaikan',
                    3 => 'Memenuhi Standar',
                    4 => 'Melebihi Standar',
                    5 => 'Luar Biasa'
                ]
            ]
        ];
    }

    /**
     * Create review goals
     */
    private function createReviewGoals(PerformanceReview $review, array $goals): void
    {
        foreach ($goals as $goalData) {
            $review->goals()->create([
                'employee_id' => $review->employee_id,
                'title' => $goalData['title'],
                'description' => $goalData['description'],
                'target_date' => $goalData['target_date'],
                'priority' => $goalData['priority'] ?? 'medium',
                'status' => 'active',
                'assigned_by' => auth()->id()
            ]);
        }
    }

    /**
     * Update employee metrics from completed review
     */
    private function updateEmployeeMetricsFromReview(PerformanceReview $review): void
    {
        if ($review->overall_rating) {
            // Create or update performance metric based on review
            $review->employee->performanceMetrics()->updateOrCreate(
                [
                    'metric_type' => 'overall_performance',
                    'period' => $review->review_period_end
                ],
                [
                    'value' => $review->overall_rating,
                    'target' => 4.0, // Target rating
                    'notes' => 'Dari review kinerja: ' . $review->review_type,
                    'updated_by' => auth()->id()
                ]
            );
        }
    }
}
```

### 3.3 Create Request Validation Classes

```bash
php artisan make:request StorePerformanceReviewRequest
php artisan make:request UpdatePerformanceReviewRequest
php artisan make:request PerformanceRequest
```

Create `app/Http/Requests/StorePerformanceReviewRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePerformanceReviewRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'employee_id' => 'required|exists:employees,id',
            'review_type' => 'required|in:annual,quarterly,monthly,probation,special',
            'review_period_start' => 'required|date',
            'review_period_end' => 'required|date|after:review_period_start',
            'overall_rating' => 'nullable|numeric|min:1|max:5',
            'strengths' => 'nullable|string|max:2000',
            'areas_for_improvement' => 'nullable|string|max:2000',
            'goals_achieved' => 'nullable|string|max:2000',
            'new_goals' => 'nullable|string|max:2000',
            'development_plan' => 'nullable|string|max:2000',
            'reviewer_comments' => 'nullable|string|max:2000',
            'employee_comments' => 'nullable|string|max:2000',
            'goals' => 'nullable|array',
            'goals.*.title' => 'required_with:goals|string|max:255',
            'goals.*.description' => 'required_with:goals|string|max:1000',
            'goals.*.target_date' => 'required_with:goals|date|after:today',
            'goals.*.priority' => 'nullable|in:low,medium,high'
        ];
    }

    public function messages(): array
    {
        return [
            'employee_id.required' => 'Karyawan harus dipilih',
            'employee_id.exists' => 'Karyawan tidak ditemukan',
            'review_type.required' => 'Tipe review harus dipilih',
            'review_type.in' => 'Tipe review tidak valid',
            'review_period_start.required' => 'Tanggal mulai periode review harus diisi',
            'review_period_start.date' => 'Format tanggal mulai tidak valid',
            'review_period_end.required' => 'Tanggal akhir periode review harus diisi',
            'review_period_end.date' => 'Format tanggal akhir tidak valid',
            'review_period_end.after' => 'Tanggal akhir harus setelah tanggal mulai',
            'overall_rating.numeric' => 'Rating harus berupa angka',
            'overall_rating.min' => 'Rating minimal adalah 1',
            'overall_rating.max' => 'Rating maksimal adalah 5',
            'strengths.max' => 'Kekuatan maksimal 2000 karakter',
            'areas_for_improvement.max' => 'Area perbaikan maksimal 2000 karakter',
            'goals_achieved.max' => 'Pencapaian tujuan maksimal 2000 karakter',
            'new_goals.max' => 'Tujuan baru maksimal 2000 karakter',
            'development_plan.max' => 'Rencana pengembangan maksimal 2000 karakter',
            'reviewer_comments.max' => 'Komentar reviewer maksimal 2000 karakter',
            'employee_comments.max' => 'Komentar karyawan maksimal 2000 karakter',
            'goals.array' => 'Format tujuan tidak valid',
            'goals.*.title.required_with' => 'Judul tujuan harus diisi',
            'goals.*.title.max' => 'Judul tujuan maksimal 255 karakter',
            'goals.*.description.required_with' => 'Deskripsi tujuan harus diisi',
            'goals.*.description.max' => 'Deskripsi tujuan maksimal 1000 karakter',
            'goals.*.target_date.required_with' => 'Tanggal target harus diisi',
            'goals.*.target_date.date' => 'Format tanggal target tidak valid',
            'goals.*.target_date.after' => 'Tanggal target harus di masa depan',
            'goals.*.priority.in' => 'Prioritas tidak valid'
        ];
    }
}
```

---

## Step 4: Create React Performance Components

### 4.1 Create Performance Dashboard

Create `resources/js/Pages/Performance/Index.tsx`:

```tsx
import React, { useState, useEffect } from 'react';
import { Head, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import {
  BarChart3,
  TrendingUp,
  Users,
  Target,
  Award,
  AlertTriangle,
  Calendar,
  Filter,
  Plus,
  Eye
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { formatDistanceToNow } from 'date-fns';
import { id } from 'date-fns/locale';

interface DashboardData {
  overview: {
    total_employees: number;
    reviewed_employees: number;
    review_completion_rate: number;
    average_rating: number;
    goals_on_track: number;
  };
  kpi_summary: Record<string, {
    label: string;
    average: number;
    target: number;
    achievement_rate: number;
    trend: 'up' | 'down' | 'stable';
    count: number;
  }>;
  recent_reviews: Array<{
    id: number;
    employee: {
      name: string;
      employee_number: string;
    };
    review_type: string;
    overall_rating: number;
    status: string;
    created_at: string;
  }>;
  top_performers: Array<{
    employee: {
      name: string;
      department: string;
    };
    average_rating: number;
    achievements: number;
  }>;
  improvement_needed: Array<{
    employee: {
      name: string;
      department: string;
    };
    areas: string[];
    last_review_rating: number;
  }>;
}

interface Props {
  dashboardData: DashboardData;
  departments: Array<{
    id: number;
    name: string;
  }>;
  filters: {
    department_id?: number;
    period?: string;
    year?: number;
  };
}

export default function PerformanceIndex({ dashboardData, departments, filters }: Props) {
  const [selectedDepartment, setSelectedDepartment] = useState(filters.department_id?.toString() || '');
  const [selectedPeriod, setSelectedPeriod] = useState(filters.period || 'month');
  const [selectedYear, setSelectedYear] = useState(filters.year?.toString() || new Date().getFullYear().toString());

  const handleFilterChange = () => {
    router.get(route('performance.index'), {
      department_id: selectedDepartment,
      period: selectedPeriod,
      year: selectedYear
    }, {
      preserveState: true,
      preserveScroll: true
    });
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />;
      default:
        return <TrendingUp className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      in_review: 'bg-blue-100 text-blue-800',
      draft: 'bg-gray-100 text-gray-800'
    };

    const labels = {
      completed: 'Selesai',
      pending: 'Menunggu',
      in_review: 'Sedang Review',
      draft: 'Draft'
    };

    return (
      <Badge className={variants[status as keyof typeof variants]}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 3.5) return 'text-blue-600';
    if (rating >= 2.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            Dashboard Kinerja
          </h2>
          <Button onClick={() => router.get(route('performance.reviews.create'))}>
            <Plus className="h-4 w-4 mr-2" />
            Buat Review
          </Button>
        </div>
      }
    >
      <Head title="Dashboard Kinerja" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filter Dashboard
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                  <SelectTrigger>
                    <SelectValue placeholder="Semua Departemen" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Departemen</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id.toString()}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="month">Bulanan</SelectItem>
                    <SelectItem value="quarter">Kuartalan</SelectItem>
                    <SelectItem value="year">Tahunan</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedYear} onValueChange={setSelectedYear}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button onClick={handleFilterChange} variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Terapkan
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Karyawan</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.total_employees}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData.overview.reviewed_employees} sudah direview
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tingkat Review</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.review_completion_rate}%</div>
                <p className="text-xs text-muted-foreground">
                  Dari target review periode ini
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rating Rata-rata</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getRatingColor(dashboardData.overview.average_rating)}`}>
                  {dashboardData.overview.average_rating.toFixed(1)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Dari skala 1-5
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Target On Track</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{dashboardData.overview.goals_on_track}</div>
                <p className="text-xs text-muted-foreground">
                  Tujuan yang tercapai
                </p>
              </CardContent>
            </Card>
          </div>

          {/* KPI Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Ringkasan KPI</CardTitle>
              <CardDescription>
                Key Performance Indicators berdasarkan filter yang dipilih
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(dashboardData.kpi_summary).map(([key, kpi]) => (
                  <div key={key} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{kpi.label}</h4>
                      {getTrendIcon(kpi.trend)}
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Rata-rata:</span>
                        <span className="font-medium">{kpi.average}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Target:</span>
                        <span>{kpi.target}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Pencapaian:</span>
                        <span className={kpi.achievement_rate >= 80 ? 'text-green-600' : 'text-red-600'}>
                          {kpi.achievement_rate}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Reviews */}
            <Card>
              <CardHeader>
                <CardTitle>Review Terbaru</CardTitle>
                <CardDescription>
                  Review kinerja yang baru saja dibuat atau diperbarui
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.recent_reviews.map((review) => (
                    <div key={review.id} className="flex items-center justify-between border-b pb-2">
                      <div>
                        <p className="font-medium">{review.employee.name}</p>
                        <p className="text-sm text-gray-500">
                          {review.employee.employee_number} • {review.review_type}
                        </p>
                        <p className="text-xs text-gray-400">
                          {formatDistanceToNow(new Date(review.created_at), {
                            addSuffix: true,
                            locale: id
                          })}
                        </p>
                      </div>
                      <div className="text-right">
                        {getStatusBadge(review.status)}
                        {review.overall_rating && (
                          <p className={`text-sm font-medium ${getRatingColor(review.overall_rating)}`}>
                            Rating: {review.overall_rating}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle>Performa Terbaik</CardTitle>
                <CardDescription>
                  Karyawan dengan kinerja terbaik periode ini
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.top_performers.map((performer, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                          <Award className="h-4 w-4 text-yellow-600" />
                        </div>
                        <div>
                          <p className="font-medium">{performer.employee.name}</p>
                          <p className="text-sm text-gray-500">{performer.employee.department}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-medium ${getRatingColor(performer.average_rating)}`}>
                          {performer.average_rating.toFixed(1)}
                        </p>
                        <p className="text-xs text-gray-500">
                          {performer.achievements} pencapaian
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Improvement Needed */}
          {dashboardData.improvement_needed.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Perlu Perbaikan
                </CardTitle>
                <CardDescription>
                  Karyawan yang memerlukan perhatian dan pengembangan lebih lanjut
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.improvement_needed.map((employee, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium">{employee.employee.name}</p>
                          <p className="text-sm text-gray-500">{employee.employee.department}</p>
                        </div>
                        <span className={`text-sm font-medium ${getRatingColor(employee.last_review_rating)}`}>
                          Rating: {employee.last_review_rating}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Area yang perlu diperbaiki:</p>
                        <div className="flex flex-wrap gap-1">
                          {employee.areas.map((area, areaIndex) => (
                            <Badge key={areaIndex} variant="outline" className="text-xs">
                              {area}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

### 4.2 Create Performance Review Interface

Create `resources/js/Pages/Performance/Reviews/Index.tsx`:

```tsx
import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';
import {
  FileText,
  Plus,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Filter,
  Search,
  Calendar,
  User,
  Star,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { formatDistanceToNow } from 'date-fns';
import { id } from 'date-fns/locale';

interface PerformanceReview {
  id: number;
  employee: {
    id: number;
    name: string;
    employee_number: string;
    department: {
      name: string;
    };
  };
  reviewer: {
    name: string;
  };
  review_type: string;
  status: string;
  overall_rating?: number;
  review_period_start: string;
  review_period_end: string;
  created_at: string;
  submitted_at?: string;
  approved_at?: string;
}

interface Props {
  reviews: {
    data: PerformanceReview[];
    links: any[];
    meta: any;
  };
  filters: {
    search?: string;
    status?: string;
    review_type?: string;
    employee_id?: number;
  };
  employees: Array<{
    id: number;
    name: string;
  }>;
}

export default function ReviewsIndex({ reviews, filters, employees }: Props) {
  const [search, setSearch] = useState(filters.search || '');
  const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
  const [selectedType, setSelectedType] = useState(filters.review_type || '');
  const [selectedEmployee, setSelectedEmployee] = useState(filters.employee_id?.toString() || '');

  const handleSearch = () => {
    router.get(route('performance.reviews.index'), {
      search,
      status: selectedStatus,
      review_type: selectedType,
      employee_id: selectedEmployee
    }, {
      preserveState: true,
      preserveScroll: true
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'in_review':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'draft':
        return <FileText className="h-4 w-4 text-gray-500" />;
      default:
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      in_review: 'bg-blue-100 text-blue-800',
      draft: 'bg-gray-100 text-gray-800',
      cancelled: 'bg-red-100 text-red-800'
    };

    const labels = {
      completed: 'Selesai',
      pending: 'Menunggu Persetujuan',
      in_review: 'Sedang Review',
      draft: 'Draft',
      cancelled: 'Dibatalkan'
    };

    return (
      <Badge className={variants[status as keyof typeof variants]}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  const getReviewTypeLabel = (type: string) => {
    const labels = {
      annual: 'Tahunan',
      quarterly: 'Kuartalan',
      monthly: 'Bulanan',
      probation: 'Masa Percobaan',
      special: 'Khusus'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getRatingStars = (rating?: number) => {
    if (!rating) return null;

    return (
      <div className="flex items-center gap-1">
        {Array.from({ length: 5 }, (_, i) => (
          <Star
            key={i}
            className={`h-3 w-3 ${
              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-xs text-gray-600 ml-1">{rating.toFixed(1)}</span>
      </div>
    );
  };

  const handleDelete = (reviewId: number) => {
    if (confirm('Apakah Anda yakin ingin menghapus review ini?')) {
      router.delete(route('performance.reviews.destroy', reviewId));
    }
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            Review Kinerja
          </h2>
          <Button asChild>
            <Link href={route('performance.reviews.create')}>
              <Plus className="h-4 w-4 mr-2" />
              Buat Review Baru
            </Link>
          </Button>
        </div>
      }
    >
      <Head title="Review Kinerja" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {/* Search and Filters */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filter Review
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cari karyawan..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>

                <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih Karyawan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Karyawan</SelectItem>
                    {employees.map((employee) => (
                      <SelectItem key={employee.id} value={employee.id.toString()}>
                        {employee.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Status</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending">Menunggu</SelectItem>
                    <SelectItem value="in_review">Sedang Review</SelectItem>
                    <SelectItem value="completed">Selesai</SelectItem>
                    <SelectItem value="cancelled">Dibatalkan</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Tipe Review" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Tipe</SelectItem>
                    <SelectItem value="annual">Tahunan</SelectItem>
                    <SelectItem value="quarterly">Kuartalan</SelectItem>
                    <SelectItem value="monthly">Bulanan</SelectItem>
                    <SelectItem value="probation">Masa Percobaan</SelectItem>
                    <SelectItem value="special">Khusus</SelectItem>
                  </SelectContent>
                </Select>

                <Button onClick={handleSearch} variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Terapkan
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Reviews List */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Review Kinerja</CardTitle>
              <CardDescription>
                Kelola dan pantau review kinerja karyawan
              </CardDescription>
            </CardHeader>
            <CardContent>
              {reviews.data.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada review kinerja</p>
                  <p className="text-sm text-gray-400 mb-4">
                    Buat review pertama dengan mengklik tombol "Buat Review Baru"
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reviews.data.map((review) => (
                    <div
                      key={review.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4 flex-1">
                          <div className="flex-shrink-0">
                            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                              <User className="h-6 w-6 text-blue-600" />
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium text-gray-900">
                                {review.employee.name}
                              </h3>
                              {getStatusBadge(review.status)}
                              <Badge variant="outline">
                                {getReviewTypeLabel(review.review_type)}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                              <span>{review.employee.employee_number}</span>
                              <span>{review.employee.department.name}</span>
                              <span>Reviewer: {review.reviewer.name}</span>
                            </div>

                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                {new Date(review.review_period_start).toLocaleDateString('id-ID')} -
                                {new Date(review.review_period_end).toLocaleDateString('id-ID')}
                              </span>
                              <span>
                                Dibuat {formatDistanceToNow(new Date(review.created_at), {
                                  addSuffix: true,
                                  locale: id
                                })}
                              </span>
                            </div>

                            {review.overall_rating && (
                              <div className="mt-2">
                                {getRatingStars(review.overall_rating)}
                              </div>
                            )}
                          </div>
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={route('performance.reviews.show', review.id)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Lihat Detail
                              </Link>
                            </DropdownMenuItem>

                            {review.status === 'draft' && (
                              <DropdownMenuItem asChild>
                                <Link href={route('performance.reviews.edit', review.id)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </Link>
                              </DropdownMenuItem>
                            )}

                            {review.status === 'draft' && (
                              <DropdownMenuItem
                                onClick={() => handleDelete(review.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Hapus
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Pagination */}
              {reviews.meta.last_page > 1 && (
                <div className="mt-6 flex justify-center">
                  <div className="flex items-center gap-2">
                    {reviews.links.map((link, index) => (
                      <Button
                        key={index}
                        variant={link.active ? "default" : "outline"}
                        size="sm"
                        disabled={!link.url}
                        onClick={() => link.url && router.get(link.url)}
                        dangerouslySetInnerHTML={{ __html: link.label }}
                      />
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

---

## Step 5: Create KPI Tracking and Testing

### 5.1 Create KPI Tracking Component

Create `resources/js/Pages/Performance/KPI/Index.tsx`:

```tsx
import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/Components/ui/dialog';
import {
  BarChart3,
  TrendingUp,
  Target,
  Plus,
  Edit,
  Calendar,
  Filter,
  Save
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';

interface KPIData {
  employee: {
    id: number;
    name: string;
    employee_number: string;
    department: {
      name: string;
    };
  };
  metrics: Record<string, {
    label: string;
    value: number;
    target: number;
    achievement_rate: number;
    trend: 'up' | 'down' | 'stable';
    last_updated: string;
  }>;
}

interface Props {
  kpiData: KPIData[];
  kpiTypes: Record<string, {
    label: string;
    description: string;
    unit: string;
    target_range: {
      min: number;
      max: number;
    };
  }>;
  employees: Array<{
    id: number;
    name: string;
    department: string;
  }>;
  filters: {
    employee_id?: number;
    department_id?: number;
    metric_type?: string;
  };
}

export default function KPIIndex({ kpiData, kpiTypes, employees, filters }: Props) {
  const [selectedEmployee, setSelectedEmployee] = useState(filters.employee_id?.toString() || '');
  const [selectedMetric, setSelectedMetric] = useState(filters.metric_type || '');
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [updateForm, setUpdateForm] = useState({
    employee_id: '',
    metric_type: '',
    value: '',
    target: '',
    period: new Date().toISOString().split('T')[0],
    notes: ''
  });

  const handleFilter = () => {
    router.get(route('performance.kpi.index'), {
      employee_id: selectedEmployee,
      metric_type: selectedMetric
    }, {
      preserveState: true,
      preserveScroll: true
    });
  };

  const handleUpdateKPI = (e: React.FormEvent) => {
    e.preventDefault();

    router.post('/api/performance/kpi', updateForm, {
      onSuccess: () => {
        setIsUpdateDialogOpen(false);
        setUpdateForm({
          employee_id: '',
          metric_type: '',
          value: '',
          target: '',
          period: new Date().toISOString().split('T')[0],
          notes: ''
        });
      }
    });
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />;
      default:
        return <TrendingUp className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAchievementColor = (rate: number) => {
    if (rate >= 100) return 'text-green-600';
    if (rate >= 80) return 'text-blue-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAchievementBadge = (rate: number) => {
    if (rate >= 100) return <Badge className="bg-green-100 text-green-800">Tercapai</Badge>;
    if (rate >= 80) return <Badge className="bg-blue-100 text-blue-800">Baik</Badge>;
    if (rate >= 60) return <Badge className="bg-yellow-100 text-yellow-800">Cukup</Badge>;
    return <Badge className="bg-red-100 text-red-800">Perlu Perbaikan</Badge>;
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            KPI Tracking
          </h2>
          <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Update KPI
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Update KPI</DialogTitle>
                <DialogDescription>
                  Perbarui nilai KPI untuk karyawan
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleUpdateKPI} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="employee">Karyawan</Label>
                  <Select
                    value={updateForm.employee_id}
                    onValueChange={(value) => setUpdateForm(prev => ({ ...prev, employee_id: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih karyawan" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id.toString()}>
                          {employee.name} - {employee.department}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metric_type">Tipe Metrik</Label>
                  <Select
                    value={updateForm.metric_type}
                    onValueChange={(value) => setUpdateForm(prev => ({ ...prev, metric_type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih metrik" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(kpiTypes).map(([key, type]) => (
                        <SelectItem key={key} value={key}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="value">Nilai Aktual</Label>
                    <Input
                      id="value"
                      type="number"
                      step="0.01"
                      value={updateForm.value}
                      onChange={(e) => setUpdateForm(prev => ({ ...prev, value: e.target.value }))}
                      placeholder="0.00"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="target">Target</Label>
                    <Input
                      id="target"
                      type="number"
                      step="0.01"
                      value={updateForm.target}
                      onChange={(e) => setUpdateForm(prev => ({ ...prev, target: e.target.value }))}
                      placeholder="0.00"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="period">Periode</Label>
                  <Input
                    id="period"
                    type="date"
                    value={updateForm.period}
                    onChange={(e) => setUpdateForm(prev => ({ ...prev, period: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Catatan</Label>
                  <Textarea
                    id="notes"
                    value={updateForm.notes}
                    onChange={(e) => setUpdateForm(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Catatan tambahan..."
                    rows={3}
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
                    Batal
                  </Button>
                  <Button type="submit">
                    <Save className="h-4 w-4 mr-2" />
                    Simpan
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      }
    >
      <Head title="KPI Tracking" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filter KPI
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                  <SelectTrigger>
                    <SelectValue placeholder="Semua Karyawan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Karyawan</SelectItem>
                    {employees.map((employee) => (
                      <SelectItem key={employee.id} value={employee.id.toString()}>
                        {employee.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                  <SelectTrigger>
                    <SelectValue placeholder="Semua Metrik" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Metrik</SelectItem>
                    {Object.entries(kpiTypes).map(([key, type]) => (
                      <SelectItem key={key} value={key}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button onClick={handleFilter} variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Terapkan Filter
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* KPI Data */}
          <div className="space-y-6">
            {kpiData.map((employee) => (
              <Card key={employee.employee.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">{employee.employee.name}</h3>
                      <p className="text-sm text-gray-500">
                        {employee.employee.employee_number} • {employee.employee.department.name}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setUpdateForm(prev => ({
                          ...prev,
                          employee_id: employee.employee.id.toString()
                        }));
                        setIsUpdateDialogOpen(true);
                      }}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Update KPI
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(employee.metrics).map(([key, metric]) => (
                      <div key={key} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm">{metric.label}</h4>
                          {getTrendIcon(metric.trend)}
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-2xl font-bold">{metric.value}</span>
                            {getAchievementBadge(metric.achievement_rate)}
                          </div>

                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Target:</span>
                              <span>{metric.target}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Pencapaian:</span>
                              <span className={getAchievementColor(metric.achievement_rate)}>
                                {metric.achievement_rate.toFixed(1)}%
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Update:</span>
                              <span className="text-xs">
                                {new Date(metric.last_updated).toLocaleDateString('id-ID')}
                              </span>
                            </div>
                          </div>

                          {/* Progress Bar */}
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                metric.achievement_rate >= 100 ? 'bg-green-500' :
                                metric.achievement_rate >= 80 ? 'bg-blue-500' :
                                metric.achievement_rate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${Math.min(metric.achievement_rate, 100)}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {kpiData.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Belum ada data KPI</p>
                <p className="text-sm text-gray-400 mb-4">
                  Mulai dengan menambahkan data KPI untuk karyawan
                </p>
                <Button onClick={() => setIsUpdateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah KPI Pertama
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

### 5.2 Create Feature Tests

Create `tests/Feature/PerformanceTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\PerformanceReview;
use App\Models\PerformanceMetric;
use App\Services\PerformanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected PerformanceService $performanceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->performanceService = app(PerformanceService::class);
    }

    public function test_can_get_performance_dashboard_data()
    {
        $department = Department::factory()->create();
        $employee = Employee::factory()->create(['department_id' => $department->id]);

        PerformanceMetric::factory()->create([
            'employee_id' => $employee->id,
            'metric_type' => 'patient_satisfaction',
            'value' => 4.5,
            'target' => 4.0
        ]);

        $data = $this->performanceService->getDashboardData($department->id);

        $this->assertArrayHasKey('overview', $data);
        $this->assertArrayHasKey('kpi_summary', $data);
        $this->assertArrayHasKey('recent_reviews', $data);
    }

    public function test_performance_dashboard_api()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/performance/dashboard');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'overview',
                        'kpi_summary',
                        'recent_reviews'
                    ]
                ]);
    }

    public function test_can_update_kpi()
    {
        $employee = Employee::factory()->create();

        $response = $this->actingAs($this->user)
            ->postJson('/api/performance/kpi', [
                'employee_id' => $employee->id,
                'metric_type' => 'quality_score',
                'value' => 4.2,
                'target' => 4.0,
                'period' => now()->format('Y-m-d'),
                'notes' => 'Excellent performance this month'
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'message'
                ]);

        $this->assertDatabaseHas('performance_metrics', [
            'employee_id' => $employee->id,
            'metric_type' => 'quality_score',
            'value' => 4.2
        ]);
    }

    public function test_can_access_performance_pages()
    {
        $response = $this->actingAs($this->user)
            ->get('/performance');

        $response->assertStatus(200);
    }

    public function test_can_access_kpi_tracking_page()
    {
        $response = $this->actingAs($this->user)
            ->get('/performance/kpi');

        $response->assertStatus(200);
    }
}
```

Create `tests/Feature/PerformanceReviewTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use App\Models\PerformanceReview;
use App\Services\PerformanceReviewService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PerformanceReviewTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected PerformanceReviewService $reviewService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->reviewService = app(PerformanceReviewService::class);
    }

    public function test_can_create_performance_review()
    {
        $employee = Employee::factory()->create();

        $reviewData = [
            'employee_id' => $employee->id,
            'review_type' => 'quarterly',
            'review_period_start' => now()->startOfQuarter()->format('Y-m-d'),
            'review_period_end' => now()->endOfQuarter()->format('Y-m-d'),
            'overall_rating' => 4.0,
            'strengths' => 'Excellent communication skills',
            'areas_for_improvement' => 'Time management could be improved'
        ];

        $review = $this->reviewService->createReview($reviewData);

        $this->assertInstanceOf(PerformanceReview::class, $review);
        $this->assertEquals('draft', $review->status);
        $this->assertEquals($employee->id, $review->employee_id);
    }

    public function test_can_submit_review_for_approval()
    {
        $employee = Employee::factory()->create();
        $review = PerformanceReview::factory()->create([
            'employee_id' => $employee->id,
            'status' => 'draft'
        ]);

        $submittedReview = $this->reviewService->submitReview($review);

        $this->assertEquals('pending', $submittedReview->status);
        $this->assertNotNull($submittedReview->submitted_at);
    }

    public function test_can_approve_review()
    {
        $employee = Employee::factory()->create();
        $review = PerformanceReview::factory()->create([
            'employee_id' => $employee->id,
            'status' => 'pending'
        ]);

        $approvedReview = $this->reviewService->approveReview($review, 'Good review');

        $this->assertEquals('completed', $approvedReview->status);
        $this->assertNotNull($approvedReview->approved_at);
        $this->assertEquals('Good review', $approvedReview->approval_notes);
    }

    public function test_review_api_endpoints()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/performance/reviews');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }

    public function test_can_create_review_via_api()
    {
        $employee = Employee::factory()->create();

        $response = $this->actingAs($this->user)
            ->postJson('/api/performance/reviews', [
                'employee_id' => $employee->id,
                'review_type' => 'annual',
                'review_period_start' => now()->startOfYear()->format('Y-m-d'),
                'review_period_end' => now()->endOfYear()->format('Y-m-d'),
                'overall_rating' => 4.5
            ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'employee',
                        'status'
                    ],
                    'message'
                ]);
    }

    public function test_review_validation()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/performance/reviews', [
                'review_type' => 'invalid_type'
            ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['employee_id', 'review_type']);
    }
}
```

### 5.3 Run Tests and Setup

```bash
# Run migrations
php artisan migrate

# Run performance tests
php artisan test --filter=PerformanceTest
php artisan test --filter=PerformanceReviewTest

# Create routes for performance management
# Add to routes/web.php and routes/api.php
```

---

## Summary

Dalam chapter ini, kita telah berhasil mengimplementasikan sistem performance tracking dan evaluation yang komprehensif untuk Hospital Employee Management System dengan fitur-fitur berikut:

### ✅ **Backend Implementation**

1. **Performance Models** - PerformanceReview, PerformanceGoal, PerformanceMetric, PerformanceEvaluation
2. **Performance Services** - PerformanceService dan PerformanceReviewService dengan business logic
3. **API Controllers** - Performance dan PerformanceReview controllers untuk API endpoints
4. **Request Validation** - Validasi input dengan pesan bahasa Indonesia
5. **Database Migrations** - Schema untuk tracking performance dan reviews

### ✅ **Frontend Implementation**

1. **Performance Dashboard** - Overview kinerja dengan KPI summary dan trends
2. **Review Management** - Interface untuk mengelola performance reviews
3. **KPI Tracking** - Real-time tracking dan update KPI karyawan
4. **Responsive Design** - UI yang mobile-friendly dengan shadcn/ui

### ✅ **Key Features**

- **Performance Dashboard** - Overview kinerja departemen dan individu
- **KPI Tracking** - 7 metrik utama (patient satisfaction, quality score, efficiency, dll)
- **Performance Reviews** - Workflow review dengan approval system
- **Goal Setting** - Target dan tracking pencapaian tujuan
- **Indonesian Healthcare Context** - Standar kinerja sesuai rumah sakit Indonesia
- **Real-time Updates** - Update KPI dan status review secara real-time
- **Analytics & Reporting** - Trend analysis dan performance insights

### ✅ **Testing**

- **Feature Tests** - Test untuk performance service dan review workflow
- **API Testing** - Test untuk semua API endpoints
- **Validation Testing** - Test untuk request validation
- **Integration Testing** - Test untuk Inertia.js pages

### 🎯 **Indonesian Healthcare Context**

- **Healthcare KPIs** - Metrik yang relevan untuk rumah sakit Indonesia
- **Review Templates** - Template review sesuai standar Indonesia
- **Performance Standards** - Standar kinerja yang sesuai dengan regulasi kesehatan
- **Localization** - UI dan terminologi dalam bahasa Indonesia

### 📊 **Performance Metrics Included**

1. **Patient Satisfaction** - Kepuasan Pasien
2. **Quality Score** - Skor Kualitas Pelayanan
3. **Efficiency Rating** - Rating Efisiensi Kerja
4. **Attendance Rate** - Tingkat Kehadiran
5. **Safety Compliance** - Kepatuhan Keselamatan
6. **Teamwork Score** - Skor Kerjasama Tim
7. **Professional Development** - Pengembangan Profesional

Chapter ini memberikan foundation yang kuat untuk sistem manajemen kinerja yang dapat membantu rumah sakit dalam mengevaluasi dan meningkatkan kinerja karyawan secara sistematis.

**Next Steps**: Lanjutkan ke Chapter 12 untuk implementasi Leave Management system, atau kembali untuk review dan enhancement chapter lainnya.
```
