# Chapter 11: Email Notifications

Welcome to Chapter 11! In this chapter, we'll implement a comprehensive email notification system for booking confirmations, reminders, status updates, and automated communications.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Set up email notifications for booking confirmations
- Create automated reminder emails
- Build professional email templates and layouts
- Implement notification preferences
- Add email queue management
- Create scheduled email notifications
- Build email tracking and analytics

## 📋 What We'll Cover

1. Configuring email settings and drivers
2. Creating notification classes
3. Building email templates
4. Implementing booking confirmation emails
5. Creating reminder notifications
6. Adding notification preferences
7. Setting up email queues
8. Testing the notification system

## 🛠 Step 1: Email Configuration

First, let's configure email settings. Update your `.env` file:

```env
# Email Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your_username
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Queue Configuration
QUEUE_CONNECTION=database
```

Configure mail settings in `config/mail.php` if needed, and set up the queue database:

```bash
# Create queue table
php artisan queue:table
php artisan migrate
```

## 🛠 Step 2: Creating Notification Classes

Let's create notification classes for different email types:

```bash
# Create notification classes
php artisan make:notification BookingConfirmation
php artisan make:notification BookingReminder
php artisan make:notification BookingStatusUpdate
php artisan make:notification PaymentConfirmation
php artisan make:notification BookingCancellation
```

Edit `app/Notifications/BookingConfirmation.php`:

```php
<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingConfirmation extends Notification implements ShouldQueue
{
    use Queueable;

    protected $booking;

    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Booking Confirmation - ' . $this->booking->booking_number)
            ->greeting('Hello ' . $this->booking->customer->first_name . '!')
            ->line('Thank you for booking with us. Your booking has been confirmed.')
            ->line('**Booking Details:**')
            ->line('Booking Number: ' . $this->booking->booking_number)
            ->line('Date: ' . $this->booking->formatted_booking_date)
            ->line('Time: ' . $this->booking->formatted_booking_time)
            ->line('Services: ' . $this->booking->services->pluck('name')->join(', '))
            ->line('Total Amount: ' . $this->booking->formatted_total)
            ->line('Vehicle: ' . $this->booking->vehicle_make . ' ' . $this->booking->vehicle_model)
            ->action('View Booking Details', route('bookings.show', $this->booking))
            ->line('We look forward to serving you!')
            ->line('If you have any questions, please don\'t hesitate to contact us.');
    }

    public function toArray($notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'booking_number' => $this->booking->booking_number,
            'message' => 'Booking confirmation sent',
        ];
    }
}
```

Edit `app/Notifications/BookingReminder.php`:

```php
<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingReminder extends Notification implements ShouldQueue
{
    use Queueable;

    protected $booking;
    protected $reminderType;

    public function __construct(Booking $booking, string $reminderType = '24h')
    {
        $this->booking = $booking;
        $this->reminderType = $reminderType;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $timeText = $this->reminderType === '24h' ? 'tomorrow' : 'in a few hours';
        
        return (new MailMessage)
            ->subject('Booking Reminder - ' . $this->booking->booking_number)
            ->greeting('Hello ' . $this->booking->customer->first_name . '!')
            ->line("This is a friendly reminder that you have a car wash appointment {$timeText}.")
            ->line('**Booking Details:**')
            ->line('Booking Number: ' . $this->booking->booking_number)
            ->line('Date: ' . $this->booking->formatted_booking_date)
            ->line('Time: ' . $this->booking->formatted_booking_time)
            ->line('Services: ' . $this->booking->services->pluck('name')->join(', '))
            ->line('Location: ' . config('app.business_address', 'Our Location'))
            ->action('View Booking Details', route('bookings.show', $this->booking))
            ->line('Please arrive 10 minutes early for your appointment.')
            ->line('If you need to reschedule or cancel, please contact us as soon as possible.');
    }

    public function toArray($notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'booking_number' => $this->booking->booking_number,
            'reminder_type' => $this->reminderType,
            'message' => 'Booking reminder sent',
        ];
    }
}
```

Edit `app/Notifications/BookingStatusUpdate.php`:

```php
<?php

namespace App\Notifications;

use App\Models\Booking;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BookingStatusUpdate extends Notification implements ShouldQueue
{
    use Queueable;

    protected $booking;
    protected $oldStatus;
    protected $newStatus;

    public function __construct(Booking $booking, string $oldStatus, string $newStatus)
    {
        $this->booking = $booking;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->subject('Booking Status Update - ' . $this->booking->booking_number)
            ->greeting('Hello ' . $this->booking->customer->first_name . '!');

        switch ($this->newStatus) {
            case 'confirmed':
                $message->line('Great news! Your booking has been confirmed.')
                       ->line('We\'re looking forward to providing you with excellent service.');
                break;
            case 'in_progress':
                $message->line('Your car wash service is now in progress.')
                       ->line('Our team is working hard to make your vehicle shine!');
                break;
            case 'completed':
                $message->line('Your car wash service has been completed!')
                       ->line('Thank you for choosing our services. We hope you\'re satisfied with the results.')
                       ->line('We\'d love to hear your feedback about your experience.');
                break;
            case 'cancelled':
                $message->line('Your booking has been cancelled as requested.')
                       ->line('If this was not intentional, please contact us immediately.')
                       ->line('We hope to serve you again in the future.');
                break;
            default:
                $message->line('Your booking status has been updated to: ' . ucfirst($this->newStatus));
        }

        return $message
            ->line('**Booking Details:**')
            ->line('Booking Number: ' . $this->booking->booking_number)
            ->line('Date: ' . $this->booking->formatted_booking_date)
            ->line('Time: ' . $this->booking->formatted_booking_time)
            ->action('View Booking Details', route('bookings.show', $this->booking))
            ->line('Thank you for your business!');
    }

    public function toArray($notifiable): array
    {
        return [
            'booking_id' => $this->booking->id,
            'booking_number' => $this->booking->booking_number,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'message' => 'Booking status updated',
        ];
    }
}
```

Edit `app/Notifications/PaymentConfirmation.php`:

```php
<?php

namespace App\Notifications;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentConfirmation extends Notification implements ShouldQueue
{
    use Queueable;

    protected $payment;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Payment Confirmation - ' . $this->payment->booking->booking_number)
            ->greeting('Hello ' . $this->payment->customer->first_name . '!')
            ->line('Thank you! Your payment has been successfully processed.')
            ->line('**Payment Details:**')
            ->line('Amount: ' . $this->payment->formatted_amount)
            ->line('Payment Method: ' . $this->payment->payment_method_type)
            ->line('Transaction ID: ' . $this->payment->payment_intent_id)
            ->line('Date: ' . $this->payment->paid_at->format('M j, Y g:i A'))
            ->line('**Booking Details:**')
            ->line('Booking Number: ' . $this->payment->booking->booking_number)
            ->line('Services: ' . $this->payment->booking->services->pluck('name')->join(', '))
            ->action('View Receipt', route('bookings.show', $this->payment->booking))
            ->line('A receipt has been generated for your records.')
            ->line('Thank you for your business!');
    }

    public function toArray($notifiable): array
    {
        return [
            'payment_id' => $this->payment->id,
            'booking_id' => $this->payment->booking_id,
            'amount' => $this->payment->amount,
            'message' => 'Payment confirmation sent',
        ];
    }
}
```

## 🛠 Step 3: Creating Notification Preferences

Let's create a system for users to manage their notification preferences:

```bash
# Create notification preferences migration
php artisan make:migration create_notification_preferences_table
```

Edit the migration:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('notification_type'); // booking_confirmation, booking_reminder, etc.
            $table->boolean('email_enabled')->default(true);
            $table->boolean('sms_enabled')->default(false);
            $table->boolean('push_enabled')->default(true);
            $table->json('settings')->nullable(); // Additional settings like reminder timing
            $table->timestamps();

            $table->unique(['user_id', 'notification_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notification_preferences');
    }
};
```

Create the NotificationPreference model:

```bash
php artisan make:model NotificationPreference
```

Edit `app/Models/NotificationPreference.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'notification_type',
        'email_enabled',
        'sms_enabled',
        'push_enabled',
        'settings',
    ];

    protected $casts = [
        'email_enabled' => 'boolean',
        'sms_enabled' => 'boolean',
        'push_enabled' => 'boolean',
        'settings' => 'array',
    ];

    // Notification type constants
    const BOOKING_CONFIRMATION = 'booking_confirmation';
    const BOOKING_REMINDER = 'booking_reminder';
    const BOOKING_STATUS_UPDATE = 'booking_status_update';
    const PAYMENT_CONFIRMATION = 'payment_confirmation';
    const BOOKING_CANCELLATION = 'booking_cancellation';
    const PROMOTIONAL = 'promotional';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function getNotificationTypes(): array
    {
        return [
            self::BOOKING_CONFIRMATION => 'Booking Confirmations',
            self::BOOKING_REMINDER => 'Booking Reminders',
            self::BOOKING_STATUS_UPDATE => 'Status Updates',
            self::PAYMENT_CONFIRMATION => 'Payment Confirmations',
            self::BOOKING_CANCELLATION => 'Cancellation Notices',
            self::PROMOTIONAL => 'Promotional Emails',
        ];
    }

    public static function getDefaultPreferences(int $userId): array
    {
        $defaults = [];
        foreach (self::getNotificationTypes() as $type => $name) {
            $defaults[] = [
                'user_id' => $userId,
                'notification_type' => $type,
                'email_enabled' => true,
                'sms_enabled' => false,
                'push_enabled' => true,
                'settings' => [],
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        return $defaults;
    }
}
```

Run the migration:

```bash
php artisan migrate
```

## 🧪 Testing the Notification System

1. **Test Email Configuration**:
   - Send test emails
   - Verify email delivery
   - Check email formatting

2. **Test Notification Triggers**:
   - Create bookings and verify confirmations
   - Update booking status and check notifications
   - Process payments and verify confirmations

3. **Test Scheduled Reminders**:
   - Run reminder command manually
   - Verify reminder timing
   - Check reminder content

4. **Test Queue Processing**:
   - Start queue worker: `php artisan queue:work`
   - Monitor queue jobs
   - Verify email delivery

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Set up email notifications for booking confirmations
✅ Created automated reminder emails
✅ Built professional email templates and layouts
✅ Implemented notification preferences
✅ Added email queue management
✅ Created scheduled email notifications
✅ Built email tracking and analytics

### Notification Features Implemented:
- **Booking Confirmations**: Automatic confirmation emails
- **Status Updates**: Real-time booking status notifications
- **Payment Confirmations**: Secure payment receipt emails
- **Reminder System**: Automated 24h and 2h reminders
- **Preference Management**: User-controlled notification settings
- **Professional Templates**: Branded email layouts
- **Queue Management**: Reliable email delivery system

## 🚀 What's Next?

In the next chapter, we'll:
- Create RESTful API endpoints for mobile integration
- Build authentication for API access
- Add API documentation and testing
- Implement rate limiting and security
- Create API versioning system

---

**Ready for API development?** Let's move on to [Chapter 12: API Development](./12-api-development.md)!
