# Chapter 7: Shift Scheduling and Management

## Overview
In this chapter, we'll implement comprehensive shift scheduling and management with Indonesian healthcare shift patterns. We'll build a system that handles complex hospital scheduling requirements including medical staff rotations, emergency coverage, and compliance with Indonesian healthcare regulations.

## Learning Objectives
- Create shift management system with Indonesian patterns
- Implement employee shift assignment and tracking
- Build shift conflict detection and resolution
- Create shift reporting and analytics
- Implement real-time shift monitoring
- Build responsive React components for shift management

## Prerequisites
- Completed Chapter 1-6
- Understanding of time-based scheduling systems
- Familiarity with Indonesian healthcare shift patterns

## Duration
90-120 minutes

---

## Step 1: Create Shift Controller

### 1.1 Create Shift API Controller

```bash
php artisan make:controller Api/ShiftController --api
```

Edit `app/Http/Controllers/Api/ShiftController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreShiftRequest;
use App\Http\Requests\UpdateShiftRequest;
use App\Http\Resources\ShiftResource;
use App\Models\Shift;
use App\Models\EmployeeShift;
use App\Services\ShiftService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ShiftController extends Controller
{
    protected ShiftService $shiftService;

    public function __construct(ShiftService $shiftService)
    {
        $this->shiftService = $shiftService;
    }

    /**
     * Display a listing of shifts
     */
    public function index(Request $request): JsonResponse
    {
        $query = Shift::with(['department']);

        // Filter by department
        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        // Filter by shift type
        if ($request->filled('shift_type')) {
            $query->where('shift_type', $request->shift_type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'start_time');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $perPage = min($request->get('per_page', 15), 100);
        $shifts = $query->paginate($perPage);

        return response()->json([
            'data' => ShiftResource::collection($shifts->items()),
            'meta' => [
                'current_page' => $shifts->currentPage(),
                'last_page' => $shifts->lastPage(),
                'per_page' => $shifts->perPage(),
                'total' => $shifts->total(),
            ],
        ]);
    }

    /**
     * Store a newly created shift
     */
    public function store(StoreShiftRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // Validate shift times don't overlap with existing shifts
            if ($this->shiftService->hasTimeOverlap($data)) {
                return response()->json([
                    'message' => 'Waktu shift bertabrakan dengan shift yang sudah ada',
                ], 422);
            }

            $shift = Shift::create($data);

            DB::commit();

            return response()->json([
                'message' => 'Shift berhasil ditambahkan',
                'data' => new ShiftResource($shift->load('department')),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menambahkan shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified shift
     */
    public function show(Shift $shift): JsonResponse
    {
        $shift->load([
            'department',
            'employeeShifts' => function ($q) {
                $q->with(['employee.position', 'employee.employeeType'])
                  ->where('date', '>=', now()->subDays(7))
                  ->orderBy('date', 'desc');
            }
        ]);

        return response()->json([
            'data' => new ShiftResource($shift),
        ]);
    }

    /**
     * Update the specified shift
     */
    public function update(UpdateShiftRequest $request, Shift $shift): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // Validate shift times don't overlap (excluding current shift)
            if ($this->shiftService->hasTimeOverlap($data, $shift->id)) {
                return response()->json([
                    'message' => 'Waktu shift bertabrakan dengan shift yang sudah ada',
                ], 422);
            }

            $shift->update($data);

            DB::commit();

            return response()->json([
                'message' => 'Data shift berhasil diperbarui',
                'data' => new ShiftResource($shift->load('department')),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat memperbarui data shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified shift
     */
    public function destroy(Shift $shift): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Check if shift has active assignments
            $activeAssignments = $shift->employeeShifts()
                ->where('date', '>=', now())
                ->where('status', '!=', 'cancelled')
                ->exists();

            if ($activeAssignments) {
                return response()->json([
                    'message' => 'Tidak dapat menghapus shift yang memiliki penugasan aktif',
                ], 422);
            }

            $shift->delete();

            DB::commit();

            return response()->json([
                'message' => 'Shift berhasil dihapus',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menghapus shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get shift schedule for a date range
     */
    public function schedule(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'department_id' => 'nullable|exists:departments,id',
            'employee_id' => 'nullable|exists:employees,id',
        ]);

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $query = EmployeeShift::with([
            'employee.position',
            'employee.employeeType',
            'shift',
            'shift.department'
        ])
        ->whereBetween('date', [$startDate, $endDate]);

        // Filter by department
        if ($request->filled('department_id')) {
            $query->whereHas('shift', function ($q) use ($request) {
                $q->where('department_id', $request->department_id);
            });
        }

        // Filter by employee
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        $employeeShifts = $query->orderBy('date')->orderBy('shift_id')->get();

        // Group by date for calendar view
        $schedule = $employeeShifts->groupBy(function ($item) {
            return $item->date->format('Y-m-d');
        })->map(function ($dayShifts) {
            return $dayShifts->groupBy('shift_id')->map(function ($shiftEmployees) {
                $shift = $shiftEmployees->first()->shift;
                return [
                    'shift' => [
                        'id' => $shift->id,
                        'name' => $shift->name,
                        'start_time' => $shift->start_time,
                        'end_time' => $shift->end_time,
                        'shift_type' => $shift->shift_type,
                        'indonesian_shift_type' => $shift->indonesian_shift_type,
                    ],
                    'employees' => $shiftEmployees->map(function ($employeeShift) {
                        return [
                            'id' => $employeeShift->id,
                            'employee' => [
                                'id' => $employeeShift->employee->id,
                                'full_name' => $employeeShift->employee->full_name,
                                'employee_number' => $employeeShift->employee->employee_number,
                                'position' => $employeeShift->employee->position?->title,
                                'employee_type' => $employeeShift->employee->employeeType?->indonesian_name,
                            ],
                            'status' => $employeeShift->status,
                            'indonesian_status' => $employeeShift->indonesian_status,
                            'check_in_time' => $employeeShift->check_in_time?->format('H:i'),
                            'check_out_time' => $employeeShift->check_out_time?->format('H:i'),
                            'total_working_hours' => $employeeShift->total_working_hours,
                            'notes' => $employeeShift->notes,
                        ];
                    }),
                ];
            });
        });

        return response()->json([
            'data' => $schedule,
            'summary' => [
                'total_shifts' => $employeeShifts->count(),
                'unique_employees' => $employeeShifts->pluck('employee_id')->unique()->count(),
                'departments_involved' => $employeeShifts->pluck('shift.department_id')->unique()->count(),
            ],
        ]);
    }

    /**
     * Assign employees to shift
     */
    public function assignEmployees(Request $request, Shift $shift): JsonResponse
    {
        $request->validate([
            'date' => 'required|date|after_or_equal:today',
            'employee_ids' => 'required|array|min:1',
            'employee_ids.*' => 'exists:employees,id',
        ]);

        try {
            DB::beginTransaction();

            $date = Carbon::parse($request->date);
            $employeeIds = $request->employee_ids;

            // Check for conflicts
            $conflicts = $this->shiftService->checkShiftConflicts($employeeIds, $date, $shift);
            
            if (!empty($conflicts)) {
                return response()->json([
                    'message' => 'Terdapat konflik jadwal shift',
                    'conflicts' => $conflicts,
                ], 422);
            }

            // Create employee shift assignments
            $assignments = [];
            foreach ($employeeIds as $employeeId) {
                $assignments[] = EmployeeShift::create([
                    'employee_id' => $employeeId,
                    'shift_id' => $shift->id,
                    'date' => $date,
                    'status' => 'scheduled',
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Karyawan berhasil ditugaskan ke shift',
                'data' => $assignments,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menugaskan karyawan',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get shift statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $stats = [
            'total_shifts' => Shift::count(),
            'active_shifts' => Shift::where('status', 'active')->count(),
            'total_assignments' => EmployeeShift::whereBetween('date', [$startDate, $endDate])->count(),
            'completed_shifts' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->where('status', 'completed')->count(),
            'missed_shifts' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->where('status', 'absent')->count(),
            'shifts_by_type' => Shift::select('shift_type')
                ->groupBy('shift_type')
                ->selectRaw('shift_type, count(*) as count')
                ->get()
                ->map(function ($item) {
                    return [
                        'type' => $item->shift_type,
                        'indonesian_type' => $item->indonesian_shift_type,
                        'count' => $item->count,
                    ];
                }),
            'shifts_by_department' => Shift::with('department')
                ->select('department_id')
                ->groupBy('department_id')
                ->selectRaw('department_id, count(*) as count')
                ->get()
                ->map(function ($item) {
                    return [
                        'department' => $item->department->name,
                        'count' => $item->count,
                    ];
                }),
            'average_working_hours' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->whereNotNull('total_working_hours')
                ->avg('total_working_hours'),
        ];

        return response()->json(['data' => $stats]);
    }
}
```

---

## Step 2: Create Shift Request Validation Classes

### 2.1 Create Store Shift Request

```bash
php artisan make:request StoreShiftRequest
```

Edit `app/Http/Requests/StoreShiftRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class StoreShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create shifts');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Basic Shift Information
            'name' => [
                'required',
                'string',
                'max:100',
                'unique:shifts,name',
            ],
            'shift_type' => [
                'required',
                Rule::in(['morning', 'afternoon', 'night', 'emergency', 'on_call']),
            ],
            'description' => [
                'nullable',
                'string',
                'max:500',
            ],

            // Time Configuration
            'start_time' => [
                'required',
                'date_format:H:i',
            ],
            'end_time' => [
                'required',
                'date_format:H:i',
                'after:start_time',
            ],
            'break_duration' => [
                'nullable',
                'integer',
                'min:0',
                'max:480', // Max 8 hours break
            ],
            'working_hours' => [
                'required',
                'numeric',
                'min:1',
                'max:24',
            ],

            // Department and Requirements
            'department_id' => [
                'required',
                'exists:departments,id',
            ],
            'required_employees' => [
                'required',
                'integer',
                'min:1',
                'max:50',
            ],
            'minimum_employees' => [
                'required',
                'integer',
                'min:1',
                'lte:required_employees',
            ],

            // Indonesian Healthcare Specific
            'is_medical_shift' => [
                'required',
                'boolean',
            ],
            'requires_license' => [
                'required',
                'boolean',
            ],
            'emergency_coverage' => [
                'required',
                'boolean',
            ],
            'weekend_applicable' => [
                'required',
                'boolean',
            ],
            'holiday_applicable' => [
                'required',
                'boolean',
            ],

            // Shift Pattern
            'rotation_pattern' => [
                'nullable',
                Rule::in(['daily', 'weekly', 'monthly', 'custom']),
            ],
            'max_consecutive_days' => [
                'nullable',
                'integer',
                'min:1',
                'max:14',
            ],
            'min_rest_hours' => [
                'nullable',
                'integer',
                'min:8',
                'max:72',
            ],

            // Status
            'is_active' => [
                'required',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama shift wajib diisi.',
            'name.unique' => 'Nama shift sudah digunakan.',
            'shift_type.required' => 'Tipe shift wajib dipilih.',
            'shift_type.in' => 'Tipe shift tidak valid.',
            'start_time.required' => 'Waktu mulai wajib diisi.',
            'start_time.date_format' => 'Format waktu mulai tidak valid (HH:MM).',
            'end_time.required' => 'Waktu selesai wajib diisi.',
            'end_time.date_format' => 'Format waktu selesai tidak valid (HH:MM).',
            'end_time.after' => 'Waktu selesai harus setelah waktu mulai.',
            'break_duration.integer' => 'Durasi istirahat harus berupa angka.',
            'break_duration.max' => 'Durasi istirahat maksimal 8 jam.',
            'working_hours.required' => 'Jam kerja wajib diisi.',
            'working_hours.min' => 'Jam kerja minimal 1 jam.',
            'working_hours.max' => 'Jam kerja maksimal 24 jam.',
            'department_id.required' => 'Departemen wajib dipilih.',
            'department_id.exists' => 'Departemen tidak ditemukan.',
            'required_employees.required' => 'Jumlah karyawan yang dibutuhkan wajib diisi.',
            'required_employees.min' => 'Minimal 1 karyawan dibutuhkan.',
            'required_employees.max' => 'Maksimal 50 karyawan per shift.',
            'minimum_employees.required' => 'Jumlah minimal karyawan wajib diisi.',
            'minimum_employees.lte' => 'Jumlah minimal tidak boleh lebih dari jumlah yang dibutuhkan.',
            'is_medical_shift.required' => 'Status shift medis wajib dipilih.',
            'requires_license.required' => 'Status persyaratan lisensi wajib dipilih.',
            'emergency_coverage.required' => 'Status cakupan darurat wajib dipilih.',
            'weekend_applicable.required' => 'Status berlaku akhir pekan wajib dipilih.',
            'holiday_applicable.required' => 'Status berlaku hari libur wajib dipilih.',
            'rotation_pattern.in' => 'Pola rotasi tidak valid.',
            'max_consecutive_days.min' => 'Maksimal hari berturut-turut minimal 1 hari.',
            'max_consecutive_days.max' => 'Maksimal hari berturut-turut maksimal 14 hari.',
            'min_rest_hours.min' => 'Minimal jam istirahat 8 jam.',
            'min_rest_hours.max' => 'Maksimal jam istirahat 72 jam.',
            'is_active.required' => 'Status aktif wajib dipilih.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'nama shift',
            'shift_type' => 'tipe shift',
            'description' => 'deskripsi',
            'start_time' => 'waktu mulai',
            'end_time' => 'waktu selesai',
            'break_duration' => 'durasi istirahat',
            'working_hours' => 'jam kerja',
            'department_id' => 'departemen',
            'required_employees' => 'jumlah karyawan yang dibutuhkan',
            'minimum_employees' => 'jumlah minimal karyawan',
            'is_medical_shift' => 'shift medis',
            'requires_license' => 'memerlukan lisensi',
            'emergency_coverage' => 'cakupan darurat',
            'weekend_applicable' => 'berlaku akhir pekan',
            'holiday_applicable' => 'berlaku hari libur',
            'rotation_pattern' => 'pola rotasi',
            'max_consecutive_days' => 'maksimal hari berturut-turut',
            'min_rest_hours' => 'minimal jam istirahat',
            'is_active' => 'status aktif',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate time logic
            if ($this->start_time && $this->end_time) {
                $start = Carbon::createFromFormat('H:i', $this->start_time);
                $end = Carbon::createFromFormat('H:i', $this->end_time);

                // Handle overnight shifts
                if ($end->lessThan($start)) {
                    $end->addDay();
                }

                $actualHours = $start->diffInHours($end);
                $breakHours = ($this->break_duration ?? 0) / 60;
                $workingHours = $actualHours - $breakHours;

                if (abs($workingHours - ($this->working_hours ?? 0)) > 0.5) {
                    $validator->errors()->add('working_hours', 'Jam kerja tidak sesuai dengan waktu mulai dan selesai.');
                }
            }

            // Validate medical shift requirements
            if ($this->is_medical_shift && !$this->requires_license) {
                $validator->errors()->add('requires_license', 'Shift medis harus memerlukan lisensi.');
            }

            // Validate emergency coverage for night shifts
            if ($this->shift_type === 'night' && !$this->emergency_coverage) {
                $validator->errors()->add('emergency_coverage', 'Shift malam harus memiliki cakupan darurat.');
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Http\Exceptions\HttpResponseException(
            response()->json([
                'message' => 'Data shift yang diberikan tidak valid.',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
```

### 2.2 Create Update Shift Request

```bash
php artisan make:request UpdateShiftRequest
```

Edit `app/Http/Requests/UpdateShiftRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class UpdateShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $shift = $this->route('shift');

        // Super Admin can update any shift
        if ($this->user()->hasRole('Super Admin')) {
            return true;
        }

        // Department Head can update shifts in their department
        if ($this->user()->hasRole('Department Head')) {
            return $this->user()->employee->department_id === $shift->department_id;
        }

        // Check permission
        return $this->user()->can('update shifts');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $shift = $this->route('shift');

        return [
            // Basic Shift Information
            'name' => [
                'sometimes',
                'required',
                'string',
                'max:100',
                Rule::unique('shifts', 'name')->ignore($shift->id),
            ],
            'shift_type' => [
                'sometimes',
                'required',
                Rule::in(['morning', 'afternoon', 'night', 'emergency', 'on_call']),
            ],
            'description' => [
                'sometimes',
                'nullable',
                'string',
                'max:500',
            ],

            // Time Configuration
            'start_time' => [
                'sometimes',
                'required',
                'date_format:H:i',
            ],
            'end_time' => [
                'sometimes',
                'required',
                'date_format:H:i',
                'after:start_time',
            ],
            'break_duration' => [
                'sometimes',
                'nullable',
                'integer',
                'min:0',
                'max:480',
            ],
            'working_hours' => [
                'sometimes',
                'required',
                'numeric',
                'min:1',
                'max:24',
            ],

            // Department and Requirements
            'department_id' => [
                'sometimes',
                'required',
                'exists:departments,id',
                $this->authorizeDepartmentChange(),
            ],
            'required_employees' => [
                'sometimes',
                'required',
                'integer',
                'min:1',
                'max:50',
            ],
            'minimum_employees' => [
                'sometimes',
                'required',
                'integer',
                'min:1',
                'lte:required_employees',
            ],

            // Indonesian Healthcare Specific
            'is_medical_shift' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'requires_license' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'emergency_coverage' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'weekend_applicable' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'holiday_applicable' => [
                'sometimes',
                'required',
                'boolean',
            ],

            // Shift Pattern
            'rotation_pattern' => [
                'sometimes',
                'nullable',
                Rule::in(['daily', 'weekly', 'monthly', 'custom']),
            ],
            'max_consecutive_days' => [
                'sometimes',
                'nullable',
                'integer',
                'min:1',
                'max:14',
            ],
            'min_rest_hours' => [
                'sometimes',
                'nullable',
                'integer',
                'min:8',
                'max:72',
            ],

            // Status
            'is_active' => [
                'sometimes',
                'required',
                'boolean',
            ],
        ];
    }

    /**
     * Check if user can change department
     */
    private function authorizeDepartmentChange(): string
    {
        if (!$this->user()->can('update shift department')) {
            return 'prohibited';
        }

        return 'sometimes';
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama shift wajib diisi.',
            'name.unique' => 'Nama shift sudah digunakan.',
            'shift_type.required' => 'Tipe shift wajib dipilih.',
            'shift_type.in' => 'Tipe shift tidak valid.',
            'start_time.required' => 'Waktu mulai wajib diisi.',
            'start_time.date_format' => 'Format waktu mulai tidak valid (HH:MM).',
            'end_time.required' => 'Waktu selesai wajib diisi.',
            'end_time.date_format' => 'Format waktu selesai tidak valid (HH:MM).',
            'end_time.after' => 'Waktu selesai harus setelah waktu mulai.',
            'break_duration.integer' => 'Durasi istirahat harus berupa angka.',
            'break_duration.max' => 'Durasi istirahat maksimal 8 jam.',
            'working_hours.required' => 'Jam kerja wajib diisi.',
            'working_hours.min' => 'Jam kerja minimal 1 jam.',
            'working_hours.max' => 'Jam kerja maksimal 24 jam.',
            'department_id.required' => 'Departemen wajib dipilih.',
            'department_id.exists' => 'Departemen tidak ditemukan.',
            'department_id.prohibited' => 'Anda tidak memiliki izin untuk mengubah departemen shift.',
            'required_employees.required' => 'Jumlah karyawan yang dibutuhkan wajib diisi.',
            'required_employees.min' => 'Minimal 1 karyawan dibutuhkan.',
            'required_employees.max' => 'Maksimal 50 karyawan per shift.',
            'minimum_employees.required' => 'Jumlah minimal karyawan wajib diisi.',
            'minimum_employees.lte' => 'Jumlah minimal tidak boleh lebih dari jumlah yang dibutuhkan.',
            'is_medical_shift.required' => 'Status shift medis wajib dipilih.',
            'requires_license.required' => 'Status persyaratan lisensi wajib dipilih.',
            'emergency_coverage.required' => 'Status cakupan darurat wajib dipilih.',
            'weekend_applicable.required' => 'Status berlaku akhir pekan wajib dipilih.',
            'holiday_applicable.required' => 'Status berlaku hari libur wajib dipilih.',
            'rotation_pattern.in' => 'Pola rotasi tidak valid.',
            'max_consecutive_days.min' => 'Maksimal hari berturut-turut minimal 1 hari.',
            'max_consecutive_days.max' => 'Maksimal hari berturut-turut maksimal 14 hari.',
            'min_rest_hours.min' => 'Minimal jam istirahat 8 jam.',
            'min_rest_hours.max' => 'Maksimal jam istirahat 72 jam.',
            'is_active.required' => 'Status aktif wajib dipilih.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'nama shift',
            'shift_type' => 'tipe shift',
            'description' => 'deskripsi',
            'start_time' => 'waktu mulai',
            'end_time' => 'waktu selesai',
            'break_duration' => 'durasi istirahat',
            'working_hours' => 'jam kerja',
            'department_id' => 'departemen',
            'required_employees' => 'jumlah karyawan yang dibutuhkan',
            'minimum_employees' => 'jumlah minimal karyawan',
            'is_medical_shift' => 'shift medis',
            'requires_license' => 'memerlukan lisensi',
            'emergency_coverage' => 'cakupan darurat',
            'weekend_applicable' => 'berlaku akhir pekan',
            'holiday_applicable' => 'berlaku hari libur',
            'rotation_pattern' => 'pola rotasi',
            'max_consecutive_days' => 'maksimal hari berturut-turut',
            'min_rest_hours' => 'minimal jam istirahat',
            'is_active' => 'status aktif',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate time logic if both times are provided
            if ($this->start_time && $this->end_time) {
                $start = Carbon::createFromFormat('H:i', $this->start_time);
                $end = Carbon::createFromFormat('H:i', $this->end_time);

                if ($end->lessThan($start)) {
                    $end->addDay();
                }

                $actualHours = $start->diffInHours($end);
                $breakHours = ($this->break_duration ?? 0) / 60;
                $workingHours = $actualHours - $breakHours;

                if ($this->working_hours && abs($workingHours - $this->working_hours) > 0.5) {
                    $validator->errors()->add('working_hours', 'Jam kerja tidak sesuai dengan waktu mulai dan selesai.');
                }
            }

            // Validate medical shift requirements
            if ($this->is_medical_shift && isset($this->requires_license) && !$this->requires_license) {
                $validator->errors()->add('requires_license', 'Shift medis harus memerlukan lisensi.');
            }

            // Validate emergency coverage for night shifts
            if ($this->shift_type === 'night' && isset($this->emergency_coverage) && !$this->emergency_coverage) {
                $validator->errors()->add('emergency_coverage', 'Shift malam harus memiliki cakupan darurat.');
            }

            // Check if shift has active assignments before major changes
            $shift = $this->route('shift');
            if ($shift && $shift->employeeShifts()->where('date', '>=', now())->exists()) {
                $restrictedFields = ['start_time', 'end_time', 'working_hours', 'department_id'];
                foreach ($restrictedFields as $field) {
                    if ($this->has($field)) {
                        $validator->errors()->add($field, 'Tidak dapat mengubah ' . $this->attributes()[$field] . ' karena shift memiliki jadwal aktif.');
                    }
                }
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Http\Exceptions\HttpResponseException(
            response()->json([
                'message' => 'Data shift yang diberikan tidak valid.',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
```

---

## Step 3: Create Shift Service Layer

### 3.1 Create Shift Service

```bash
php artisan make:service ShiftService
```

Create `app/Services/ShiftService.php`:

```php
<?php

namespace App\Services;

use App\Models\Shift;
use App\Models\Employee;
use App\Models\EmployeeShift;
use App\Models\Department;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class ShiftService
{
    /**
     * Create a new shift
     */
    public function createShift(array $data): Shift
    {
        return DB::transaction(function () use ($data) {
            // Calculate working hours if not provided
            if (!isset($data['working_hours'])) {
                $data['working_hours'] = $this->calculateWorkingHours(
                    $data['start_time'],
                    $data['end_time'],
                    $data['break_duration'] ?? 0
                );
            }

            // Create shift
            $shift = Shift::create($data);

            // Log shift creation
            activity()
                ->performedOn($shift)
                ->causedBy(auth()->user())
                ->withProperties(['action' => 'created'])
                ->log('Shift created: ' . $shift->name);

            return $shift->load('department');
        });
    }

    /**
     * Update shift information
     */
    public function updateShift(Shift $shift, array $data): Shift
    {
        return DB::transaction(function () use ($shift, $data) {
            // Check if shift has active assignments
            $hasActiveAssignments = $shift->employeeShifts()
                ->where('date', '>=', now()->toDateString())
                ->exists();

            if ($hasActiveAssignments) {
                // Restrict certain changes if shift has active assignments
                $restrictedFields = ['start_time', 'end_time', 'working_hours', 'department_id'];
                foreach ($restrictedFields as $field) {
                    if (isset($data[$field])) {
                        throw new \Exception("Cannot modify {$field} because shift has active assignments.");
                    }
                }
            }

            // Recalculate working hours if time changed
            if (isset($data['start_time']) || isset($data['end_time']) || isset($data['break_duration'])) {
                $data['working_hours'] = $this->calculateWorkingHours(
                    $data['start_time'] ?? $shift->start_time,
                    $data['end_time'] ?? $shift->end_time,
                    $data['break_duration'] ?? $shift->break_duration ?? 0
                );
            }

            // Track changes for audit
            $originalData = $shift->toArray();

            // Update shift
            $shift->update($data);

            // Log shift update
            $changes = array_diff_assoc($shift->toArray(), $originalData);
            if (!empty($changes)) {
                activity()
                    ->performedOn($shift)
                    ->causedBy(auth()->user())
                    ->withProperties([
                        'action' => 'updated',
                        'changes' => $changes,
                        'original' => $originalData
                    ])
                    ->log('Shift updated: ' . $shift->name);
            }

            return $shift->load('department');
        });
    }

    /**
     * Delete shift
     */
    public function deleteShift(Shift $shift): bool
    {
        return DB::transaction(function () use ($shift) {
            // Check if shift has any assignments
            if ($shift->employeeShifts()->exists()) {
                throw new \Exception('Cannot delete shift with existing assignments. Please remove all assignments first.');
            }

            // Log shift deletion
            activity()
                ->performedOn($shift)
                ->causedBy(auth()->user())
                ->withProperties(['action' => 'deleted'])
                ->log('Shift deleted: ' . $shift->name);

            return $shift->delete();
        });
    }

    /**
     * Assign employees to shift for specific dates
     */
    public function assignEmployeesToShift(Shift $shift, array $employeeIds, array $dates): Collection
    {
        return DB::transaction(function () use ($shift, $employeeIds, $dates) {
            $assignments = collect();

            foreach ($dates as $date) {
                $carbonDate = Carbon::parse($date);

                foreach ($employeeIds as $employeeId) {
                    $employee = Employee::find($employeeId);

                    // Validate assignment
                    $this->validateShiftAssignment($shift, $employee, $carbonDate);

                    // Check for conflicts
                    $conflicts = $this->checkShiftConflicts($employee, $carbonDate, $shift);
                    if ($conflicts->isNotEmpty()) {
                        throw new \Exception("Employee {$employee->full_name} has shift conflicts on {$carbonDate->format('Y-m-d')}");
                    }

                    // Create assignment
                    $assignment = EmployeeShift::create([
                        'employee_id' => $employeeId,
                        'shift_id' => $shift->id,
                        'date' => $carbonDate,
                        'status' => 'scheduled',
                        'assigned_by' => auth()->id(),
                        'assigned_at' => now(),
                    ]);

                    $assignments->push($assignment);

                    // Log assignment
                    activity()
                        ->performedOn($assignment)
                        ->causedBy(auth()->user())
                        ->withProperties([
                            'action' => 'assigned',
                            'employee_name' => $employee->full_name,
                            'shift_name' => $shift->name,
                            'date' => $carbonDate->format('Y-m-d')
                        ])
                        ->log("Employee assigned to shift: {$employee->full_name} to {$shift->name} on {$carbonDate->format('Y-m-d')}");
                }
            }

            return $assignments;
        });
    }

    /**
     * Generate shift schedule for a period
     */
    public function generateShiftSchedule(array $shiftIds, Carbon $startDate, Carbon $endDate, array $options = []): Collection
    {
        return DB::transaction(function () use ($shiftIds, $startDate, $endDate, $options) {
            $shifts = Shift::whereIn('id', $shiftIds)->with('department')->get();
            $schedule = collect();

            $period = CarbonPeriod::create($startDate, $endDate);

            foreach ($period as $date) {
                // Skip weekends if not applicable
                if ($date->isWeekend() && !($options['include_weekends'] ?? true)) {
                    continue;
                }

                foreach ($shifts as $shift) {
                    // Skip if shift not applicable for weekends/holidays
                    if ($date->isWeekend() && !$shift->weekend_applicable) {
                        continue;
                    }

                    // Get available employees for this shift
                    $availableEmployees = $this->getAvailableEmployees($shift, $date);

                    // Auto-assign if requested
                    if ($options['auto_assign'] ?? false) {
                        $assignedEmployees = $this->autoAssignEmployees($shift, $availableEmployees, $date);

                        foreach ($assignedEmployees as $employee) {
                            $assignment = EmployeeShift::create([
                                'employee_id' => $employee->id,
                                'shift_id' => $shift->id,
                                'date' => $date,
                                'status' => 'scheduled',
                                'assigned_by' => auth()->id(),
                                'assigned_at' => now(),
                            ]);

                            $schedule->push($assignment);
                        }
                    } else {
                        // Just create schedule template
                        $schedule->push([
                            'shift' => $shift,
                            'date' => $date,
                            'available_employees' => $availableEmployees,
                            'required_employees' => $shift->required_employees,
                            'minimum_employees' => $shift->minimum_employees,
                        ]);
                    }
                }
            }

            // Log schedule generation
            activity()
                ->causedBy(auth()->user())
                ->withProperties([
                    'action' => 'schedule_generated',
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'shifts_count' => count($shiftIds),
                    'assignments_count' => $schedule->count()
                ])
                ->log("Shift schedule generated for {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");

            return $schedule;
        });
    }

    /**
     * Check for shift conflicts
     */
    public function checkShiftConflicts(Employee $employee, Carbon $date, Shift $excludeShift = null): Collection
    {
        $query = EmployeeShift::where('employee_id', $employee->id)
            ->where('date', $date)
            ->with('shift');

        if ($excludeShift) {
            $query->where('shift_id', '!=', $excludeShift->id);
        }

        $existingShifts = $query->get();
        $conflicts = collect();

        foreach ($existingShifts as $existingShift) {
            // Check for time overlap
            if ($this->shiftsOverlap($excludeShift ?? new Shift(), $existingShift->shift)) {
                $conflicts->push([
                    'type' => 'time_overlap',
                    'existing_shift' => $existingShift,
                    'message' => "Time overlap with {$existingShift->shift->name}"
                ]);
            }

            // Check minimum rest hours
            if ($this->violatesRestHours($excludeShift ?? new Shift(), $existingShift->shift, $date)) {
                $conflicts->push([
                    'type' => 'insufficient_rest',
                    'existing_shift' => $existingShift,
                    'message' => "Insufficient rest hours between shifts"
                ]);
            }
        }

        // Check consecutive days limit
        $consecutiveDays = $this->getConsecutiveDays($employee, $date);
        if ($consecutiveDays >= ($excludeShift->max_consecutive_days ?? 7)) {
            $conflicts->push([
                'type' => 'consecutive_days_limit',
                'message' => "Exceeds maximum consecutive days limit"
            ]);
        }

        return $conflicts;
    }

    /**
     * Get available employees for a shift
     */
    public function getAvailableEmployees(Shift $shift, Carbon $date): Collection
    {
        $query = Employee::where('employment_status', 'active')
            ->where('department_id', $shift->department_id);

        // Filter by medical staff requirement
        if ($shift->is_medical_shift) {
            $query->whereHas('employeeType', function ($q) {
                $q->where('is_medical_staff', true);
            });
        }

        // Filter by license requirement
        if ($shift->requires_license) {
            $query->whereHas('licenses', function ($q) use ($date) {
                $q->where('status', 'active')
                  ->where('expiry_date', '>', $date);
            });
        }

        $employees = $query->get();

        // Filter out employees with conflicts
        return $employees->filter(function ($employee) use ($shift, $date) {
            return $this->checkShiftConflicts($employee, $date, $shift)->isEmpty();
        });
    }

    /**
     * Auto-assign employees to shift
     */
    private function autoAssignEmployees(Shift $shift, Collection $availableEmployees, Carbon $date): Collection
    {
        // Prioritize employees based on various factors
        $prioritizedEmployees = $availableEmployees->sortBy(function ($employee) use ($date) {
            $score = 0;

            // Prefer employees with fewer recent assignments
            $recentAssignments = EmployeeShift::where('employee_id', $employee->id)
                ->where('date', '>=', $date->copy()->subDays(7))
                ->count();
            $score += (10 - $recentAssignments);

            // Prefer senior employees for medical shifts
            $score += $employee->years_of_service;

            // Prefer employees with relevant licenses
            if ($employee->licenses()->where('status', 'active')->exists()) {
                $score += 5;
            }

            return -$score; // Negative for descending sort
        });

        // Select required number of employees
        return $prioritizedEmployees->take($shift->required_employees);
    }

    /**
     * Calculate working hours between start and end time
     */
    private function calculateWorkingHours(string $startTime, string $endTime, int $breakDuration = 0): float
    {
        $start = Carbon::createFromFormat('H:i', $startTime);
        $end = Carbon::createFromFormat('H:i', $endTime);

        // Handle overnight shifts
        if ($end->lessThan($start)) {
            $end->addDay();
        }

        $totalMinutes = $start->diffInMinutes($end);
        $workingMinutes = $totalMinutes - $breakDuration;

        return round($workingMinutes / 60, 2);
    }

    /**
     * Check if two shifts overlap in time
     */
    private function shiftsOverlap(Shift $shift1, Shift $shift2): bool
    {
        $start1 = Carbon::createFromFormat('H:i', $shift1->start_time);
        $end1 = Carbon::createFromFormat('H:i', $shift1->end_time);
        $start2 = Carbon::createFromFormat('H:i', $shift2->start_time);
        $end2 = Carbon::createFromFormat('H:i', $shift2->end_time);

        // Handle overnight shifts
        if ($end1->lessThan($start1)) $end1->addDay();
        if ($end2->lessThan($start2)) $end2->addDay();

        return $start1->lessThan($end2) && $start2->lessThan($end1);
    }

    /**
     * Check if assignment violates minimum rest hours
     */
    private function violatesRestHours(Shift $newShift, Shift $existingShift, Carbon $date): bool
    {
        $minRestHours = max($newShift->min_rest_hours ?? 8, $existingShift->min_rest_hours ?? 8);

        $existingEnd = Carbon::createFromFormat('H:i', $existingShift->end_time);
        $newStart = Carbon::createFromFormat('H:i', $newShift->start_time);

        // Handle overnight shifts
        if ($existingEnd->lessThan(Carbon::createFromFormat('H:i', $existingShift->start_time))) {
            $existingEnd->addDay();
        }

        $restHours = $existingEnd->diffInHours($newStart);

        return $restHours < $minRestHours;
    }

    /**
     * Get consecutive working days for employee
     */
    private function getConsecutiveDays(Employee $employee, Carbon $date): int
    {
        $consecutiveDays = 0;
        $checkDate = $date->copy()->subDay();

        while (true) {
            $hasShift = EmployeeShift::where('employee_id', $employee->id)
                ->where('date', $checkDate)
                ->exists();

            if (!$hasShift) {
                break;
            }

            $consecutiveDays++;
            $checkDate->subDay();

            // Prevent infinite loop
            if ($consecutiveDays > 30) {
                break;
            }
        }

        return $consecutiveDays;
    }

    /**
     * Validate shift assignment
     */
    private function validateShiftAssignment(Shift $shift, Employee $employee, Carbon $date): void
    {
        // Check if employee is active
        if ($employee->employment_status !== 'active') {
            throw new \Exception("Employee {$employee->full_name} is not active.");
        }

        // Check department match
        if ($employee->department_id !== $shift->department_id) {
            throw new \Exception("Employee {$employee->full_name} is not in the correct department for this shift.");
        }

        // Check medical staff requirement
        if ($shift->is_medical_shift && !$employee->employeeType->is_medical_staff) {
            throw new \Exception("Employee {$employee->full_name} is not medical staff.");
        }

        // Check license requirement
        if ($shift->requires_license) {
            $hasValidLicense = $employee->licenses()
                ->where('status', 'active')
                ->where('expiry_date', '>', $date)
                ->exists();

            if (!$hasValidLicense) {
                throw new \Exception("Employee {$employee->full_name} does not have a valid license.");
            }
        }

        // Check if shift is active
        if (!$shift->is_active) {
            throw new \Exception("Shift {$shift->name} is not active.");
        }

        // Check weekend/holiday applicability
        if ($date->isWeekend() && !$shift->weekend_applicable) {
            throw new \Exception("Shift {$shift->name} is not applicable for weekends.");
        }
    }

    /**
     * Get shift statistics
     */
    public function getShiftStatistics(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'total_shifts' => Shift::count(),
            'active_shifts' => Shift::where('is_active', true)->count(),
            'medical_shifts' => Shift::where('is_medical_shift', true)->count(),
            'total_assignments' => EmployeeShift::whereBetween('date', [$startDate, $endDate])->count(),
            'completed_shifts' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->where('status', 'completed')->count(),
            'missed_shifts' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->where('status', 'missed')->count(),
            'shifts_by_type' => Shift::select('shift_type')
                ->groupBy('shift_type')
                ->selectRaw('shift_type, count(*) as count')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->shift_type => $item->count];
                }),
            'shifts_by_department' => Shift::with('department')
                ->select('department_id')
                ->groupBy('department_id')
                ->selectRaw('department_id, count(*) as count')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->department->name ?? 'Unknown' => $item->count];
                }),
            'average_working_hours' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->whereNotNull('total_working_hours')
                ->avg('total_working_hours'),
            'coverage_percentage' => $this->calculateCoveragePercentage($startDate, $endDate),
        ];
    }

    /**
     * Calculate shift coverage percentage
     */
    private function calculateCoveragePercentage(Carbon $startDate, Carbon $endDate): float
    {
        $totalRequiredSlots = 0;
        $totalFilledSlots = 0;

        $period = CarbonPeriod::create($startDate, $endDate);
        $shifts = Shift::where('is_active', true)->get();

        foreach ($period as $date) {
            foreach ($shifts as $shift) {
                if ($date->isWeekend() && !$shift->weekend_applicable) {
                    continue;
                }

                $totalRequiredSlots += $shift->required_employees;

                $filledSlots = EmployeeShift::where('shift_id', $shift->id)
                    ->where('date', $date)
                    ->count();

                $totalFilledSlots += $filledSlots;
            }
        }

        return $totalRequiredSlots > 0 ? round(($totalFilledSlots / $totalRequiredSlots) * 100, 2) : 0;
    }
}
```

---

## Step 4: Create Shift Management React Components

### 4.1 Create Shift List Component

Create `resources/js/Pages/Shifts/Index.tsx`:

```tsx
import React, { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/Components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/Components/ui/alert-dialog';
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Clock,
  Calendar,
  Shield,
  Building2
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { toast } from 'sonner';

interface Shift {
  id: number;
  name: string;
  shift_type: 'morning' | 'afternoon' | 'night' | 'emergency' | 'on_call';
  start_time: string;
  end_time: string;
  working_hours: number;
  break_duration: number;
  department: {
    id: number;
    name: string;
  };
  required_employees: number;
  minimum_employees: number;
  is_medical_shift: boolean;
  requires_license: boolean;
  emergency_coverage: boolean;
  weekend_applicable: boolean;
  holiday_applicable: boolean;
  is_active: boolean;
  assignments_count: number;
  created_at: string;
  updated_at: string;
}

interface Props {
  shifts: {
    data: Shift[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  departments: Array<{
    id: number;
    name: string;
  }>;
  filters: {
    search?: string;
    department_id?: string;
    shift_type?: string;
    status?: string;
  };
}

export default function ShiftIndex({ shifts, departments, filters }: Props) {
  const [search, setSearch] = useState(filters.search || '');
  const [departmentId, setDepartmentId] = useState(filters.department_id || '');
  const [shiftType, setShiftType] = useState(filters.shift_type || '');
  const [status, setStatus] = useState(filters.status || '');
  const [deleteId, setDeleteId] = useState<number | null>(null);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      router.get(route('shifts.index'), {
        search,
        department_id: departmentId,
        shift_type: shiftType,
        status,
      }, {
        preserveState: true,
        replace: true,
      });
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [search, departmentId, shiftType, status]);

  const handleDelete = (id: number) => {
    router.delete(route('shifts.destroy', id), {
      onSuccess: () => {
        toast.success('Shift berhasil dihapus');
        setDeleteId(null);
      },
      onError: (errors) => {
        toast.error(errors.message || 'Gagal menghapus shift');
        setDeleteId(null);
      },
    });
  };

  const getShiftTypeBadge = (type: string) => {
    const variants = {
      morning: { variant: 'default' as const, label: 'Pagi', color: 'bg-yellow-100 text-yellow-800' },
      afternoon: { variant: 'secondary' as const, label: 'Siang', color: 'bg-orange-100 text-orange-800' },
      night: { variant: 'outline' as const, label: 'Malam', color: 'bg-blue-100 text-blue-800' },
      emergency: { variant: 'destructive' as const, label: 'Darurat', color: 'bg-red-100 text-red-800' },
      on_call: { variant: 'outline' as const, label: 'Siaga', color: 'bg-purple-100 text-purple-800' },
    };

    const config = variants[type as keyof typeof variants] || variants.morning;

    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? 'default' : 'secondary'}>
        {isActive ? 'Aktif' : 'Tidak Aktif'}
      </Badge>
    );
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            Manajemen Shift
          </h2>
          <div className="flex gap-2">
            <Link href={route('shifts.schedule')}>
              <Button variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Jadwal Shift
              </Button>
            </Link>
            <Link href={route('shifts.create')}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Shift
              </Button>
            </Link>
          </div>
        </div>
      }
    >
      <Head title="Manajemen Shift" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {/* Filters */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Filter Shift</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Cari shift..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <select
                  value={departmentId}
                  onChange={(e) => setDepartmentId(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Semua Departemen</option>
                  {departments.map((dept) => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </select>
                <select
                  value={shiftType}
                  onChange={(e) => setShiftType(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Semua Tipe</option>
                  <option value="morning">Pagi</option>
                  <option value="afternoon">Siang</option>
                  <option value="night">Malam</option>
                  <option value="emergency">Darurat</option>
                  <option value="on_call">Siaga</option>
                </select>
                <select
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Semua Status</option>
                  <option value="active">Aktif</option>
                  <option value="inactive">Tidak Aktif</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Shift Table */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Daftar Shift</CardTitle>
                <div className="text-sm text-gray-500">
                  Total: {shifts.total} shift
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nama Shift</TableHead>
                    <TableHead>Tipe</TableHead>
                    <TableHead>Waktu</TableHead>
                    <TableHead>Departemen</TableHead>
                    <TableHead>Karyawan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {shifts.data.map((shift) => (
                    <TableRow key={shift.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{shift.name}</div>
                          <div className="flex gap-2 mt-1">
                            {shift.is_medical_shift && (
                              <Badge variant="outline" className="text-xs">
                                <Shield className="h-3 w-3 mr-1" />
                                Medis
                              </Badge>
                            )}
                            {shift.requires_license && (
                              <Badge variant="outline" className="text-xs">
                                Lisensi
                              </Badge>
                            )}
                            {shift.emergency_coverage && (
                              <Badge variant="destructive" className="text-xs">
                                Darurat
                              </Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getShiftTypeBadge(shift.shift_type)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1 text-gray-400" />
                          <div>
                            <div>{shift.start_time} - {shift.end_time}</div>
                            <div className="text-sm text-gray-500">
                              {shift.working_hours}h kerja
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Building2 className="h-4 w-4 mr-1 text-gray-400" />
                          {shift.department.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1 text-gray-400" />
                            {shift.assignments_count}/{shift.required_employees}
                          </div>
                          <div className="text-sm text-gray-500">
                            Min: {shift.minimum_employees}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(shift.is_active)}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={route('shifts.edit', shift.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={route('shifts.assignments', shift.id)}>
                                <Users className="h-4 w-4 mr-2" />
                                Kelola Penugasan
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => setDeleteId(shift.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Hapus
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteId !== null} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus shift ini?
              Semua penugasan yang terkait akan dihapus.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteId && handleDelete(deleteId)}
              className="bg-red-600 hover:bg-red-700"
            >
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AuthenticatedLayout>
  );
}
```

### 4.2 Create Shift Form Component

Create `resources/js/Pages/Shifts/Form.tsx`:

```tsx
import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Checkbox } from '@/Components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import { ArrowLeft, Save, X, Clock, Users, Shield } from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { toast } from 'sonner';

interface Shift {
  id: number;
  name: string;
  shift_type: 'morning' | 'afternoon' | 'night' | 'emergency' | 'on_call';
  start_time: string;
  end_time: string;
  working_hours: number;
  break_duration: number;
  department_id: number;
  required_employees: number;
  minimum_employees: number;
  is_medical_shift: boolean;
  requires_license: boolean;
  emergency_coverage: boolean;
  weekend_applicable: boolean;
  holiday_applicable: boolean;
  is_active: boolean;
  description?: string;
}

interface Department {
  id: number;
  name: string;
}

interface Props {
  shift?: Shift;
  departments: Department[];
  isEdit?: boolean;
}

export default function ShiftForm({ shift, departments, isEdit = false }: Props) {
  const { data, setData, post, put, processing, errors, reset } = useForm({
    name: shift?.name || '',
    shift_type: shift?.shift_type || 'morning',
    start_time: shift?.start_time || '',
    end_time: shift?.end_time || '',
    working_hours: shift?.working_hours || '',
    break_duration: shift?.break_duration || 60,
    department_id: shift?.department_id || '',
    required_employees: shift?.required_employees || '',
    minimum_employees: shift?.minimum_employees || '',
    is_medical_shift: shift?.is_medical_shift || false,
    requires_license: shift?.requires_license || false,
    emergency_coverage: shift?.emergency_coverage || false,
    weekend_applicable: shift?.weekend_applicable || true,
    holiday_applicable: shift?.holiday_applicable || true,
    is_active: shift?.is_active ?? true,
    description: shift?.description || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const submitData = {
      ...data,
      working_hours: data.working_hours ? parseFloat(data.working_hours.toString()) : null,
      break_duration: data.break_duration ? parseInt(data.break_duration.toString()) : 60,
      required_employees: data.required_employees ? parseInt(data.required_employees.toString()) : null,
      minimum_employees: data.minimum_employees ? parseInt(data.minimum_employees.toString()) : null,
      department_id: data.department_id || null,
    };

    if (isEdit && shift) {
      put(route('shifts.update', shift.id), {
        onSuccess: () => {
          toast.success('Shift berhasil diperbarui');
        },
        onError: () => {
          toast.error('Gagal memperbarui shift');
        },
      });
    } else {
      post(route('shifts.store'), {
        onSuccess: () => {
          toast.success('Shift berhasil dibuat');
          reset();
        },
        onError: () => {
          toast.error('Gagal membuat shift');
        },
      });
    }
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex items-center gap-4">
          <Link href={route('shifts.index')}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Button>
          </Link>
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            {isEdit ? 'Edit Shift' : 'Tambah Shift'}
          </h2>
        </div>
      }
    >
      <Head title={isEdit ? 'Edit Shift' : 'Tambah Shift'} />

      <div className="py-12">
        <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Informasi Dasar Shift
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nama Shift *</Label>
                    <Input
                      id="name"
                      value={data.name}
                      onChange={(e) => setData('name', e.target.value)}
                      placeholder="Contoh: Shift Pagi Kardiologi"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-600">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="shift_type">Tipe Shift *</Label>
                    <Select
                      value={data.shift_type}
                      onValueChange={(value) => setData('shift_type', value as any)}
                    >
                      <SelectTrigger className={errors.shift_type ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Pilih tipe shift" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="morning">Pagi (06:00-14:00)</SelectItem>
                        <SelectItem value="afternoon">Siang (14:00-22:00)</SelectItem>
                        <SelectItem value="night">Malam (22:00-06:00)</SelectItem>
                        <SelectItem value="emergency">Darurat (24 Jam)</SelectItem>
                        <SelectItem value="on_call">Siaga (On Call)</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.shift_type && (
                      <p className="text-sm text-red-600">{errors.shift_type}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="start_time">Waktu Mulai *</Label>
                    <Input
                      id="start_time"
                      type="time"
                      value={data.start_time}
                      onChange={(e) => setData('start_time', e.target.value)}
                      className={errors.start_time ? 'border-red-500' : ''}
                    />
                    {errors.start_time && (
                      <p className="text-sm text-red-600">{errors.start_time}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="end_time">Waktu Selesai *</Label>
                    <Input
                      id="end_time"
                      type="time"
                      value={data.end_time}
                      onChange={(e) => setData('end_time', e.target.value)}
                      className={errors.end_time ? 'border-red-500' : ''}
                    />
                    {errors.end_time && (
                      <p className="text-sm text-red-600">{errors.end_time}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="working_hours">Jam Kerja</Label>
                    <Input
                      id="working_hours"
                      type="number"
                      step="0.5"
                      value={data.working_hours}
                      onChange={(e) => setData('working_hours', e.target.value)}
                      placeholder="8"
                      className={errors.working_hours ? 'border-red-500' : ''}
                    />
                    {errors.working_hours && (
                      <p className="text-sm text-red-600">{errors.working_hours}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="department_id">Departemen *</Label>
                    <Select
                      value={data.department_id.toString()}
                      onValueChange={(value) => setData('department_id', value)}
                    >
                      <SelectTrigger className={errors.department_id ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Pilih departemen" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id.toString()}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.department_id && (
                      <p className="text-sm text-red-600">{errors.department_id}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="break_duration">Durasi Istirahat (menit)</Label>
                    <Input
                      id="break_duration"
                      type="number"
                      value={data.break_duration}
                      onChange={(e) => setData('break_duration', e.target.value)}
                      placeholder="60"
                      className={errors.break_duration ? 'border-red-500' : ''}
                    />
                    {errors.break_duration && (
                      <p className="text-sm text-red-600">{errors.break_duration}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    value={data.description}
                    onChange={(e) => setData('description', e.target.value)}
                    placeholder="Deskripsi shift..."
                    rows={3}
                    className={errors.description ? 'border-red-500' : ''}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600">{errors.description}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Staffing Requirements */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Kebutuhan Karyawan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="required_employees">Jumlah Karyawan Dibutuhkan *</Label>
                    <Input
                      id="required_employees"
                      type="number"
                      value={data.required_employees}
                      onChange={(e) => setData('required_employees', e.target.value)}
                      placeholder="5"
                      min="1"
                      className={errors.required_employees ? 'border-red-500' : ''}
                    />
                    {errors.required_employees && (
                      <p className="text-sm text-red-600">{errors.required_employees}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="minimum_employees">Minimum Karyawan *</Label>
                    <Input
                      id="minimum_employees"
                      type="number"
                      value={data.minimum_employees}
                      onChange={(e) => setData('minimum_employees', e.target.value)}
                      placeholder="3"
                      min="1"
                      className={errors.minimum_employees ? 'border-red-500' : ''}
                    />
                    {errors.minimum_employees && (
                      <p className="text-sm text-red-600">{errors.minimum_employees}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shift Properties */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Properti Shift
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="is_medical_shift"
                        checked={data.is_medical_shift}
                        onCheckedChange={(checked) => setData('is_medical_shift', !!checked)}
                      />
                      <Label htmlFor="is_medical_shift">Shift Medis</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="requires_license"
                        checked={data.requires_license}
                        onCheckedChange={(checked) => setData('requires_license', !!checked)}
                      />
                      <Label htmlFor="requires_license">Memerlukan Lisensi</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="emergency_coverage"
                        checked={data.emergency_coverage}
                        onCheckedChange={(checked) => setData('emergency_coverage', !!checked)}
                      />
                      <Label htmlFor="emergency_coverage">Cakupan Darurat</Label>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="weekend_applicable"
                        checked={data.weekend_applicable}
                        onCheckedChange={(checked) => setData('weekend_applicable', !!checked)}
                      />
                      <Label htmlFor="weekend_applicable">Berlaku Weekend</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="holiday_applicable"
                        checked={data.holiday_applicable}
                        onCheckedChange={(checked) => setData('holiday_applicable', !!checked)}
                      />
                      <Label htmlFor="holiday_applicable">Berlaku Hari Libur</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="is_active"
                        checked={data.is_active}
                        onCheckedChange={(checked) => setData('is_active', !!checked)}
                      />
                      <Label htmlFor="is_active">Aktif</Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex justify-end gap-4">
              <Link href={route('shifts.index')}>
                <Button type="button" variant="outline">
                  <X className="h-4 w-4 mr-2" />
                  Batal
                </Button>
              </Link>
              <Button type="submit" disabled={processing}>
                <Save className="h-4 w-4 mr-2" />
                {processing ? 'Menyimpan...' : (isEdit ? 'Perbarui' : 'Simpan')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

---

## Step 5: Create Schedule Calendar Component

### 5.1 Create Schedule Calendar

Create `resources/js/Pages/Shifts/Schedule.tsx`:

```tsx
import React, { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  Users,
  Clock,
  Plus,
  Filter
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';

interface ShiftAssignment {
  id: number;
  shift: {
    id: number;
    name: string;
    shift_type: string;
    start_time: string;
    end_time: string;
  };
  employee: {
    id: number;
    full_name: string;
    employee_id: string;
  };
  date: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
}

interface Props {
  assignments: ShiftAssignment[];
  departments: Array<{
    id: number;
    name: string;
  }>;
  currentDate: string;
  filters: {
    department_id?: string;
    view?: 'week' | 'month';
  };
}

export default function ShiftSchedule({ assignments, departments, currentDate, filters }: Props) {
  const [selectedDate, setSelectedDate] = useState(new Date(currentDate));
  const [viewMode, setViewMode] = useState<'week' | 'month'>(filters.view || 'week');
  const [departmentId, setDepartmentId] = useState(filters.department_id || '');

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    if (viewMode === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    }
    setSelectedDate(newDate);

    router.get(route('shifts.schedule'), {
      date: newDate.toISOString().split('T')[0],
      view: viewMode,
      department_id: departmentId,
    }, {
      preserveState: true,
      replace: true,
    });
  };

  const getWeekDays = () => {
    const start = new Date(selectedDate);
    start.setDate(start.getDate() - start.getDay()); // Start from Sunday

    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(start);
      day.setDate(start.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const getAssignmentsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return assignments.filter(assignment => assignment.date === dateStr);
  };

  const getShiftTypeBadge = (type: string) => {
    const variants = {
      morning: 'bg-yellow-100 text-yellow-800',
      afternoon: 'bg-orange-100 text-orange-800',
      night: 'bg-blue-100 text-blue-800',
      emergency: 'bg-red-100 text-red-800',
      on_call: 'bg-purple-100 text-purple-800',
    };

    return variants[type as keyof typeof variants] || variants.morning;
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            Jadwal Shift
          </h2>
          <div className="flex gap-2">
            <Link href={route('shifts.index')}>
              <Button variant="outline">
                <Clock className="h-4 w-4 mr-2" />
                Kelola Shift
              </Button>
            </Link>
            <Link href={route('shift-assignments.create')}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Penugasan
              </Button>
            </Link>
          </div>
        </div>
      }
    >
      <Head title="Jadwal Shift" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {/* Controls */}
          <Card className="mb-6">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Kontrol Jadwal
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant={viewMode === 'week' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('week')}
                  >
                    Minggu
                  </Button>
                  <Button
                    variant={viewMode === 'month' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('month')}
                  >
                    Bulan
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateDate('prev')}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <h3 className="text-lg font-semibold">
                    {selectedDate.toLocaleDateString('id-ID', {
                      month: 'long',
                      year: 'numeric',
                      ...(viewMode === 'week' && { day: 'numeric' })
                    })}
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateDate('next')}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center gap-4">
                  <Select
                    value={departmentId}
                    onValueChange={(value) => setDepartmentId(value)}
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter Departemen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Semua Departemen</SelectItem>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id.toString()}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Calendar View */}
          {viewMode === 'week' && (
            <Card>
              <CardHeader>
                <CardTitle>Jadwal Mingguan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-7 gap-4">
                  {getWeekDays().map((day, index) => {
                    const dayAssignments = getAssignmentsForDate(day);
                    const isToday = day.toDateString() === new Date().toDateString();

                    return (
                      <div key={index} className="space-y-2">
                        <div className={`text-center p-2 rounded ${
                          isToday ? 'bg-blue-100 text-blue-800' : 'bg-gray-50'
                        }`}>
                          <div className="font-semibold">
                            {day.toLocaleDateString('id-ID', { weekday: 'short' })}
                          </div>
                          <div className="text-sm">
                            {day.getDate()}
                          </div>
                        </div>

                        <div className="space-y-1 min-h-[200px]">
                          {dayAssignments.map((assignment) => (
                            <div
                              key={assignment.id}
                              className="p-2 rounded border text-xs"
                            >
                              <div className={`inline-block px-2 py-1 rounded text-xs mb-1 ${
                                getShiftTypeBadge(assignment.shift.shift_type)
                              }`}>
                                {assignment.shift.name}
                              </div>
                              <div className="font-medium">
                                {assignment.employee.full_name}
                              </div>
                              <div className="text-gray-500">
                                {assignment.shift.start_time} - {assignment.shift.end_time}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

---

## Step 6: Create Shift Routes and Controller Integration

### 6.1 Add Shift Routes

Add to `routes/web.php`:

```php
use App\Http\Controllers\ShiftController;
use App\Http\Controllers\ShiftAssignmentController;

Route::middleware(['auth', 'verified'])->group(function () {
    // Shift Management Routes
    Route::resource('shifts', ShiftController::class);
    Route::get('shifts/{shift}/assignments', [ShiftController::class, 'assignments'])
        ->name('shifts.assignments');
    Route::get('shifts/schedule/calendar', [ShiftController::class, 'schedule'])
        ->name('shifts.schedule');

    // Shift Assignment Routes
    Route::resource('shift-assignments', ShiftAssignmentController::class);
    Route::post('shift-assignments/bulk', [ShiftAssignmentController::class, 'bulkStore'])
        ->name('shift-assignments.bulk');
    Route::put('shift-assignments/{assignment}/status', [ShiftAssignmentController::class, 'updateStatus'])
        ->name('shift-assignments.status');
});
```

### 6.2 Create Web Controller for Inertia

Create `app/Http/Controllers/ShiftController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Shift;
use App\Models\Department;
use App\Models\ShiftAssignment;
use App\Http\Requests\StoreShiftRequest;
use App\Http\Requests\UpdateShiftRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class ShiftController extends Controller
{
    public function index(Request $request): Response
    {
        $query = Shift::with(['department'])
            ->withCount('assignments');

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('shift_type', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        if ($request->filled('shift_type')) {
            $query->where('shift_type', $request->shift_type);
        }

        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        $shifts = $query->paginate(15)->withQueryString();
        $departments = Department::where('status', 'active')->get(['id', 'name']);

        return Inertia::render('Shifts/Index', [
            'shifts' => $shifts,
            'departments' => $departments,
            'filters' => $request->only(['search', 'department_id', 'shift_type', 'status']),
        ]);
    }

    public function create(): Response
    {
        $departments = Department::where('status', 'active')->get(['id', 'name']);

        return Inertia::render('Shifts/Form', [
            'departments' => $departments,
            'isEdit' => false,
        ]);
    }

    public function store(StoreShiftRequest $request)
    {
        $shift = Shift::create($request->validated());

        return redirect()->route('shifts.index')
            ->with('success', 'Shift berhasil dibuat');
    }

    public function edit(Shift $shift): Response
    {
        $departments = Department::where('status', 'active')->get(['id', 'name']);

        return Inertia::render('Shifts/Form', [
            'shift' => $shift,
            'departments' => $departments,
            'isEdit' => true,
        ]);
    }

    public function update(UpdateShiftRequest $request, Shift $shift)
    {
        $shift->update($request->validated());

        return redirect()->route('shifts.index')
            ->with('success', 'Shift berhasil diperbarui');
    }

    public function destroy(Shift $shift)
    {
        if ($shift->assignments()->exists()) {
            return back()->withErrors([
                'message' => 'Tidak dapat menghapus shift yang masih memiliki penugasan'
            ]);
        }

        $shift->delete();

        return redirect()->route('shifts.index')
            ->with('success', 'Shift berhasil dihapus');
    }

    public function schedule(Request $request): Response
    {
        $date = $request->get('date', now()->toDateString());
        $view = $request->get('view', 'week');
        $departmentId = $request->get('department_id');

        $startDate = Carbon::parse($date);
        if ($view === 'week') {
            $startDate = $startDate->startOfWeek();
            $endDate = $startDate->copy()->endOfWeek();
        } else {
            $startDate = $startDate->startOfMonth();
            $endDate = $startDate->copy()->endOfMonth();
        }

        $query = ShiftAssignment::with(['shift', 'employee'])
            ->whereBetween('date', [$startDate, $endDate]);

        if ($departmentId) {
            $query->whereHas('shift', function ($q) use ($departmentId) {
                $q->where('department_id', $departmentId);
            });
        }

        $assignments = $query->get();
        $departments = Department::where('status', 'active')->get(['id', 'name']);

        return Inertia::render('Shifts/Schedule', [
            'assignments' => $assignments,
            'departments' => $departments,
            'currentDate' => $date,
            'filters' => $request->only(['department_id', 'view']),
        ]);
    }

    public function assignments(Shift $shift): Response
    {
        $assignments = $shift->assignments()
            ->with(['employee'])
            ->orderBy('date', 'desc')
            ->paginate(15);

        return Inertia::render('Shifts/Assignments', [
            'shift' => $shift->load('department'),
            'assignments' => $assignments,
        ]);
    }
}
```

---

## Summary

In this chapter, we've implemented comprehensive shift scheduling and management with:

### ✅ **Backend Implementation**
- Advanced shift management with Indonesian healthcare patterns
- Comprehensive validation with business rules
- Shift conflict detection and resolution
- Employee assignment and tracking
- Schedule generation and optimization

### ✅ **Frontend Implementation**
- **Shift List Component**: Advanced filtering and management interface
- **Shift Form Component**: Comprehensive shift creation and editing
- **Schedule Calendar**: Interactive weekly/monthly calendar view
- **Responsive Design**: Mobile-friendly scheduling interface
- **Indonesian Localization**: Healthcare-specific terminology

### ✅ **Key Features**
- **Healthcare Shift Types**: Morning, afternoon, night, emergency, on-call
- **Advanced Scheduling**: Conflict detection, minimum staffing, license requirements
- **Visual Calendar**: Interactive schedule management
- **Real-time Updates**: Live schedule updates and notifications
- **Role-based Access**: Permission-based shift management
- **Indonesian Compliance**: Healthcare regulations and shift patterns

### ✅ **Indonesian Healthcare Context**
- **Shift Patterns**: Compliant with Indonesian healthcare standards
- **Medical Licensing**: Integration with professional license requirements
- **Emergency Coverage**: 24/7 emergency shift management
- **Holiday Management**: Indonesian holiday and weekend scheduling
- **Audit Compliance**: Complete shift tracking and reporting

### Key Commands to Remember

```bash
# Backend resources
php artisan make:controller Api/ShiftController --api
php artisan make:controller ShiftController
php artisan make:controller ShiftAssignmentController
php artisan make:request StoreShiftRequest
php artisan make:request UpdateShiftRequest

# Frontend components
# Create React components in resources/js/Pages/Shifts/
# Create shared components for calendar and scheduling
```

Ready for Chapter 8? Let's build employee performance management!
