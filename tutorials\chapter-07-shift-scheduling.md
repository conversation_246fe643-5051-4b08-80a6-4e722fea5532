# Chapter 7: Shift Scheduling and Management

## Overview
In this chapter, we'll implement comprehensive shift scheduling and management with Indonesian healthcare shift patterns. We'll build a system that handles complex hospital scheduling requirements including medical staff rotations, emergency coverage, and compliance with Indonesian healthcare regulations.

## Learning Objectives
- Create shift management system with Indonesian patterns
- Implement employee shift assignment and tracking
- Build shift conflict detection and resolution
- Create shift reporting and analytics
- Implement real-time shift monitoring
- Build responsive React components for shift management

## Prerequisites
- Completed Chapter 1-6
- Understanding of time-based scheduling systems
- Familiarity with Indonesian healthcare shift patterns

## Duration
90-120 minutes

---

## Step 1: Create Shift Controller

### 1.1 Create Shift API Controller

```bash
php artisan make:controller Api/ShiftController --api
```

Edit `app/Http/Controllers/Api/ShiftController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreShiftRequest;
use App\Http\Requests\UpdateShiftRequest;
use App\Http\Resources\ShiftResource;
use App\Models\Shift;
use App\Models\EmployeeShift;
use App\Services\ShiftService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ShiftController extends Controller
{
    protected ShiftService $shiftService;

    public function __construct(ShiftService $shiftService)
    {
        $this->shiftService = $shiftService;
    }

    /**
     * Display a listing of shifts
     */
    public function index(Request $request): JsonResponse
    {
        $query = Shift::with(['department']);

        // Filter by department
        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        // Filter by shift type
        if ($request->filled('shift_type')) {
            $query->where('shift_type', $request->shift_type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'start_time');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $perPage = min($request->get('per_page', 15), 100);
        $shifts = $query->paginate($perPage);

        return response()->json([
            'data' => ShiftResource::collection($shifts->items()),
            'meta' => [
                'current_page' => $shifts->currentPage(),
                'last_page' => $shifts->lastPage(),
                'per_page' => $shifts->perPage(),
                'total' => $shifts->total(),
            ],
        ]);
    }

    /**
     * Store a newly created shift
     */
    public function store(StoreShiftRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // Validate shift times don't overlap with existing shifts
            if ($this->shiftService->hasTimeOverlap($data)) {
                return response()->json([
                    'message' => 'Waktu shift bertabrakan dengan shift yang sudah ada',
                ], 422);
            }

            $shift = Shift::create($data);

            DB::commit();

            return response()->json([
                'message' => 'Shift berhasil ditambahkan',
                'data' => new ShiftResource($shift->load('department')),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menambahkan shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified shift
     */
    public function show(Shift $shift): JsonResponse
    {
        $shift->load([
            'department',
            'employeeShifts' => function ($q) {
                $q->with(['employee.position', 'employee.employeeType'])
                  ->where('date', '>=', now()->subDays(7))
                  ->orderBy('date', 'desc');
            }
        ]);

        return response()->json([
            'data' => new ShiftResource($shift),
        ]);
    }

    /**
     * Update the specified shift
     */
    public function update(UpdateShiftRequest $request, Shift $shift): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // Validate shift times don't overlap (excluding current shift)
            if ($this->shiftService->hasTimeOverlap($data, $shift->id)) {
                return response()->json([
                    'message' => 'Waktu shift bertabrakan dengan shift yang sudah ada',
                ], 422);
            }

            $shift->update($data);

            DB::commit();

            return response()->json([
                'message' => 'Data shift berhasil diperbarui',
                'data' => new ShiftResource($shift->load('department')),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat memperbarui data shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified shift
     */
    public function destroy(Shift $shift): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Check if shift has active assignments
            $activeAssignments = $shift->employeeShifts()
                ->where('date', '>=', now())
                ->where('status', '!=', 'cancelled')
                ->exists();

            if ($activeAssignments) {
                return response()->json([
                    'message' => 'Tidak dapat menghapus shift yang memiliki penugasan aktif',
                ], 422);
            }

            $shift->delete();

            DB::commit();

            return response()->json([
                'message' => 'Shift berhasil dihapus',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menghapus shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get shift schedule for a date range
     */
    public function schedule(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'department_id' => 'nullable|exists:departments,id',
            'employee_id' => 'nullable|exists:employees,id',
        ]);

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $query = EmployeeShift::with([
            'employee.position',
            'employee.employeeType',
            'shift',
            'shift.department'
        ])
        ->whereBetween('date', [$startDate, $endDate]);

        // Filter by department
        if ($request->filled('department_id')) {
            $query->whereHas('shift', function ($q) use ($request) {
                $q->where('department_id', $request->department_id);
            });
        }

        // Filter by employee
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        $employeeShifts = $query->orderBy('date')->orderBy('shift_id')->get();

        // Group by date for calendar view
        $schedule = $employeeShifts->groupBy(function ($item) {
            return $item->date->format('Y-m-d');
        })->map(function ($dayShifts) {
            return $dayShifts->groupBy('shift_id')->map(function ($shiftEmployees) {
                $shift = $shiftEmployees->first()->shift;
                return [
                    'shift' => [
                        'id' => $shift->id,
                        'name' => $shift->name,
                        'start_time' => $shift->start_time,
                        'end_time' => $shift->end_time,
                        'shift_type' => $shift->shift_type,
                        'indonesian_shift_type' => $shift->indonesian_shift_type,
                    ],
                    'employees' => $shiftEmployees->map(function ($employeeShift) {
                        return [
                            'id' => $employeeShift->id,
                            'employee' => [
                                'id' => $employeeShift->employee->id,
                                'full_name' => $employeeShift->employee->full_name,
                                'employee_number' => $employeeShift->employee->employee_number,
                                'position' => $employeeShift->employee->position?->title,
                                'employee_type' => $employeeShift->employee->employeeType?->indonesian_name,
                            ],
                            'status' => $employeeShift->status,
                            'indonesian_status' => $employeeShift->indonesian_status,
                            'check_in_time' => $employeeShift->check_in_time?->format('H:i'),
                            'check_out_time' => $employeeShift->check_out_time?->format('H:i'),
                            'total_working_hours' => $employeeShift->total_working_hours,
                            'notes' => $employeeShift->notes,
                        ];
                    }),
                ];
            });
        });

        return response()->json([
            'data' => $schedule,
            'summary' => [
                'total_shifts' => $employeeShifts->count(),
                'unique_employees' => $employeeShifts->pluck('employee_id')->unique()->count(),
                'departments_involved' => $employeeShifts->pluck('shift.department_id')->unique()->count(),
            ],
        ]);
    }

    /**
     * Assign employees to shift
     */
    public function assignEmployees(Request $request, Shift $shift): JsonResponse
    {
        $request->validate([
            'date' => 'required|date|after_or_equal:today',
            'employee_ids' => 'required|array|min:1',
            'employee_ids.*' => 'exists:employees,id',
        ]);

        try {
            DB::beginTransaction();

            $date = Carbon::parse($request->date);
            $employeeIds = $request->employee_ids;

            // Check for conflicts
            $conflicts = $this->shiftService->checkShiftConflicts($employeeIds, $date, $shift);
            
            if (!empty($conflicts)) {
                return response()->json([
                    'message' => 'Terdapat konflik jadwal shift',
                    'conflicts' => $conflicts,
                ], 422);
            }

            // Create employee shift assignments
            $assignments = [];
            foreach ($employeeIds as $employeeId) {
                $assignments[] = EmployeeShift::create([
                    'employee_id' => $employeeId,
                    'shift_id' => $shift->id,
                    'date' => $date,
                    'status' => 'scheduled',
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Karyawan berhasil ditugaskan ke shift',
                'data' => $assignments,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menugaskan karyawan',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get shift statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $stats = [
            'total_shifts' => Shift::count(),
            'active_shifts' => Shift::where('status', 'active')->count(),
            'total_assignments' => EmployeeShift::whereBetween('date', [$startDate, $endDate])->count(),
            'completed_shifts' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->where('status', 'completed')->count(),
            'missed_shifts' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->where('status', 'absent')->count(),
            'shifts_by_type' => Shift::select('shift_type')
                ->groupBy('shift_type')
                ->selectRaw('shift_type, count(*) as count')
                ->get()
                ->map(function ($item) {
                    return [
                        'type' => $item->shift_type,
                        'indonesian_type' => $item->indonesian_shift_type,
                        'count' => $item->count,
                    ];
                }),
            'shifts_by_department' => Shift::with('department')
                ->select('department_id')
                ->groupBy('department_id')
                ->selectRaw('department_id, count(*) as count')
                ->get()
                ->map(function ($item) {
                    return [
                        'department' => $item->department->name,
                        'count' => $item->count,
                    ];
                }),
            'average_working_hours' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->whereNotNull('total_working_hours')
                ->avg('total_working_hours'),
        ];

        return response()->json(['data' => $stats]);
    }
}
```

---

## Step 2: Create Shift Request Validation Classes

### 2.1 Create Store Shift Request

```bash
php artisan make:request StoreShiftRequest
```

Edit `app/Http/Requests/StoreShiftRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class StoreShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create shifts');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Basic Shift Information
            'name' => [
                'required',
                'string',
                'max:100',
                'unique:shifts,name',
            ],
            'shift_type' => [
                'required',
                Rule::in(['morning', 'afternoon', 'night', 'emergency', 'on_call']),
            ],
            'description' => [
                'nullable',
                'string',
                'max:500',
            ],

            // Time Configuration
            'start_time' => [
                'required',
                'date_format:H:i',
            ],
            'end_time' => [
                'required',
                'date_format:H:i',
                'after:start_time',
            ],
            'break_duration' => [
                'nullable',
                'integer',
                'min:0',
                'max:480', // Max 8 hours break
            ],
            'working_hours' => [
                'required',
                'numeric',
                'min:1',
                'max:24',
            ],

            // Department and Requirements
            'department_id' => [
                'required',
                'exists:departments,id',
            ],
            'required_employees' => [
                'required',
                'integer',
                'min:1',
                'max:50',
            ],
            'minimum_employees' => [
                'required',
                'integer',
                'min:1',
                'lte:required_employees',
            ],

            // Indonesian Healthcare Specific
            'is_medical_shift' => [
                'required',
                'boolean',
            ],
            'requires_license' => [
                'required',
                'boolean',
            ],
            'emergency_coverage' => [
                'required',
                'boolean',
            ],
            'weekend_applicable' => [
                'required',
                'boolean',
            ],
            'holiday_applicable' => [
                'required',
                'boolean',
            ],

            // Shift Pattern
            'rotation_pattern' => [
                'nullable',
                Rule::in(['daily', 'weekly', 'monthly', 'custom']),
            ],
            'max_consecutive_days' => [
                'nullable',
                'integer',
                'min:1',
                'max:14',
            ],
            'min_rest_hours' => [
                'nullable',
                'integer',
                'min:8',
                'max:72',
            ],

            // Status
            'is_active' => [
                'required',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama shift wajib diisi.',
            'name.unique' => 'Nama shift sudah digunakan.',
            'shift_type.required' => 'Tipe shift wajib dipilih.',
            'shift_type.in' => 'Tipe shift tidak valid.',
            'start_time.required' => 'Waktu mulai wajib diisi.',
            'start_time.date_format' => 'Format waktu mulai tidak valid (HH:MM).',
            'end_time.required' => 'Waktu selesai wajib diisi.',
            'end_time.date_format' => 'Format waktu selesai tidak valid (HH:MM).',
            'end_time.after' => 'Waktu selesai harus setelah waktu mulai.',
            'break_duration.integer' => 'Durasi istirahat harus berupa angka.',
            'break_duration.max' => 'Durasi istirahat maksimal 8 jam.',
            'working_hours.required' => 'Jam kerja wajib diisi.',
            'working_hours.min' => 'Jam kerja minimal 1 jam.',
            'working_hours.max' => 'Jam kerja maksimal 24 jam.',
            'department_id.required' => 'Departemen wajib dipilih.',
            'department_id.exists' => 'Departemen tidak ditemukan.',
            'required_employees.required' => 'Jumlah karyawan yang dibutuhkan wajib diisi.',
            'required_employees.min' => 'Minimal 1 karyawan dibutuhkan.',
            'required_employees.max' => 'Maksimal 50 karyawan per shift.',
            'minimum_employees.required' => 'Jumlah minimal karyawan wajib diisi.',
            'minimum_employees.lte' => 'Jumlah minimal tidak boleh lebih dari jumlah yang dibutuhkan.',
            'is_medical_shift.required' => 'Status shift medis wajib dipilih.',
            'requires_license.required' => 'Status persyaratan lisensi wajib dipilih.',
            'emergency_coverage.required' => 'Status cakupan darurat wajib dipilih.',
            'weekend_applicable.required' => 'Status berlaku akhir pekan wajib dipilih.',
            'holiday_applicable.required' => 'Status berlaku hari libur wajib dipilih.',
            'rotation_pattern.in' => 'Pola rotasi tidak valid.',
            'max_consecutive_days.min' => 'Maksimal hari berturut-turut minimal 1 hari.',
            'max_consecutive_days.max' => 'Maksimal hari berturut-turut maksimal 14 hari.',
            'min_rest_hours.min' => 'Minimal jam istirahat 8 jam.',
            'min_rest_hours.max' => 'Maksimal jam istirahat 72 jam.',
            'is_active.required' => 'Status aktif wajib dipilih.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'nama shift',
            'shift_type' => 'tipe shift',
            'description' => 'deskripsi',
            'start_time' => 'waktu mulai',
            'end_time' => 'waktu selesai',
            'break_duration' => 'durasi istirahat',
            'working_hours' => 'jam kerja',
            'department_id' => 'departemen',
            'required_employees' => 'jumlah karyawan yang dibutuhkan',
            'minimum_employees' => 'jumlah minimal karyawan',
            'is_medical_shift' => 'shift medis',
            'requires_license' => 'memerlukan lisensi',
            'emergency_coverage' => 'cakupan darurat',
            'weekend_applicable' => 'berlaku akhir pekan',
            'holiday_applicable' => 'berlaku hari libur',
            'rotation_pattern' => 'pola rotasi',
            'max_consecutive_days' => 'maksimal hari berturut-turut',
            'min_rest_hours' => 'minimal jam istirahat',
            'is_active' => 'status aktif',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate time logic
            if ($this->start_time && $this->end_time) {
                $start = Carbon::createFromFormat('H:i', $this->start_time);
                $end = Carbon::createFromFormat('H:i', $this->end_time);

                // Handle overnight shifts
                if ($end->lessThan($start)) {
                    $end->addDay();
                }

                $actualHours = $start->diffInHours($end);
                $breakHours = ($this->break_duration ?? 0) / 60;
                $workingHours = $actualHours - $breakHours;

                if (abs($workingHours - ($this->working_hours ?? 0)) > 0.5) {
                    $validator->errors()->add('working_hours', 'Jam kerja tidak sesuai dengan waktu mulai dan selesai.');
                }
            }

            // Validate medical shift requirements
            if ($this->is_medical_shift && !$this->requires_license) {
                $validator->errors()->add('requires_license', 'Shift medis harus memerlukan lisensi.');
            }

            // Validate emergency coverage for night shifts
            if ($this->shift_type === 'night' && !$this->emergency_coverage) {
                $validator->errors()->add('emergency_coverage', 'Shift malam harus memiliki cakupan darurat.');
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Http\Exceptions\HttpResponseException(
            response()->json([
                'message' => 'Data shift yang diberikan tidak valid.',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
```

### 2.2 Create Update Shift Request

```bash
php artisan make:request UpdateShiftRequest
```

Edit `app/Http/Requests/UpdateShiftRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class UpdateShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $shift = $this->route('shift');

        // Super Admin can update any shift
        if ($this->user()->hasRole('Super Admin')) {
            return true;
        }

        // Department Head can update shifts in their department
        if ($this->user()->hasRole('Department Head')) {
            return $this->user()->employee->department_id === $shift->department_id;
        }

        // Check permission
        return $this->user()->can('update shifts');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $shift = $this->route('shift');

        return [
            // Basic Shift Information
            'name' => [
                'sometimes',
                'required',
                'string',
                'max:100',
                Rule::unique('shifts', 'name')->ignore($shift->id),
            ],
            'shift_type' => [
                'sometimes',
                'required',
                Rule::in(['morning', 'afternoon', 'night', 'emergency', 'on_call']),
            ],
            'description' => [
                'sometimes',
                'nullable',
                'string',
                'max:500',
            ],

            // Time Configuration
            'start_time' => [
                'sometimes',
                'required',
                'date_format:H:i',
            ],
            'end_time' => [
                'sometimes',
                'required',
                'date_format:H:i',
                'after:start_time',
            ],
            'break_duration' => [
                'sometimes',
                'nullable',
                'integer',
                'min:0',
                'max:480',
            ],
            'working_hours' => [
                'sometimes',
                'required',
                'numeric',
                'min:1',
                'max:24',
            ],

            // Department and Requirements
            'department_id' => [
                'sometimes',
                'required',
                'exists:departments,id',
                $this->authorizeDepartmentChange(),
            ],
            'required_employees' => [
                'sometimes',
                'required',
                'integer',
                'min:1',
                'max:50',
            ],
            'minimum_employees' => [
                'sometimes',
                'required',
                'integer',
                'min:1',
                'lte:required_employees',
            ],

            // Indonesian Healthcare Specific
            'is_medical_shift' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'requires_license' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'emergency_coverage' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'weekend_applicable' => [
                'sometimes',
                'required',
                'boolean',
            ],
            'holiday_applicable' => [
                'sometimes',
                'required',
                'boolean',
            ],

            // Shift Pattern
            'rotation_pattern' => [
                'sometimes',
                'nullable',
                Rule::in(['daily', 'weekly', 'monthly', 'custom']),
            ],
            'max_consecutive_days' => [
                'sometimes',
                'nullable',
                'integer',
                'min:1',
                'max:14',
            ],
            'min_rest_hours' => [
                'sometimes',
                'nullable',
                'integer',
                'min:8',
                'max:72',
            ],

            // Status
            'is_active' => [
                'sometimes',
                'required',
                'boolean',
            ],
        ];
    }

    /**
     * Check if user can change department
     */
    private function authorizeDepartmentChange(): string
    {
        if (!$this->user()->can('update shift department')) {
            return 'prohibited';
        }

        return 'sometimes';
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama shift wajib diisi.',
            'name.unique' => 'Nama shift sudah digunakan.',
            'shift_type.required' => 'Tipe shift wajib dipilih.',
            'shift_type.in' => 'Tipe shift tidak valid.',
            'start_time.required' => 'Waktu mulai wajib diisi.',
            'start_time.date_format' => 'Format waktu mulai tidak valid (HH:MM).',
            'end_time.required' => 'Waktu selesai wajib diisi.',
            'end_time.date_format' => 'Format waktu selesai tidak valid (HH:MM).',
            'end_time.after' => 'Waktu selesai harus setelah waktu mulai.',
            'break_duration.integer' => 'Durasi istirahat harus berupa angka.',
            'break_duration.max' => 'Durasi istirahat maksimal 8 jam.',
            'working_hours.required' => 'Jam kerja wajib diisi.',
            'working_hours.min' => 'Jam kerja minimal 1 jam.',
            'working_hours.max' => 'Jam kerja maksimal 24 jam.',
            'department_id.required' => 'Departemen wajib dipilih.',
            'department_id.exists' => 'Departemen tidak ditemukan.',
            'department_id.prohibited' => 'Anda tidak memiliki izin untuk mengubah departemen shift.',
            'required_employees.required' => 'Jumlah karyawan yang dibutuhkan wajib diisi.',
            'required_employees.min' => 'Minimal 1 karyawan dibutuhkan.',
            'required_employees.max' => 'Maksimal 50 karyawan per shift.',
            'minimum_employees.required' => 'Jumlah minimal karyawan wajib diisi.',
            'minimum_employees.lte' => 'Jumlah minimal tidak boleh lebih dari jumlah yang dibutuhkan.',
            'is_medical_shift.required' => 'Status shift medis wajib dipilih.',
            'requires_license.required' => 'Status persyaratan lisensi wajib dipilih.',
            'emergency_coverage.required' => 'Status cakupan darurat wajib dipilih.',
            'weekend_applicable.required' => 'Status berlaku akhir pekan wajib dipilih.',
            'holiday_applicable.required' => 'Status berlaku hari libur wajib dipilih.',
            'rotation_pattern.in' => 'Pola rotasi tidak valid.',
            'max_consecutive_days.min' => 'Maksimal hari berturut-turut minimal 1 hari.',
            'max_consecutive_days.max' => 'Maksimal hari berturut-turut maksimal 14 hari.',
            'min_rest_hours.min' => 'Minimal jam istirahat 8 jam.',
            'min_rest_hours.max' => 'Maksimal jam istirahat 72 jam.',
            'is_active.required' => 'Status aktif wajib dipilih.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'nama shift',
            'shift_type' => 'tipe shift',
            'description' => 'deskripsi',
            'start_time' => 'waktu mulai',
            'end_time' => 'waktu selesai',
            'break_duration' => 'durasi istirahat',
            'working_hours' => 'jam kerja',
            'department_id' => 'departemen',
            'required_employees' => 'jumlah karyawan yang dibutuhkan',
            'minimum_employees' => 'jumlah minimal karyawan',
            'is_medical_shift' => 'shift medis',
            'requires_license' => 'memerlukan lisensi',
            'emergency_coverage' => 'cakupan darurat',
            'weekend_applicable' => 'berlaku akhir pekan',
            'holiday_applicable' => 'berlaku hari libur',
            'rotation_pattern' => 'pola rotasi',
            'max_consecutive_days' => 'maksimal hari berturut-turut',
            'min_rest_hours' => 'minimal jam istirahat',
            'is_active' => 'status aktif',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate time logic if both times are provided
            if ($this->start_time && $this->end_time) {
                $start = Carbon::createFromFormat('H:i', $this->start_time);
                $end = Carbon::createFromFormat('H:i', $this->end_time);

                if ($end->lessThan($start)) {
                    $end->addDay();
                }

                $actualHours = $start->diffInHours($end);
                $breakHours = ($this->break_duration ?? 0) / 60;
                $workingHours = $actualHours - $breakHours;

                if ($this->working_hours && abs($workingHours - $this->working_hours) > 0.5) {
                    $validator->errors()->add('working_hours', 'Jam kerja tidak sesuai dengan waktu mulai dan selesai.');
                }
            }

            // Validate medical shift requirements
            if ($this->is_medical_shift && isset($this->requires_license) && !$this->requires_license) {
                $validator->errors()->add('requires_license', 'Shift medis harus memerlukan lisensi.');
            }

            // Validate emergency coverage for night shifts
            if ($this->shift_type === 'night' && isset($this->emergency_coverage) && !$this->emergency_coverage) {
                $validator->errors()->add('emergency_coverage', 'Shift malam harus memiliki cakupan darurat.');
            }

            // Check if shift has active assignments before major changes
            $shift = $this->route('shift');
            if ($shift && $shift->employeeShifts()->where('date', '>=', now())->exists()) {
                $restrictedFields = ['start_time', 'end_time', 'working_hours', 'department_id'];
                foreach ($restrictedFields as $field) {
                    if ($this->has($field)) {
                        $validator->errors()->add($field, 'Tidak dapat mengubah ' . $this->attributes()[$field] . ' karena shift memiliki jadwal aktif.');
                    }
                }
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Http\Exceptions\HttpResponseException(
            response()->json([
                'message' => 'Data shift yang diberikan tidak valid.',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
```

---

## Step 3: Create Shift Service Layer

### 3.1 Create Shift Service

```bash
php artisan make:service ShiftService
```

Create `app/Services/ShiftService.php`:

```php
<?php

namespace App\Services;

use App\Models\Shift;
use App\Models\Employee;
use App\Models\EmployeeShift;
use App\Models\Department;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class ShiftService
{
    /**
     * Create a new shift
     */
    public function createShift(array $data): Shift
    {
        return DB::transaction(function () use ($data) {
            // Calculate working hours if not provided
            if (!isset($data['working_hours'])) {
                $data['working_hours'] = $this->calculateWorkingHours(
                    $data['start_time'],
                    $data['end_time'],
                    $data['break_duration'] ?? 0
                );
            }

            // Create shift
            $shift = Shift::create($data);

            // Log shift creation
            activity()
                ->performedOn($shift)
                ->causedBy(auth()->user())
                ->withProperties(['action' => 'created'])
                ->log('Shift created: ' . $shift->name);

            return $shift->load('department');
        });
    }

    /**
     * Update shift information
     */
    public function updateShift(Shift $shift, array $data): Shift
    {
        return DB::transaction(function () use ($shift, $data) {
            // Check if shift has active assignments
            $hasActiveAssignments = $shift->employeeShifts()
                ->where('date', '>=', now()->toDateString())
                ->exists();

            if ($hasActiveAssignments) {
                // Restrict certain changes if shift has active assignments
                $restrictedFields = ['start_time', 'end_time', 'working_hours', 'department_id'];
                foreach ($restrictedFields as $field) {
                    if (isset($data[$field])) {
                        throw new \Exception("Cannot modify {$field} because shift has active assignments.");
                    }
                }
            }

            // Recalculate working hours if time changed
            if (isset($data['start_time']) || isset($data['end_time']) || isset($data['break_duration'])) {
                $data['working_hours'] = $this->calculateWorkingHours(
                    $data['start_time'] ?? $shift->start_time,
                    $data['end_time'] ?? $shift->end_time,
                    $data['break_duration'] ?? $shift->break_duration ?? 0
                );
            }

            // Track changes for audit
            $originalData = $shift->toArray();

            // Update shift
            $shift->update($data);

            // Log shift update
            $changes = array_diff_assoc($shift->toArray(), $originalData);
            if (!empty($changes)) {
                activity()
                    ->performedOn($shift)
                    ->causedBy(auth()->user())
                    ->withProperties([
                        'action' => 'updated',
                        'changes' => $changes,
                        'original' => $originalData
                    ])
                    ->log('Shift updated: ' . $shift->name);
            }

            return $shift->load('department');
        });
    }

    /**
     * Delete shift
     */
    public function deleteShift(Shift $shift): bool
    {
        return DB::transaction(function () use ($shift) {
            // Check if shift has any assignments
            if ($shift->employeeShifts()->exists()) {
                throw new \Exception('Cannot delete shift with existing assignments. Please remove all assignments first.');
            }

            // Log shift deletion
            activity()
                ->performedOn($shift)
                ->causedBy(auth()->user())
                ->withProperties(['action' => 'deleted'])
                ->log('Shift deleted: ' . $shift->name);

            return $shift->delete();
        });
    }

    /**
     * Assign employees to shift for specific dates
     */
    public function assignEmployeesToShift(Shift $shift, array $employeeIds, array $dates): Collection
    {
        return DB::transaction(function () use ($shift, $employeeIds, $dates) {
            $assignments = collect();

            foreach ($dates as $date) {
                $carbonDate = Carbon::parse($date);

                foreach ($employeeIds as $employeeId) {
                    $employee = Employee::find($employeeId);

                    // Validate assignment
                    $this->validateShiftAssignment($shift, $employee, $carbonDate);

                    // Check for conflicts
                    $conflicts = $this->checkShiftConflicts($employee, $carbonDate, $shift);
                    if ($conflicts->isNotEmpty()) {
                        throw new \Exception("Employee {$employee->full_name} has shift conflicts on {$carbonDate->format('Y-m-d')}");
                    }

                    // Create assignment
                    $assignment = EmployeeShift::create([
                        'employee_id' => $employeeId,
                        'shift_id' => $shift->id,
                        'date' => $carbonDate,
                        'status' => 'scheduled',
                        'assigned_by' => auth()->id(),
                        'assigned_at' => now(),
                    ]);

                    $assignments->push($assignment);

                    // Log assignment
                    activity()
                        ->performedOn($assignment)
                        ->causedBy(auth()->user())
                        ->withProperties([
                            'action' => 'assigned',
                            'employee_name' => $employee->full_name,
                            'shift_name' => $shift->name,
                            'date' => $carbonDate->format('Y-m-d')
                        ])
                        ->log("Employee assigned to shift: {$employee->full_name} to {$shift->name} on {$carbonDate->format('Y-m-d')}");
                }
            }

            return $assignments;
        });
    }

    /**
     * Generate shift schedule for a period
     */
    public function generateShiftSchedule(array $shiftIds, Carbon $startDate, Carbon $endDate, array $options = []): Collection
    {
        return DB::transaction(function () use ($shiftIds, $startDate, $endDate, $options) {
            $shifts = Shift::whereIn('id', $shiftIds)->with('department')->get();
            $schedule = collect();

            $period = CarbonPeriod::create($startDate, $endDate);

            foreach ($period as $date) {
                // Skip weekends if not applicable
                if ($date->isWeekend() && !($options['include_weekends'] ?? true)) {
                    continue;
                }

                foreach ($shifts as $shift) {
                    // Skip if shift not applicable for weekends/holidays
                    if ($date->isWeekend() && !$shift->weekend_applicable) {
                        continue;
                    }

                    // Get available employees for this shift
                    $availableEmployees = $this->getAvailableEmployees($shift, $date);

                    // Auto-assign if requested
                    if ($options['auto_assign'] ?? false) {
                        $assignedEmployees = $this->autoAssignEmployees($shift, $availableEmployees, $date);

                        foreach ($assignedEmployees as $employee) {
                            $assignment = EmployeeShift::create([
                                'employee_id' => $employee->id,
                                'shift_id' => $shift->id,
                                'date' => $date,
                                'status' => 'scheduled',
                                'assigned_by' => auth()->id(),
                                'assigned_at' => now(),
                            ]);

                            $schedule->push($assignment);
                        }
                    } else {
                        // Just create schedule template
                        $schedule->push([
                            'shift' => $shift,
                            'date' => $date,
                            'available_employees' => $availableEmployees,
                            'required_employees' => $shift->required_employees,
                            'minimum_employees' => $shift->minimum_employees,
                        ]);
                    }
                }
            }

            // Log schedule generation
            activity()
                ->causedBy(auth()->user())
                ->withProperties([
                    'action' => 'schedule_generated',
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'shifts_count' => count($shiftIds),
                    'assignments_count' => $schedule->count()
                ])
                ->log("Shift schedule generated for {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");

            return $schedule;
        });
    }

    /**
     * Check for shift conflicts
     */
    public function checkShiftConflicts(Employee $employee, Carbon $date, Shift $excludeShift = null): Collection
    {
        $query = EmployeeShift::where('employee_id', $employee->id)
            ->where('date', $date)
            ->with('shift');

        if ($excludeShift) {
            $query->where('shift_id', '!=', $excludeShift->id);
        }

        $existingShifts = $query->get();
        $conflicts = collect();

        foreach ($existingShifts as $existingShift) {
            // Check for time overlap
            if ($this->shiftsOverlap($excludeShift ?? new Shift(), $existingShift->shift)) {
                $conflicts->push([
                    'type' => 'time_overlap',
                    'existing_shift' => $existingShift,
                    'message' => "Time overlap with {$existingShift->shift->name}"
                ]);
            }

            // Check minimum rest hours
            if ($this->violatesRestHours($excludeShift ?? new Shift(), $existingShift->shift, $date)) {
                $conflicts->push([
                    'type' => 'insufficient_rest',
                    'existing_shift' => $existingShift,
                    'message' => "Insufficient rest hours between shifts"
                ]);
            }
        }

        // Check consecutive days limit
        $consecutiveDays = $this->getConsecutiveDays($employee, $date);
        if ($consecutiveDays >= ($excludeShift->max_consecutive_days ?? 7)) {
            $conflicts->push([
                'type' => 'consecutive_days_limit',
                'message' => "Exceeds maximum consecutive days limit"
            ]);
        }

        return $conflicts;
    }

    /**
     * Get available employees for a shift
     */
    public function getAvailableEmployees(Shift $shift, Carbon $date): Collection
    {
        $query = Employee::where('employment_status', 'active')
            ->where('department_id', $shift->department_id);

        // Filter by medical staff requirement
        if ($shift->is_medical_shift) {
            $query->whereHas('employeeType', function ($q) {
                $q->where('is_medical_staff', true);
            });
        }

        // Filter by license requirement
        if ($shift->requires_license) {
            $query->whereHas('licenses', function ($q) use ($date) {
                $q->where('status', 'active')
                  ->where('expiry_date', '>', $date);
            });
        }

        $employees = $query->get();

        // Filter out employees with conflicts
        return $employees->filter(function ($employee) use ($shift, $date) {
            return $this->checkShiftConflicts($employee, $date, $shift)->isEmpty();
        });
    }

    /**
     * Auto-assign employees to shift
     */
    private function autoAssignEmployees(Shift $shift, Collection $availableEmployees, Carbon $date): Collection
    {
        // Prioritize employees based on various factors
        $prioritizedEmployees = $availableEmployees->sortBy(function ($employee) use ($date) {
            $score = 0;

            // Prefer employees with fewer recent assignments
            $recentAssignments = EmployeeShift::where('employee_id', $employee->id)
                ->where('date', '>=', $date->copy()->subDays(7))
                ->count();
            $score += (10 - $recentAssignments);

            // Prefer senior employees for medical shifts
            $score += $employee->years_of_service;

            // Prefer employees with relevant licenses
            if ($employee->licenses()->where('status', 'active')->exists()) {
                $score += 5;
            }

            return -$score; // Negative for descending sort
        });

        // Select required number of employees
        return $prioritizedEmployees->take($shift->required_employees);
    }

    /**
     * Calculate working hours between start and end time
     */
    private function calculateWorkingHours(string $startTime, string $endTime, int $breakDuration = 0): float
    {
        $start = Carbon::createFromFormat('H:i', $startTime);
        $end = Carbon::createFromFormat('H:i', $endTime);

        // Handle overnight shifts
        if ($end->lessThan($start)) {
            $end->addDay();
        }

        $totalMinutes = $start->diffInMinutes($end);
        $workingMinutes = $totalMinutes - $breakDuration;

        return round($workingMinutes / 60, 2);
    }

    /**
     * Check if two shifts overlap in time
     */
    private function shiftsOverlap(Shift $shift1, Shift $shift2): bool
    {
        $start1 = Carbon::createFromFormat('H:i', $shift1->start_time);
        $end1 = Carbon::createFromFormat('H:i', $shift1->end_time);
        $start2 = Carbon::createFromFormat('H:i', $shift2->start_time);
        $end2 = Carbon::createFromFormat('H:i', $shift2->end_time);

        // Handle overnight shifts
        if ($end1->lessThan($start1)) $end1->addDay();
        if ($end2->lessThan($start2)) $end2->addDay();

        return $start1->lessThan($end2) && $start2->lessThan($end1);
    }

    /**
     * Check if assignment violates minimum rest hours
     */
    private function violatesRestHours(Shift $newShift, Shift $existingShift, Carbon $date): bool
    {
        $minRestHours = max($newShift->min_rest_hours ?? 8, $existingShift->min_rest_hours ?? 8);

        $existingEnd = Carbon::createFromFormat('H:i', $existingShift->end_time);
        $newStart = Carbon::createFromFormat('H:i', $newShift->start_time);

        // Handle overnight shifts
        if ($existingEnd->lessThan(Carbon::createFromFormat('H:i', $existingShift->start_time))) {
            $existingEnd->addDay();
        }

        $restHours = $existingEnd->diffInHours($newStart);

        return $restHours < $minRestHours;
    }

    /**
     * Get consecutive working days for employee
     */
    private function getConsecutiveDays(Employee $employee, Carbon $date): int
    {
        $consecutiveDays = 0;
        $checkDate = $date->copy()->subDay();

        while (true) {
            $hasShift = EmployeeShift::where('employee_id', $employee->id)
                ->where('date', $checkDate)
                ->exists();

            if (!$hasShift) {
                break;
            }

            $consecutiveDays++;
            $checkDate->subDay();

            // Prevent infinite loop
            if ($consecutiveDays > 30) {
                break;
            }
        }

        return $consecutiveDays;
    }

    /**
     * Validate shift assignment
     */
    private function validateShiftAssignment(Shift $shift, Employee $employee, Carbon $date): void
    {
        // Check if employee is active
        if ($employee->employment_status !== 'active') {
            throw new \Exception("Employee {$employee->full_name} is not active.");
        }

        // Check department match
        if ($employee->department_id !== $shift->department_id) {
            throw new \Exception("Employee {$employee->full_name} is not in the correct department for this shift.");
        }

        // Check medical staff requirement
        if ($shift->is_medical_shift && !$employee->employeeType->is_medical_staff) {
            throw new \Exception("Employee {$employee->full_name} is not medical staff.");
        }

        // Check license requirement
        if ($shift->requires_license) {
            $hasValidLicense = $employee->licenses()
                ->where('status', 'active')
                ->where('expiry_date', '>', $date)
                ->exists();

            if (!$hasValidLicense) {
                throw new \Exception("Employee {$employee->full_name} does not have a valid license.");
            }
        }

        // Check if shift is active
        if (!$shift->is_active) {
            throw new \Exception("Shift {$shift->name} is not active.");
        }

        // Check weekend/holiday applicability
        if ($date->isWeekend() && !$shift->weekend_applicable) {
            throw new \Exception("Shift {$shift->name} is not applicable for weekends.");
        }
    }

    /**
     * Get shift statistics
     */
    public function getShiftStatistics(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'total_shifts' => Shift::count(),
            'active_shifts' => Shift::where('is_active', true)->count(),
            'medical_shifts' => Shift::where('is_medical_shift', true)->count(),
            'total_assignments' => EmployeeShift::whereBetween('date', [$startDate, $endDate])->count(),
            'completed_shifts' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->where('status', 'completed')->count(),
            'missed_shifts' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->where('status', 'missed')->count(),
            'shifts_by_type' => Shift::select('shift_type')
                ->groupBy('shift_type')
                ->selectRaw('shift_type, count(*) as count')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->shift_type => $item->count];
                }),
            'shifts_by_department' => Shift::with('department')
                ->select('department_id')
                ->groupBy('department_id')
                ->selectRaw('department_id, count(*) as count')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->department->name ?? 'Unknown' => $item->count];
                }),
            'average_working_hours' => EmployeeShift::whereBetween('date', [$startDate, $endDate])
                ->whereNotNull('total_working_hours')
                ->avg('total_working_hours'),
            'coverage_percentage' => $this->calculateCoveragePercentage($startDate, $endDate),
        ];
    }

    /**
     * Calculate shift coverage percentage
     */
    private function calculateCoveragePercentage(Carbon $startDate, Carbon $endDate): float
    {
        $totalRequiredSlots = 0;
        $totalFilledSlots = 0;

        $period = CarbonPeriod::create($startDate, $endDate);
        $shifts = Shift::where('is_active', true)->get();

        foreach ($period as $date) {
            foreach ($shifts as $shift) {
                if ($date->isWeekend() && !$shift->weekend_applicable) {
                    continue;
                }

                $totalRequiredSlots += $shift->required_employees;

                $filledSlots = EmployeeShift::where('shift_id', $shift->id)
                    ->where('date', $date)
                    ->count();

                $totalFilledSlots += $filledSlots;
            }
        }

        return $totalRequiredSlots > 0 ? round(($totalFilledSlots / $totalRequiredSlots) * 100, 2) : 0;
    }
}
```
