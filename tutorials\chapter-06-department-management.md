# Chapter 6: Department Management

## Overview
In this chapter, we'll implement comprehensive department management with hierarchical structures, location management, and organizational chart visualization. We'll build a system that handles complex hospital organizational structures with Indonesian healthcare context.

## Learning Objectives
- Create hierarchical department management
- Implement department head assignment and management
- Build location and capacity management
- Create organizational chart visualization
- Implement department-based access control
- Build responsive React components for department management

## Prerequisites
- Completed Chapter 1-5
- Understanding of hierarchical data structures
- Familiarity with tree-like data visualization

## Duration
90-120 minutes

---

## Step 1: Create Department Controller

### 1.1 Create Department API Controller

```bash
php artisan make:controller Api/DepartmentController --api
```

Edit `app/Http/Controllers/Api/DepartmentController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDepartmentRequest;
use App\Http\Requests\UpdateDepartmentRequest;
use App\Http\Resources\DepartmentResource;
use App\Models\Department;
use App\Models\Employee;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DepartmentController extends Controller
{
    /**
     * Display a listing of departments
     */
    public function index(Request $request): JsonResponse
    {
        $query = Department::with([
            'parentDepartment',
            'childDepartments',
            'departmentHead.position',
            'employees' => function ($q) {
                $q->where('employment_status', 'active');
            }
        ]);

        // Filter by parent department
        if ($request->filled('parent_id')) {
            if ($request->parent_id === 'null') {
                $query->whereNull('parent_department_id');
            } else {
                $query->where('parent_department_id', $request->parent_id);
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by location
        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Get tree structure or flat list
        if ($request->get('tree', false)) {
            $departments = $this->buildDepartmentTree($query->get());
            return response()->json(['data' => $departments]);
        }

        // Paginated results
        $perPage = min($request->get('per_page', 15), 100);
        $departments = $query->paginate($perPage);

        return response()->json([
            'data' => DepartmentResource::collection($departments->items()),
            'meta' => [
                'current_page' => $departments->currentPage(),
                'last_page' => $departments->lastPage(),
                'per_page' => $departments->perPage(),
                'total' => $departments->total(),
            ],
        ]);
    }

    /**
     * Store a newly created department
     */
    public function store(StoreDepartmentRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // Generate department code if not provided
            if (!isset($data['code'])) {
                $data['code'] = $this->generateDepartmentCode($data['name']);
            }

            $department = Department::create($data);

            // Assign department head if provided
            if ($request->filled('department_head_id')) {
                $this->assignDepartmentHead($department, $request->department_head_id);
            }

            DB::commit();

            return response()->json([
                'message' => 'Departemen berhasil ditambahkan',
                'data' => new DepartmentResource($department->load([
                    'parentDepartment', 'departmentHead', 'employees'
                ])),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menambahkan departemen',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified department
     */
    public function show(Department $department): JsonResponse
    {
        $department->load([
            'parentDepartment',
            'childDepartments.departmentHead',
            'departmentHead.position',
            'employees' => function ($q) {
                $q->with(['position', 'employeeType'])
                  ->where('employment_status', 'active')
                  ->orderBy('hire_date');
            }
        ]);

        // Get department statistics
        $stats = [
            'total_employees' => $department->employees->count(),
            'medical_staff_count' => $department->employees->filter(function ($emp) {
                return $emp->employeeType->is_medical_staff;
            })->count(),
            'average_years_of_service' => $department->employees->avg('years_of_service'),
            'capacity_utilization' => $department->capacity > 0 
                ? ($department->employees->count() / $department->capacity) * 100 
                : 0,
        ];

        return response()->json([
            'data' => new DepartmentResource($department),
            'statistics' => $stats,
        ]);
    }

    /**
     * Update the specified department
     */
    public function update(UpdateDepartmentRequest $request, Department $department): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // Check for circular reference in parent department
            if (isset($data['parent_department_id'])) {
                if ($this->wouldCreateCircularReference($department, $data['parent_department_id'])) {
                    return response()->json([
                        'message' => 'Tidak dapat mengatur departemen induk karena akan membuat referensi melingkar',
                    ], 422);
                }
            }

            $department->update($data);

            // Update department head if provided
            if ($request->filled('department_head_id')) {
                $this->assignDepartmentHead($department, $request->department_head_id);
            }

            DB::commit();

            return response()->json([
                'message' => 'Data departemen berhasil diperbarui',
                'data' => new DepartmentResource($department->load([
                    'parentDepartment', 'departmentHead', 'employees'
                ])),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat memperbarui data departemen',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified department
     */
    public function destroy(Department $department): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Check if department has employees
            if ($department->employees()->exists()) {
                return response()->json([
                    'message' => 'Tidak dapat menghapus departemen yang memiliki karyawan. Silakan pindahkan karyawan terlebih dahulu.',
                ], 422);
            }

            // Check if department has child departments
            if ($department->childDepartments()->exists()) {
                return response()->json([
                    'message' => 'Tidak dapat menghapus departemen yang memiliki sub-departemen. Silakan hapus atau pindahkan sub-departemen terlebih dahulu.',
                ], 422);
            }

            $department->delete();

            DB::commit();

            return response()->json([
                'message' => 'Departemen berhasil dihapus',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menghapus departemen',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get department hierarchy tree
     */
    public function tree(): JsonResponse
    {
        $departments = Department::with([
            'childDepartments',
            'departmentHead',
            'employees' => function ($q) {
                $q->where('employment_status', 'active');
            }
        ])->whereNull('parent_department_id')->get();

        $tree = $this->buildDepartmentTree($departments);

        return response()->json(['data' => $tree]);
    }

    /**
     * Get department statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_departments' => Department::count(),
            'active_departments' => Department::where('status', 'active')->count(),
            'departments_with_head' => Department::whereNotNull('department_head_id')->count(),
            'average_employees_per_department' => Department::withCount('employees')
                ->get()
                ->avg('employees_count'),
            'departments_by_location' => Department::select('location')
                ->groupBy('location')
                ->selectRaw('location, count(*) as count')
                ->get()
                ->map(function ($item) {
                    return [
                        'location' => $item->location,
                        'count' => $item->count,
                    ];
                }),
            'capacity_utilization' => Department::select(
                    DB::raw('SUM(capacity) as total_capacity'),
                    DB::raw('(SELECT COUNT(*) FROM employees WHERE department_id = departments.id AND employment_status = "active") as total_employees')
                )
                ->first(),
        ];

        return response()->json(['data' => $stats]);
    }

    /**
     * Assign department head
     */
    public function assignHead(Request $request, Department $department): JsonResponse
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
        ]);

        try {
            DB::beginTransaction();

            $employee = Employee::findOrFail($request->employee_id);

            // Check if employee belongs to this department
            if ($employee->department_id !== $department->id) {
                return response()->json([
                    'message' => 'Karyawan harus berada di departemen yang sama untuk menjadi kepala departemen',
                ], 422);
            }

            // Remove previous department head role
            if ($department->departmentHead) {
                $department->departmentHead->user?->removeRole('Department Head');
            }

            // Assign new department head
            $department->update(['department_head_id' => $employee->id]);
            $employee->user?->assignRole('Department Head');

            DB::commit();

            return response()->json([
                'message' => 'Kepala departemen berhasil ditugaskan',
                'data' => new DepartmentResource($department->load('departmentHead')),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menugaskan kepala departemen',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Build department tree structure
     */
    private function buildDepartmentTree($departments, $parentId = null): array
    {
        $tree = [];

        foreach ($departments as $department) {
            if ($department->parent_department_id == $parentId) {
                $children = $this->buildDepartmentTree($departments, $department->id);
                
                $departmentData = [
                    'id' => $department->id,
                    'name' => $department->name,
                    'code' => $department->code,
                    'description' => $department->description,
                    'location' => $department->location,
                    'capacity' => $department->capacity,
                    'status' => $department->status,
                    'department_head' => $department->departmentHead ? [
                        'id' => $department->departmentHead->id,
                        'full_name' => $department->departmentHead->full_name,
                        'position' => $department->departmentHead->position?->title,
                    ] : null,
                    'employee_count' => $department->employees->count(),
                    'children' => $children,
                ];

                $tree[] = $departmentData;
            }
        }

        return $tree;
    }

    /**
     * Generate department code
     */
    private function generateDepartmentCode(string $name): string
    {
        $words = explode(' ', $name);
        $code = '';
        
        foreach ($words as $word) {
            $code .= strtoupper(substr($word, 0, 1));
        }
        
        // Ensure uniqueness
        $baseCode = $code;
        $counter = 1;
        
        while (Department::where('code', $code)->exists()) {
            $code = $baseCode . $counter;
            $counter++;
        }
        
        return $code;
    }

    /**
     * Check if assigning parent would create circular reference
     */
    private function wouldCreateCircularReference(Department $department, ?int $parentId): bool
    {
        if (!$parentId) {
            return false;
        }

        $currentDept = Department::find($parentId);
        
        while ($currentDept) {
            if ($currentDept->id === $department->id) {
                return true;
            }
            $currentDept = $currentDept->parentDepartment;
        }

        return false;
    }

    /**
     * Assign department head
     */
    private function assignDepartmentHead(Department $department, int $employeeId): void
    {
        $employee = Employee::findOrFail($employeeId);

        // Remove previous department head role
        if ($department->departmentHead) {
            $department->departmentHead->user?->removeRole('Department Head');
        }

        // Assign new department head
        $department->update(['department_head_id' => $employeeId]);
        $employee->user?->assignRole('Department Head');
    }
}
```

---

## Step 2: Create Department Resource

### 2.1 Create Department API Resource

```bash
php artisan make:resource DepartmentResource
```

Edit `app/Http/Resources/DepartmentResource.php`:

```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DepartmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'location' => $this->location,
            'capacity' => $this->capacity,
            'status' => $this->status,
            'indonesian_status' => $this->indonesian_status,
            'full_path' => $this->full_path,

            // Parent Department
            'parent_department' => $this->whenLoaded('parentDepartment', function () {
                return $this->parentDepartment ? [
                    'id' => $this->parentDepartment->id,
                    'name' => $this->parentDepartment->name,
                    'code' => $this->parentDepartment->code,
                    'full_path' => $this->parentDepartment->full_path,
                ] : null;
            }),

            // Child Departments
            'child_departments' => $this->whenLoaded('childDepartments', function () {
                return $this->childDepartments->map(function ($child) {
                    return [
                        'id' => $child->id,
                        'name' => $child->name,
                        'code' => $child->code,
                        'location' => $child->location,
                        'employee_count' => $child->employees_count ?? $child->employees->count(),
                        'status' => $child->status,
                        'department_head' => $child->departmentHead ? [
                            'id' => $child->departmentHead->id,
                            'full_name' => $child->departmentHead->full_name,
                            'position' => $child->departmentHead->position?->title,
                        ] : null,
                    ];
                });
            }),

            // Department Head
            'department_head' => $this->whenLoaded('departmentHead', function () {
                return $this->departmentHead ? [
                    'id' => $this->departmentHead->id,
                    'employee_number' => $this->departmentHead->employee_number,
                    'full_name' => $this->departmentHead->full_name,
                    'position' => $this->departmentHead->position ? [
                        'id' => $this->departmentHead->position->id,
                        'title' => $this->departmentHead->position->title,
                        'level' => $this->departmentHead->position->level,
                    ] : null,
                    'phone_number' => $this->departmentHead->phone_number,
                    'email' => $this->departmentHead->user?->email,
                    'years_of_service' => $this->departmentHead->years_of_service,
                ] : null;
            }),

            // Employees
            'employees' => $this->whenLoaded('employees', function () {
                return $this->employees->map(function ($employee) {
                    return [
                        'id' => $employee->id,
                        'employee_number' => $employee->employee_number,
                        'full_name' => $employee->full_name,
                        'position' => $employee->position?->title,
                        'employee_type' => $employee->employeeType?->indonesian_name,
                        'employment_status' => $employee->employment_status,
                        'indonesian_employment_status' => $employee->indonesian_employment_status,
                        'hire_date' => $employee->hire_date?->format('Y-m-d'),
                        'years_of_service' => $employee->years_of_service,
                        'is_medical_staff' => $employee->isMedicalStaff(),
                        'profile_photo_url' => $employee->profile_photo_path
                            ? asset('storage/' . $employee->profile_photo_path)
                            : null,
                    ];
                });
            }),

            // Computed attributes
            'employee_count' => $this->whenLoaded('employees', function () {
                return $this->employees->count();
            }),
            'active_employee_count' => $this->whenLoaded('employees', function () {
                return $this->employees->where('employment_status', 'active')->count();
            }),
            'medical_staff_count' => $this->whenLoaded('employees', function () {
                return $this->employees->filter(function ($emp) {
                    return $emp->isMedicalStaff();
                })->count();
            }),
            'capacity_utilization' => $this->capacity > 0
                ? round(($this->whenLoaded('employees', function () {
                    return $this->employees->count();
                }) / $this->capacity) * 100, 2)
                : 0,
            'has_children' => $this->whenLoaded('childDepartments', function () {
                return $this->childDepartments->isNotEmpty();
            }),
            'level' => $this->level,
            'is_active' => $this->isActive(),

            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
```

---

## Step 3: Create Form Request Classes

### 3.1 Create Store Department Request

```bash
php artisan make:request StoreDepartmentRequest
```

Edit `app/Http/Requests/StoreDepartmentRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreDepartmentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create departments');
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'unique:departments,name'],
            'code' => ['nullable', 'string', 'max:10', 'unique:departments,code'],
            'description' => ['nullable', 'string'],
            'location' => ['required', 'string', 'max:255'],
            'capacity' => ['nullable', 'integer', 'min:1'],
            'status' => ['required', Rule::in(['active', 'inactive'])],
            'parent_department_id' => ['nullable', 'exists:departments,id'],
            'department_head_id' => ['nullable', 'exists:employees,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Nama departemen wajib diisi',
            'name.unique' => 'Nama departemen sudah ada',
            'code.unique' => 'Kode departemen sudah ada',
            'location.required' => 'Lokasi departemen wajib diisi',
            'capacity.min' => 'Kapasitas minimal 1 orang',
            'status.required' => 'Status departemen wajib dipilih',
            'status.in' => 'Status departemen tidak valid',
            'parent_department_id.exists' => 'Departemen induk tidak valid',
            'department_head_id.exists' => 'Kepala departemen tidak valid',
        ];
    }

    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate department head belongs to the same department
            if ($this->filled(['department_head_id', 'parent_department_id'])) {
                $employee = \App\Models\Employee::find($this->department_head_id);
                if ($employee && $employee->department_id !== $this->parent_department_id) {
                    $validator->errors()->add(
                        'department_head_id',
                        'Kepala departemen harus berada di departemen yang sama'
                    );
                }
            }
        });
    }
}
```

### 3.2 Create Update Department Request

```bash
php artisan make:request UpdateDepartmentRequest
```

Edit `app/Http/Requests/UpdateDepartmentRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateDepartmentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('edit departments');
    }

    public function rules(): array
    {
        $departmentId = $this->route('department')->id;

        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('departments', 'name')->ignore($departmentId)
            ],
            'code' => [
                'nullable',
                'string',
                'max:10',
                Rule::unique('departments', 'code')->ignore($departmentId)
            ],
            'description' => ['nullable', 'string'],
            'location' => ['required', 'string', 'max:255'],
            'capacity' => ['nullable', 'integer', 'min:1'],
            'status' => ['required', Rule::in(['active', 'inactive'])],
            'parent_department_id' => [
                'nullable',
                'exists:departments,id',
                Rule::notIn([$departmentId]) // Prevent self-reference
            ],
            'department_head_id' => ['nullable', 'exists:employees,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Nama departemen wajib diisi',
            'name.unique' => 'Nama departemen sudah ada',
            'code.unique' => 'Kode departemen sudah ada',
            'location.required' => 'Lokasi departemen wajib diisi',
            'capacity.min' => 'Kapasitas minimal 1 orang',
            'status.required' => 'Status departemen wajib dipilih',
            'status.in' => 'Status departemen tidak valid',
            'parent_department_id.exists' => 'Departemen induk tidak valid',
            'parent_department_id.not_in' => 'Departemen tidak dapat menjadi induk untuk dirinya sendiri',
            'department_head_id.exists' => 'Kepala departemen tidak valid',
        ];
    }
}
```

---

## Chapter Summary

In this chapter, you've successfully implemented:

✅ **Hierarchical Department Management**
- Tree-structured department organization
- Parent-child department relationships
- Circular reference prevention
- Department head assignment

✅ **Department API Controller**
- Full CRUD operations with hierarchy support
- Department statistics and reporting
- Tree structure visualization
- Advanced filtering and search

✅ **Department Resource & Validation**
- Comprehensive data transformation
- Hierarchical data representation
- Form validation with business rules
- Indonesian localization

### Key Features Implemented

**Backend Features:**
- Hierarchical department structure
- Department head management
- Capacity and location tracking
- Department statistics
- Circular reference prevention

**Security & Validation:**
- Permission-based access control
- Comprehensive input validation
- Business rule enforcement
- Data integrity protection

### What's Next?

In Chapter 7, we'll implement shift scheduling and management with Indonesian healthcare shift patterns.

---

## Step 4: Create Department Management React Components

### 4.1 Create Department List Component

Create `resources/js/Pages/Departments/Index.tsx`:

```tsx
import React, { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/Components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/Components/ui/alert-dialog';
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  MapPin,
  Building2,
  TreePine
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { toast } from 'sonner';

interface Department {
  id: number;
  name: string;
  code: string;
  description: string;
  location: string;
  capacity: number;
  status: 'active' | 'inactive';
  parent_department_id: number | null;
  department_head_id: number | null;
  parent_department?: Department;
  department_head?: {
    id: number;
    full_name: string;
  };
  employees_count: number;
  children_count: number;
  created_at: string;
  updated_at: string;
}

interface Props {
  departments: {
    data: Department[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  filters: {
    search?: string;
    status?: string;
    parent_id?: string;
  };
}

export default function DepartmentIndex({ departments, filters }: Props) {
  const [search, setSearch] = useState(filters.search || '');
  const [status, setStatus] = useState(filters.status || '');
  const [parentId, setParentId] = useState(filters.parent_id || '');
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'tree'>('table');

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      router.get(route('departments.index'), {
        search,
        status,
        parent_id: parentId,
      }, {
        preserveState: true,
        replace: true,
      });
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [search, status, parentId]);

  const handleDelete = (id: number) => {
    router.delete(route('departments.destroy', id), {
      onSuccess: () => {
        toast.success('Departemen berhasil dihapus');
        setDeleteId(null);
      },
      onError: (errors) => {
        toast.error(errors.message || 'Gagal menghapus departemen');
        setDeleteId(null);
      },
    });
  };

  const getStatusBadge = (status: string) => {
    return (
      <Badge variant={status === 'active' ? 'default' : 'secondary'}>
        {status === 'active' ? 'Aktif' : 'Tidak Aktif'}
      </Badge>
    );
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            Manajemen Departemen
          </h2>
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'table' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('table')}
            >
              <Building2 className="h-4 w-4 mr-2" />
              Tabel
            </Button>
            <Button
              variant={viewMode === 'tree' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('tree')}
            >
              <TreePine className="h-4 w-4 mr-2" />
              Struktur
            </Button>
            <Link href={route('departments.create')}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Departemen
              </Button>
            </Link>
          </div>
        </div>
      }
    >
      <Head title="Manajemen Departemen" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {/* Filters */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Filter Departemen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Cari departemen..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <select
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Semua Status</option>
                  <option value="active">Aktif</option>
                  <option value="inactive">Tidak Aktif</option>
                </select>
                <select
                  value={parentId}
                  onChange={(e) => setParentId(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                >
                  <option value="">Semua Departemen</option>
                  <option value="null">Departemen Utama</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Department Table */}
          {viewMode === 'table' && (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Daftar Departemen</CardTitle>
                  <div className="text-sm text-gray-500">
                    Total: {departments.total} departemen
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama Departemen</TableHead>
                      <TableHead>Kode</TableHead>
                      <TableHead>Lokasi</TableHead>
                      <TableHead>Kepala Departemen</TableHead>
                      <TableHead>Karyawan</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {departments.data.map((department) => (
                      <TableRow key={department.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{department.name}</div>
                            {department.parent_department && (
                              <div className="text-sm text-gray-500">
                                Sub dari: {department.parent_department.name}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{department.code}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                            {department.location}
                          </div>
                        </TableCell>
                        <TableCell>
                          {department.department_head ? (
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-1 text-gray-400" />
                              {department.department_head.full_name}
                            </div>
                          ) : (
                            <span className="text-gray-400">Belum ditentukan</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {department.employees_count} orang
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(department.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={route('departments.edit', department.id)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => setDeleteId(department.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Hapus
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* Tree View Placeholder */}
          {viewMode === 'tree' && (
            <Card>
              <CardHeader>
                <CardTitle>Struktur Organisasi Departemen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  Struktur organisasi akan ditampilkan di Step 5
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteId !== null} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus departemen ini?
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteId && handleDelete(deleteId)}
              className="bg-red-600 hover:bg-red-700"
            >
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AuthenticatedLayout>
  );
}
```

### 4.2 Create Department Form Component

Create `resources/js/Pages/Departments/Form.tsx`:

```tsx
import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import { ArrowLeft, Save, X } from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { toast } from 'sonner';

interface Department {
  id: number;
  name: string;
  code: string;
  description: string;
  location: string;
  capacity: number;
  status: 'active' | 'inactive';
  parent_department_id: number | null;
  department_head_id: number | null;
}

interface Employee {
  id: number;
  full_name: string;
  employee_id: string;
}

interface Props {
  department?: Department;
  parentDepartments: Department[];
  employees: Employee[];
  isEdit?: boolean;
}

export default function DepartmentForm({
  department,
  parentDepartments,
  employees,
  isEdit = false
}: Props) {
  const { data, setData, post, put, processing, errors, reset } = useForm({
    name: department?.name || '',
    code: department?.code || '',
    description: department?.description || '',
    location: department?.location || '',
    capacity: department?.capacity || '',
    status: department?.status || 'active',
    parent_department_id: department?.parent_department_id || '',
    department_head_id: department?.department_head_id || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const submitData = {
      ...data,
      capacity: data.capacity ? parseInt(data.capacity.toString()) : null,
      parent_department_id: data.parent_department_id || null,
      department_head_id: data.department_head_id || null,
    };

    if (isEdit && department) {
      put(route('departments.update', department.id), {
        onSuccess: () => {
          toast.success('Departemen berhasil diperbarui');
        },
        onError: () => {
          toast.error('Gagal memperbarui departemen');
        },
      });
    } else {
      post(route('departments.store'), {
        onSuccess: () => {
          toast.success('Departemen berhasil dibuat');
          reset();
        },
        onError: () => {
          toast.error('Gagal membuat departemen');
        },
      });
    }
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex items-center gap-4">
          <Link href={route('departments.index')}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Button>
          </Link>
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            {isEdit ? 'Edit Departemen' : 'Tambah Departemen'}
          </h2>
        </div>
      }
    >
      <Head title={isEdit ? 'Edit Departemen' : 'Tambah Departemen'} />

      <div className="py-12">
        <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <Card>
            <CardHeader>
              <CardTitle>
                {isEdit ? 'Edit Informasi Departemen' : 'Informasi Departemen Baru'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Department Name */}
                  <div className="space-y-2">
                    <Label htmlFor="name">Nama Departemen *</Label>
                    <Input
                      id="name"
                      value={data.name}
                      onChange={(e) => setData('name', e.target.value)}
                      placeholder="Contoh: Departemen Kardiologi"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-600">{errors.name}</p>
                    )}
                  </div>

                  {/* Department Code */}
                  <div className="space-y-2">
                    <Label htmlFor="code">Kode Departemen</Label>
                    <Input
                      id="code"
                      value={data.code}
                      onChange={(e) => setData('code', e.target.value)}
                      placeholder="Contoh: KARD"
                      className={errors.code ? 'border-red-500' : ''}
                    />
                    {errors.code && (
                      <p className="text-sm text-red-600">{errors.code}</p>
                    )}
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    value={data.description}
                    onChange={(e) => setData('description', e.target.value)}
                    placeholder="Deskripsi departemen..."
                    rows={3}
                    className={errors.description ? 'border-red-500' : ''}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600">{errors.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Location */}
                  <div className="space-y-2">
                    <Label htmlFor="location">Lokasi *</Label>
                    <Input
                      id="location"
                      value={data.location}
                      onChange={(e) => setData('location', e.target.value)}
                      placeholder="Contoh: Lantai 2, Gedung A"
                      className={errors.location ? 'border-red-500' : ''}
                    />
                    {errors.location && (
                      <p className="text-sm text-red-600">{errors.location}</p>
                    )}
                  </div>

                  {/* Capacity */}
                  <div className="space-y-2">
                    <Label htmlFor="capacity">Kapasitas (orang)</Label>
                    <Input
                      id="capacity"
                      type="number"
                      value={data.capacity}
                      onChange={(e) => setData('capacity', e.target.value)}
                      placeholder="Contoh: 50"
                      min="1"
                      className={errors.capacity ? 'border-red-500' : ''}
                    />
                    {errors.capacity && (
                      <p className="text-sm text-red-600">{errors.capacity}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Parent Department */}
                  <div className="space-y-2">
                    <Label htmlFor="parent_department_id">Departemen Induk</Label>
                    <Select
                      value={data.parent_department_id.toString()}
                      onValueChange={(value) => setData('parent_department_id', value)}
                    >
                      <SelectTrigger className={errors.parent_department_id ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Pilih departemen induk" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Tidak ada (Departemen Utama)</SelectItem>
                        {parentDepartments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id.toString()}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.parent_department_id && (
                      <p className="text-sm text-red-600">{errors.parent_department_id}</p>
                    )}
                  </div>

                  {/* Department Head */}
                  <div className="space-y-2">
                    <Label htmlFor="department_head_id">Kepala Departemen</Label>
                    <Select
                      value={data.department_head_id.toString()}
                      onValueChange={(value) => setData('department_head_id', value)}
                    >
                      <SelectTrigger className={errors.department_head_id ? 'border-red-500' : ''}>
                        <SelectValue placeholder="Pilih kepala departemen" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Belum ditentukan</SelectItem>
                        {employees.map((employee) => (
                          <SelectItem key={employee.id} value={employee.id.toString()}>
                            {employee.full_name} ({employee.employee_id})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.department_head_id && (
                      <p className="text-sm text-red-600">{errors.department_head_id}</p>
                    )}
                  </div>
                </div>

                {/* Status */}
                <div className="space-y-2">
                  <Label htmlFor="status">Status *</Label>
                  <Select
                    value={data.status}
                    onValueChange={(value) => setData('status', value as 'active' | 'inactive')}
                  >
                    <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Pilih status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Aktif</SelectItem>
                      <SelectItem value="inactive">Tidak Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.status && (
                    <p className="text-sm text-red-600">{errors.status}</p>
                  )}
                </div>

                {/* Form Actions */}
                <div className="flex justify-end gap-4 pt-6 border-t">
                  <Link href={route('departments.index')}>
                    <Button type="button" variant="outline">
                      <X className="h-4 w-4 mr-2" />
                      Batal
                    </Button>
                  </Link>
                  <Button type="submit" disabled={processing}>
                    <Save className="h-4 w-4 mr-2" />
                    {processing ? 'Menyimpan...' : (isEdit ? 'Perbarui' : 'Simpan')}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

---

## Step 5: Create Organizational Chart Component

### 5.1 Create Department Tree Component

Create `resources/js/Components/DepartmentTree.tsx`:

```tsx
import React, { useState } from 'react';
import { Card, CardContent } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import {
  ChevronDown,
  ChevronRight,
  Users,
  MapPin,
  Building2,
  Crown
} from 'lucide-react';

interface Department {
  id: number;
  name: string;
  code: string;
  location: string;
  status: 'active' | 'inactive';
  department_head?: {
    id: number;
    full_name: string;
  };
  employees_count: number;
  children?: Department[];
}

interface DepartmentNodeProps {
  department: Department;
  level: number;
  onSelect?: (department: Department) => void;
}

const DepartmentNode: React.FC<DepartmentNodeProps> = ({
  department,
  level,
  onSelect
}) => {
  const [isExpanded, setIsExpanded] = useState(level < 2);
  const hasChildren = department.children && department.children.length > 0;

  const handleToggle = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleSelect = () => {
    onSelect?.(department);
  };

  return (
    <div className="w-full">
      <Card
        className={`mb-2 cursor-pointer transition-all hover:shadow-md ${
          department.status === 'inactive' ? 'opacity-60' : ''
        }`}
        onClick={handleSelect}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {/* Expand/Collapse Button */}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggle();
                }}
                disabled={!hasChildren}
              >
                {hasChildren ? (
                  isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )
                ) : (
                  <div className="h-4 w-4" />
                )}
              </Button>

              {/* Department Info */}
              <div className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-gray-900">
                      {department.name}
                    </h3>
                    {department.code && (
                      <Badge variant="outline" className="text-xs">
                        {department.code}
                      </Badge>
                    )}
                    <Badge
                      variant={department.status === 'active' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {department.status === 'active' ? 'Aktif' : 'Tidak Aktif'}
                    </Badge>
                  </div>

                  <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {department.location}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {department.employees_count} karyawan
                    </div>
                    {department.department_head && (
                      <div className="flex items-center gap-1">
                        <Crown className="h-3 w-3 text-yellow-600" />
                        {department.department_head.full_name}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="ml-8 border-l-2 border-gray-200 pl-4">
          {department.children!.map((child) => (
            <DepartmentNode
              key={child.id}
              department={child}
              level={level + 1}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </div>
  );
};

interface DepartmentTreeProps {
  departments: Department[];
  onDepartmentSelect?: (department: Department) => void;
}

export const DepartmentTree: React.FC<DepartmentTreeProps> = ({
  departments,
  onDepartmentSelect
}) => {
  return (
    <div className="space-y-4">
      {departments.map((department) => (
        <DepartmentNode
          key={department.id}
          department={department}
          level={0}
          onSelect={onDepartmentSelect}
        />
      ))}
    </div>
  );
};

export default DepartmentTree;
```

### 5.2 Update Department Index to Use Tree Component

Update `resources/js/Pages/Departments/Index.tsx` to include the tree view:

```tsx
// Add import at the top
import DepartmentTree from '@/Components/DepartmentTree';

// Replace the Tree View section with:
{viewMode === 'tree' && (
  <Card>
    <CardHeader>
      <CardTitle>Struktur Organisasi Departemen</CardTitle>
    </CardHeader>
    <CardContent>
      <DepartmentTree
        departments={departments.data}
        onDepartmentSelect={(dept) => {
          // Handle department selection
          console.log('Selected department:', dept);
        }}
      />
    </CardContent>
  </Card>
)}
```

---

## Step 6: Create Department Routes and Controller Integration

### 6.1 Add Department Routes

Add to `routes/web.php`:

```php
use App\Http\Controllers\DepartmentController;

Route::middleware(['auth', 'verified'])->group(function () {
    // Department Management Routes
    Route::resource('departments', DepartmentController::class);
    Route::post('departments/{department}/assign-head', [DepartmentController::class, 'assignHead'])
        ->name('departments.assign-head');
    Route::get('departments/{department}/employees', [DepartmentController::class, 'employees'])
        ->name('departments.employees');
});
```

### 6.2 Create Web Controller for Inertia

Create `app/Http/Controllers/DepartmentController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\Employee;
use App\Http\Requests\StoreDepartmentRequest;
use App\Http\Requests\UpdateDepartmentRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DepartmentController extends Controller
{
    public function index(Request $request): Response
    {
        $query = Department::with(['parentDepartment', 'departmentHead', 'employees'])
            ->withCount('employees');

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('code', 'like', '%' . $request->search . '%')
                  ->orWhere('location', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('parent_id')) {
            if ($request->parent_id === 'null') {
                $query->whereNull('parent_department_id');
            } else {
                $query->where('parent_department_id', $request->parent_id);
            }
        }

        $departments = $query->paginate(15)->withQueryString();

        return Inertia::render('Departments/Index', [
            'departments' => $departments,
            'filters' => $request->only(['search', 'status', 'parent_id']),
        ]);
    }

    public function create(): Response
    {
        $parentDepartments = Department::where('status', 'active')
            ->whereNull('parent_department_id')
            ->get(['id', 'name']);

        $employees = Employee::where('employment_status', 'active')
            ->whereDoesntHave('departmentHead')
            ->get(['id', 'full_name', 'employee_id']);

        return Inertia::render('Departments/Form', [
            'parentDepartments' => $parentDepartments,
            'employees' => $employees,
            'isEdit' => false,
        ]);
    }

    public function store(StoreDepartmentRequest $request)
    {
        $department = Department::create($request->validated());

        return redirect()->route('departments.index')
            ->with('success', 'Departemen berhasil dibuat');
    }

    public function edit(Department $department): Response
    {
        $parentDepartments = Department::where('status', 'active')
            ->where('id', '!=', $department->id)
            ->whereNull('parent_department_id')
            ->get(['id', 'name']);

        $employees = Employee::where('employment_status', 'active')
            ->where(function ($q) use ($department) {
                $q->whereDoesntHave('departmentHead')
                  ->orWhere('id', $department->department_head_id);
            })
            ->get(['id', 'full_name', 'employee_id']);

        return Inertia::render('Departments/Form', [
            'department' => $department,
            'parentDepartments' => $parentDepartments,
            'employees' => $employees,
            'isEdit' => true,
        ]);
    }

    public function update(UpdateDepartmentRequest $request, Department $department)
    {
        $department->update($request->validated());

        return redirect()->route('departments.index')
            ->with('success', 'Departemen berhasil diperbarui');
    }

    public function destroy(Department $department)
    {
        if ($department->employees()->exists()) {
            return back()->withErrors([
                'message' => 'Tidak dapat menghapus departemen yang masih memiliki karyawan'
            ]);
        }

        if ($department->children()->exists()) {
            return back()->withErrors([
                'message' => 'Tidak dapat menghapus departemen yang masih memiliki sub-departemen'
            ]);
        }

        $department->delete();

        return redirect()->route('departments.index')
            ->with('success', 'Departemen berhasil dihapus');
    }
}
```

---

## Summary

In this chapter, we've implemented comprehensive department management with:

### ✅ **Backend Implementation**
- Hierarchical department structure with parent-child relationships
- Department head assignment and management
- Location and capacity tracking
- Comprehensive validation and error handling
- Permission-based access control

### ✅ **Frontend Implementation**
- **Department List Component**: Table and tree view with filtering
- **Department Form Component**: Create and edit departments
- **Organizational Chart**: Interactive tree structure visualization
- **Responsive Design**: Mobile-friendly interface
- **Indonesian Localization**: All text in Indonesian

### ✅ **Key Features**
- **Hierarchical Management**: Parent-child department relationships
- **Visual Organization**: Tree structure for organizational chart
- **Advanced Filtering**: Search, status, and parent department filters
- **Real-time Updates**: Debounced search and instant feedback
- **Role-based Access**: Permission-based department management
- **Data Validation**: Comprehensive form validation with Indonesian messages

### Key Commands to Remember

```bash
# Backend resources
php artisan make:controller Api/DepartmentController --api
php artisan make:controller DepartmentController
php artisan make:resource DepartmentResource
php artisan make:request StoreDepartmentRequest
php artisan make:request UpdateDepartmentRequest

# Frontend components
# Create React components in resources/js/Pages/Departments/
# Create shared components in resources/js/Components/
```

Ready for Chapter 7? Let's build shift scheduling with React components!
