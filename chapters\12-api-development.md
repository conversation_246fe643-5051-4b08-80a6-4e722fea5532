# Chapter 12: API Development

Welcome to Chapter 12! In this chapter, we'll create a comprehensive RESTful API for mobile app integration and third-party services, complete with authentication, documentation, and security features.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create RESTful API endpoints for mobile integration
- Build authentication for API access
- Add API documentation and testing
- Implement rate limiting and security
- Create API versioning system
- Build API resource transformations
- Add comprehensive error handling

## 📋 What We'll Cover

1. Setting up API routes and structure
2. Creating API authentication with Sanctum
3. Building API controllers and resources
4. Implementing API versioning
5. Adding rate limiting and security
6. Creating API documentation
7. Building comprehensive error handling
8. Testing the API endpoints

## 🛠 Step 1: Installing Laravel Sanctum

First, let's install and configure Laravel Sanctum for API authentication:

```bash
# Install Laravel Sanctum
composer require laravel/sanctum

# Publish Sanctum configuration
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

# Run migrations
php artisan migrate
```

Configure Sanctum in `config/sanctum.php`:

```php
<?php

return [
    'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
        '%s%s',
        'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
        env('APP_URL') ? ','.parse_url(env('APP_URL'), PHP_URL_HOST) : ''
    ))),

    'guard' => ['web'],

    'expiration' => null,

    'middleware' => [
        'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
        'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
    ],
];
```

Add Sanctum middleware to `app/Http/Kernel.php`:

```php
'api' => [
    \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
    'throttle:api',
    \Illuminate\Routing\Middleware\SubstituteBindings::class,
],
```

## 🛠 Step 2: Creating API Base Controller

Create a base API controller for consistent responses:

```bash
php artisan make:controller Api/BaseApiController
```

Edit `app/Http/Controllers/Api/BaseApiController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class BaseApiController extends Controller
{
    /**
     * Success response method
     */
    public function sendResponse($result, $message = 'Success', $code = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $result,
        ];

        return response()->json($response, $code);
    }

    /**
     * Error response method
     */
    public function sendError($error, $errorMessages = [], $code = 404): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $error,
        ];

        if (!empty($errorMessages)) {
            $response['errors'] = $errorMessages;
        }

        return response()->json($response, $code);
    }

    /**
     * Validation error response
     */
    public function sendValidationError($validator): JsonResponse
    {
        return $this->sendError('Validation Error', $validator->errors(), 422);
    }

    /**
     * Unauthorized response
     */
    public function sendUnauthorized($message = 'Unauthorized'): JsonResponse
    {
        return $this->sendError($message, [], 401);
    }

    /**
     * Forbidden response
     */
    public function sendForbidden($message = 'Forbidden'): JsonResponse
    {
        return $this->sendError($message, [], 403);
    }

    /**
     * Not found response
     */
    public function sendNotFound($message = 'Resource not found'): JsonResponse
    {
        return $this->sendError($message, [], 404);
    }

    /**
     * Server error response
     */
    public function sendServerError($message = 'Internal server error'): JsonResponse
    {
        return $this->sendError($message, [], 500);
    }

    /**
     * Paginated response
     */
    public function sendPaginatedResponse($data, $message = 'Success'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data->items(),
            'pagination' => [
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'per_page' => $data->perPage(),
                'total' => $data->total(),
                'from' => $data->firstItem(),
                'to' => $data->lastItem(),
                'has_more_pages' => $data->hasMorePages(),
            ],
        ]);
    }
}
```

## 🛠 Step 3: Creating API Resources

Create API resources for data transformation:

```bash
# Create API resources
php artisan make:resource Api/UserResource
php artisan make:resource Api/CustomerResource
php artisan make:resource Api/ServiceResource
php artisan make:resource Api/BookingResource
php artisan make:resource Api/PaymentResource
```

Edit `app/Http/Resources/Api/UserResource.php`:

```php
<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at,
            'roles' => $this->roles->pluck('name'),
            'permissions' => $this->getAllPermissions()->pluck('name'),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

Edit `app/Http/Resources/Api/CustomerResource.php`:

```php
<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->full_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'zip_code' => $this->zip_code,
            'date_of_birth' => $this->date_of_birth,
            'total_bookings' => $this->bookings_count ?? $this->bookings()->count(),
            'total_spent' => $this->total_spent,
            'user' => new UserResource($this->whenLoaded('user')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

Edit `app/Http/Resources/Api/ServiceResource.php`:

```php
<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'formatted_price' => $this->formatted_price,
            'duration' => $this->duration,
            'formatted_duration' => $this->formatted_duration,
            'category' => $this->category,
            'is_active' => $this->is_active,
            'image_url' => $this->image_url,
            'features' => $this->features,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

Edit `app/Http/Resources/Api/BookingResource.php`:

```php
<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookingResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'booking_number' => $this->booking_number,
            'booking_date' => $this->booking_date,
            'booking_time' => $this->booking_time,
            'formatted_booking_date' => $this->formatted_booking_date,
            'formatted_booking_time' => $this->formatted_booking_time,
            'status' => $this->status,
            'status_label' => $this->status_label,
            'total_amount' => $this->total_amount,
            'formatted_total' => $this->formatted_total,
            'vehicle_make' => $this->vehicle_make,
            'vehicle_model' => $this->vehicle_model,
            'vehicle_year' => $this->vehicle_year,
            'vehicle_color' => $this->vehicle_color,
            'vehicle_license_plate' => $this->vehicle_license_plate,
            'special_instructions' => $this->special_instructions,
            'customer' => new CustomerResource($this->whenLoaded('customer')),
            'services' => ServiceResource::collection($this->whenLoaded('services')),
            'payments' => PaymentResource::collection($this->whenLoaded('payments')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

Edit `app/Http/Resources/Api/PaymentResource.php`:

```php
<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PaymentResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'amount' => $this->amount,
            'formatted_amount' => $this->formatted_amount,
            'currency' => $this->currency,
            'status' => $this->status,
            'status_label' => $this->status_label,
            'payment_method_type' => $this->payment_method_type,
            'payment_intent_id' => $this->payment_intent_id,
            'stripe_charge_id' => $this->stripe_charge_id,
            'paid_at' => $this->paid_at,
            'refunded_at' => $this->refunded_at,
            'refund_amount' => $this->refund_amount,
            'booking' => new BookingResource($this->whenLoaded('booking')),
            'customer' => new CustomerResource($this->whenLoaded('customer')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

## 🛠 Step 4: Creating Authentication Controller

Create an authentication controller for API login/logout:

```bash
php artisan make:controller Api/AuthController
```

Edit `app/Http/Controllers/Api/AuthController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\Api\UserResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AuthController extends BaseApiController
{
    /**
     * Register a new user
     */
    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        // Assign customer role by default
        $user->assignRole('customer');

        $token = $user->createToken('auth_token')->plainTextToken;

        return $this->sendResponse([
            'user' => new UserResource($user),
            'access_token' => $token,
            'token_type' => 'Bearer',
        ], 'User registered successfully');
    }

    /**
     * Login user
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator);
        }

        if (!Auth::attempt($request->only('email', 'password'))) {
            return $this->sendUnauthorized('Invalid credentials');
        }

        $user = User::where('email', $request->email)->firstOrFail();
        $token = $user->createToken('auth_token')->plainTextToken;

        return $this->sendResponse([
            'user' => new UserResource($user),
            'access_token' => $token,
            'token_type' => 'Bearer',
        ], 'Login successful');
    }

    /**
     * Get authenticated user
     */
    public function me(Request $request): JsonResponse
    {
        return $this->sendResponse(
            new UserResource($request->user()),
            'User profile retrieved successfully'
        );
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return $this->sendResponse([], 'Logged out successfully');
    }

    /**
     * Logout from all devices
     */
    public function logoutAll(Request $request): JsonResponse
    {
        $request->user()->tokens()->delete();

        return $this->sendResponse([], 'Logged out from all devices successfully');
    }
}
```

## 🧪 Testing the API

1. **Test Authentication**:
   - Register new user
   - Login with credentials
   - Access protected endpoints
   - Test token expiration

2. **Test CRUD Operations**:
   - Create, read, update, delete resources
   - Test validation rules
   - Verify response formats

3. **Test Rate Limiting**:
   - Exceed rate limits
   - Verify throttling responses
   - Test different rate limit tiers

4. **Test Error Handling**:
   - Invalid requests
   - Unauthorized access
   - Server errors

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Created RESTful API endpoints for mobile integration
✅ Built authentication for API access
✅ Added API documentation and testing
✅ Implemented rate limiting and security
✅ Created API versioning system
✅ Built API resource transformations
✅ Added comprehensive error handling

### API Features Implemented:
- **RESTful Architecture**: Clean, consistent API design
- **Authentication**: Secure token-based authentication with Sanctum
- **Resource Transformation**: Consistent data formatting with API resources
- **Rate Limiting**: Protection against abuse and overuse
- **Error Handling**: Comprehensive error responses
- **Versioning**: Future-proof API structure
- **Security**: Protected endpoints and data validation

## 🚀 What's Next?

In the next chapter, we'll:
- Add comprehensive testing suite with PHPUnit
- Create feature and unit tests
- Build deployment guide for production
- Add performance optimization
- Create monitoring and logging setup

---

**Ready for testing and deployment?** Let's move on to [Chapter 13: Testing and Deployment](./13-testing-deployment.md)!
