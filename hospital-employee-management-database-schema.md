# Hospital Employee Management System - Database Schema Design

## Overview
This document outlines the comprehensive database schema for the Hospital Employee Management System targeting Indonesian healthcare institutions. The schema is designed to handle complex hospital hierarchies, employee types, shift management, and compliance requirements specific to Indonesian healthcare regulations.

## Core Entities and Relationships

### 1. Users Table (Authentication)
```sql
users
├── id (Primary Key)
├── email (Unique)
├── email_verified_at
├── password
├── remember_token
├── created_at
├── updated_at
└── deleted_at (Soft Delete)
```

### 2. Employee Profiles
```sql
employees
├── id (Primary Key)
├── user_id (Foreign Key to users)
├── employee_number (Unique)
├── first_name
├── last_name
├── full_name (Generated)
├── date_of_birth
├── gender (enum: male, female)
├── phone_number
├── emergency_contact_name
├── emergency_contact_phone
├── address
├── city
├── province
├── postal_code
├── hire_date
├── employment_status (enum: active, inactive, terminated, suspended)
├── employee_type_id (Foreign Key)
├── department_id (Foreign Key)
├── position_id (Foreign Key)
├── supervisor_id (Foreign Key to employees - self-referencing)
├── salary_grade
├── basic_salary
├── created_at
├── updated_at
└── deleted_at
```

### 3. Employee Types (Indonesian Healthcare Context)
```sql
employee_types
├── id (Primary Key)
├── name (e.g., <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bidan, Tenaga Administrasi)
├── code (e.g., DS, DU, PER, BID, ADM)
├── description
├── requires_license (boolean)
├── minimum_education_level
├── created_at
└── updated_at
```

### 4. Departments/Units
```sql
departments
├── id (Primary Key)
├── name (e.g., IGD, ICU, Rawat Inap, Laboratorium, Radiologi)
├── code (e.g., ER, ICU, WARD, LAB, RAD)
├── description
├── parent_department_id (Self-referencing for sub-departments)
├── department_head_id (Foreign Key to employees)
├── location
├── phone_extension
├── is_active (boolean)
├── created_at
└── updated_at
```

### 5. Positions/Job Titles
```sql
positions
├── id (Primary Key)
├── title (e.g., Kepala Departemen, Supervisor, Staff)
├── level (enum: executive, manager, supervisor, staff)
├── description
├── responsibilities (text)
├── requirements (text)
├── created_at
└── updated_at
```

### 6. Professional Licenses & Certifications
```sql
employee_licenses
├── id (Primary Key)
├── employee_id (Foreign Key)
├── license_type (e.g., STR, SIP, Sertifikat Kompetensi)
├── license_number
├── issuing_authority
├── issue_date
├── expiry_date
├── status (enum: active, expired, suspended, revoked)
├── document_path (file storage)
├── created_at
└── updated_at
```

### 7. Shift Management
```sql
shifts
├── id (Primary Key)
├── name (e.g., Pagi, Siang, Malam, Jaga)
├── start_time
├── end_time
├── duration_hours
├── is_overnight (boolean)
├── shift_type (enum: regular, on_call, emergency)
├── created_at
└── updated_at
```

### 8. Employee Shift Assignments
```sql
employee_shifts
├── id (Primary Key)
├── employee_id (Foreign Key)
├── shift_id (Foreign Key)
├── department_id (Foreign Key)
├── date
├── status (enum: scheduled, confirmed, completed, absent, sick_leave)
├── check_in_time
├── check_out_time
├── break_start_time
├── break_end_time
├── overtime_hours
├── notes
├── created_by (Foreign Key to users)
├── created_at
└── updated_at
```

### 9. Leave Management
```sql
leave_types
├── id (Primary Key)
├── name (e.g., Cuti Tahunan, Cuti Sakit, Cuti Melahirkan, Cuti Haji)
├── code
├── max_days_per_year
├── requires_medical_certificate (boolean)
├── is_paid (boolean)
├── created_at
└── updated_at

leave_requests
├── id (Primary Key)
├── employee_id (Foreign Key)
├── leave_type_id (Foreign Key)
├── start_date
├── end_date
├── total_days
├── reason
├── status (enum: pending, approved, rejected, cancelled)
├── approved_by (Foreign Key to employees)
├── approved_at
├── rejection_reason
├── medical_certificate_path
├── created_at
└── updated_at
```

### 10. Performance Evaluation
```sql
performance_evaluations
├── id (Primary Key)
├── employee_id (Foreign Key)
├── evaluator_id (Foreign Key to employees)
├── evaluation_period_start
├── evaluation_period_end
├── overall_score
├── technical_skills_score
├── communication_score
├── teamwork_score
├── punctuality_score
├── patient_care_score (for medical staff)
├── comments
├── goals_next_period
├── status (enum: draft, submitted, approved)
├── created_at
└── updated_at
```

### 11. Training & Education
```sql
training_programs
├── id (Primary Key)
├── title
├── description
├── duration_hours
├── training_type (enum: mandatory, optional, certification)
├── provider
├── cost
├── is_active (boolean)
├── created_at
└── updated_at

employee_trainings
├── id (Primary Key)
├── employee_id (Foreign Key)
├── training_program_id (Foreign Key)
├── enrollment_date
├── completion_date
├── status (enum: enrolled, in_progress, completed, failed, cancelled)
├── score
├── certificate_path
├── expiry_date (for certifications)
├── created_at
└── updated_at
```

### 12. Payroll Integration
```sql
payroll_components
├── id (Primary Key)
├── name (e.g., Gaji Pokok, Tunjangan Jabatan, Tunjangan Kesehatan)
├── type (enum: earning, deduction)
├── calculation_method (enum: fixed, percentage, hourly)
├── is_taxable (boolean)
├── created_at
└── updated_at

employee_payroll
├── id (Primary Key)
├── employee_id (Foreign Key)
├── payroll_period_start
├── payroll_period_end
├── basic_salary
├── overtime_amount
├── allowances_total
├── deductions_total
├── gross_salary
├── tax_amount
├── net_salary
├── status (enum: draft, calculated, approved, paid)
├── processed_by (Foreign Key to users)
├── processed_at
├── created_at
└── updated_at
```

### 13. Audit Trail
```sql
employee_audit_logs
├── id (Primary Key)
├── employee_id (Foreign Key)
├── action (enum: created, updated, deleted, status_changed)
├── field_changed
├── old_value
├── new_value
├── changed_by (Foreign Key to users)
├── ip_address
├── user_agent
├── created_at
```

## Key Relationships and Constraints

### Foreign Key Relationships
1. employees.user_id → users.id
2. employees.employee_type_id → employee_types.id
3. employees.department_id → departments.id
4. employees.position_id → positions.id
5. employees.supervisor_id → employees.id (self-referencing)
6. departments.parent_department_id → departments.id (self-referencing)
7. departments.department_head_id → employees.id
8. employee_licenses.employee_id → employees.id
9. employee_shifts.employee_id → employees.id
10. employee_shifts.shift_id → shifts.id
11. employee_shifts.department_id → departments.id
12. leave_requests.employee_id → employees.id
13. leave_requests.leave_type_id → leave_types.id
14. performance_evaluations.employee_id → employees.id
15. performance_evaluations.evaluator_id → employees.id

### Indexes for Performance
```sql
-- Employee search and filtering
CREATE INDEX idx_employees_department_status ON employees(department_id, employment_status);
CREATE INDEX idx_employees_type_status ON employees(employee_type_id, employment_status);
CREATE INDEX idx_employees_supervisor ON employees(supervisor_id);
CREATE INDEX idx_employees_hire_date ON employees(hire_date);

-- Shift management
CREATE INDEX idx_employee_shifts_date_employee ON employee_shifts(date, employee_id);
CREATE INDEX idx_employee_shifts_department_date ON employee_shifts(department_id, date);

-- Leave management
CREATE INDEX idx_leave_requests_employee_status ON leave_requests(employee_id, status);
CREATE INDEX idx_leave_requests_date_range ON leave_requests(start_date, end_date);

-- License tracking
CREATE INDEX idx_employee_licenses_expiry ON employee_licenses(expiry_date, status);
```

## Indonesian Healthcare Specific Considerations

### 1. Employee Types Mapping
- **Dokter Spesialis** (Specialist Doctor): Requires STR + SIP + Specialist Certificate
- **Dokter Umum** (General Practitioner): Requires STR + SIP
- **Perawat** (Nurse): Requires STR + SIP for nurses
- **Bidan** (Midwife): Requires STR + SIP for midwives
- **Tenaga Administrasi** (Administrative Staff): No medical license required
- **Tenaga Penunjang** (Support Staff): May require specific certifications

### 2. Shift Patterns
- **Pagi** (Morning): 07:00 - 14:00
- **Siang** (Afternoon): 14:00 - 21:00
- **Malam** (Night): 21:00 - 07:00
- **Jaga** (On-call): 24-hour availability

### 3. Leave Types (Indonesian Context)
- **Cuti Tahunan** (Annual Leave): 12 days per year
- **Cuti Sakit** (Sick Leave): With medical certificate
- **Cuti Melahirkan** (Maternity Leave): 3 months
- **Cuti Haji** (Hajj Leave): Once in a lifetime
- **Cuti Besar** (Long Service Leave): After certain years of service

### 4. Compliance Requirements
- STR (Surat Tanda Registrasi) renewal tracking
- SIP (Surat Izin Praktik) renewal tracking
- Continuing Medical Education (CME) credits tracking
- Hospital accreditation requirements compliance
