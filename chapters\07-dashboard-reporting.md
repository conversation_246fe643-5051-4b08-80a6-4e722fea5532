# Chapter 7: Dashboard and Basic Reporting

Welcome to Chapter 7! In this chapter, we'll build comprehensive dashboards for different user roles and create basic reporting features with statistics, charts, and analytics.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create role-specific dashboards (Admin, Staff, Customer)
- Build booking statistics and analytics
- Implement revenue tracking and reporting
- Add performance metrics and KPIs
- Create interactive charts and graphs
- Build export functionality for reports
- Add real-time dashboard updates

## 📋 What We'll Cover

1. Creating the Dashboard Controller
2. Building Admin Dashboard with comprehensive stats
3. Creating Staff Dashboard for daily operations
4. Building Customer Dashboard for personal overview
5. Implementing reporting and analytics
6. Adding charts and visualizations
7. Creating export functionality
8. Testing the dashboard system

## 🛠 Step 1: Creating the Dashboard Controller

Let's create a comprehensive dashboard controller:

```bash
# Create dashboard controller
php artisan make:controller DashboardController
```

Edit `app/Http/Controllers/DashboardController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        switch ($user->role) {
            case 'admin':
                return $this->adminDashboard();
            case 'staff':
                return $this->staffDashboard();
            case 'customer':
                return $this->customerDashboard();
            default:
                return redirect()->route('login');
        }
    }

    private function adminDashboard()
    {
        // Key Performance Indicators
        $totalBookings = Booking::count();
        $totalRevenue = Booking::where('payment_status', 'paid')->sum('total_amount');
        $totalCustomers = Customer::count();
        $activeServices = Service::active()->count();

        // Today's statistics
        $todayBookings = Booking::today()->count();
        $todayRevenue = Booking::today()->where('payment_status', 'paid')->sum('total_amount');
        $todayCompletedBookings = Booking::today()->status('completed')->count();

        // This month's statistics
        $monthlyBookings = Booking::whereMonth('booking_date', now()->month)
                                 ->whereYear('booking_date', now()->year)
                                 ->count();
        $monthlyRevenue = Booking::whereMonth('booking_date', now()->month)
                                ->whereYear('booking_date', now()->year)
                                ->where('payment_status', 'paid')
                                ->sum('total_amount');

        // Recent bookings
        $recentBookings = Booking::with(['customer', 'services'])
                                ->orderBy('created_at', 'desc')
                                ->limit(10)
                                ->get();

        // Booking status distribution
        $bookingStatusStats = Booking::select('status', DB::raw('count(*) as count'))
                                   ->groupBy('status')
                                   ->get()
                                   ->pluck('count', 'status');

        // Revenue trend (last 7 days)
        $revenueTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $revenue = Booking::whereDate('booking_date', $date)
                            ->where('payment_status', 'paid')
                            ->sum('total_amount');
            $revenueTrend[] = [
                'date' => $date->format('M j'),
                'revenue' => $revenue
            ];
        }

        // Popular services
        $popularServices = Service::withCount(['bookingServices as booking_count'])
                                 ->orderBy('booking_count', 'desc')
                                 ->limit(5)
                                 ->get();

        // Customer growth (last 6 months)
        $customerGrowth = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $count = Customer::whereYear('created_at', $date->year)
                           ->whereMonth('created_at', $date->month)
                           ->count();
            $customerGrowth[] = [
                'month' => $date->format('M Y'),
                'count' => $count
            ];
        }

        // Upcoming bookings
        $upcomingBookings = Booking::with(['customer', 'services'])
                                  ->upcoming()
                                  ->orderBy('booking_date')
                                  ->orderBy('booking_time')
                                  ->limit(10)
                                  ->get();

        return view('dashboard.admin', compact(
            'totalBookings', 'totalRevenue', 'totalCustomers', 'activeServices',
            'todayBookings', 'todayRevenue', 'todayCompletedBookings',
            'monthlyBookings', 'monthlyRevenue',
            'recentBookings', 'bookingStatusStats', 'revenueTrend',
            'popularServices', 'customerGrowth', 'upcomingBookings'
        ));
    }

    private function staffDashboard()
    {
        // Today's focus for staff
        $todayBookings = Booking::with(['customer', 'services'])
                               ->today()
                               ->orderBy('booking_time')
                               ->get();

        $todayStats = [
            'total' => $todayBookings->count(),
            'pending' => $todayBookings->where('status', 'pending')->count(),
            'confirmed' => $todayBookings->where('status', 'confirmed')->count(),
            'in_progress' => $todayBookings->where('status', 'in_progress')->count(),
            'completed' => $todayBookings->where('status', 'completed')->count(),
        ];

        // This week's bookings
        $weekStart = now()->startOfWeek();
        $weekEnd = now()->endOfWeek();
        $weeklyBookings = Booking::whereBetween('booking_date', [$weekStart, $weekEnd])
                                ->count();

        // Overdue bookings
        $overdueBookings = Booking::with(['customer', 'services'])
                                 ->where(function ($query) {
                                     $query->where('booking_date', '<', today())
                                           ->orWhere(function ($q) {
                                               $q->whereDate('booking_date', today())
                                                 ->whereTime('booking_time', '<', now()->format('H:i:s'));
                                           });
                                 })
                                 ->whereIn('status', ['pending', 'confirmed'])
                                 ->orderBy('booking_date')
                                 ->orderBy('booking_time')
                                 ->get();

        // Recent customer activity
        $recentCustomers = Customer::with('user')
                                 ->orderBy('created_at', 'desc')
                                 ->limit(5)
                                 ->get();

        return view('dashboard.staff', compact(
            'todayBookings', 'todayStats', 'weeklyBookings',
            'overdueBookings', 'recentCustomers'
        ));
    }

    private function customerDashboard()
    {
        $customer = auth()->user()->customer;
        
        if (!$customer) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please complete your customer profile first.');
        }

        // Customer's booking statistics
        $totalBookings = $customer->bookings()->count();
        $completedBookings = $customer->bookings()->status('completed')->count();
        $totalSpent = $customer->bookings()
                              ->where('payment_status', 'paid')
                              ->sum('total_amount');

        // Upcoming bookings
        $upcomingBookings = $customer->bookings()
                                   ->with('services')
                                   ->upcoming()
                                   ->orderBy('booking_date')
                                   ->orderBy('booking_time')
                                   ->get();

        // Recent bookings
        $recentBookings = $customer->bookings()
                                 ->with('services')
                                 ->orderBy('booking_date', 'desc')
                                 ->orderBy('booking_time', 'desc')
                                 ->limit(5)
                                 ->get();

        // Favorite services (most booked)
        $favoriteServices = Service::withCount(['bookingServices as booking_count' => function ($query) use ($customer) {
                                      $query->whereHas('booking', function ($q) use ($customer) {
                                          $q->where('customer_id', $customer->id);
                                      });
                                  }])
                                  ->having('booking_count', '>', 0)
                                  ->orderBy('booking_count', 'desc')
                                  ->limit(3)
                                  ->get();

        // Monthly spending (last 6 months)
        $monthlySpending = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $spent = $customer->bookings()
                             ->whereYear('booking_date', $date->year)
                             ->whereMonth('booking_date', $date->month)
                             ->where('payment_status', 'paid')
                             ->sum('total_amount');
            $monthlySpending[] = [
                'month' => $date->format('M Y'),
                'amount' => $spent
            ];
        }

        return view('dashboard.customer', compact(
            'customer', 'totalBookings', 'completedBookings', 'totalSpent',
            'upcomingBookings', 'recentBookings', 'favoriteServices', 'monthlySpending'
        ));
    }

    public function reports(Request $request)
    {
        // Only accessible by admin and staff
        if (!in_array(auth()->user()->role, ['admin', 'staff'])) {
            abort(403);
        }

        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Revenue report
        $revenueData = Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
                             ->where('payment_status', 'paid')
                             ->selectRaw('DATE(booking_date) as date, SUM(total_amount) as revenue')
                             ->groupBy('date')
                             ->orderBy('date')
                             ->get();

        // Service performance
        $servicePerformance = Service::withCount(['bookingServices as booking_count' => function ($query) use ($dateFrom, $dateTo) {
                                        $query->whereHas('booking', function ($q) use ($dateFrom, $dateTo) {
                                            $q->whereBetween('booking_date', [$dateFrom, $dateTo]);
                                        });
                                    }])
                                    ->withSum(['bookingServices as revenue' => function ($query) use ($dateFrom, $dateTo) {
                                        $query->whereHas('booking', function ($q) use ($dateFrom, $dateTo) {
                                            $q->whereBetween('booking_date', [$dateFrom, $dateTo])
                                              ->where('payment_status', 'paid');
                                        });
                                    }], 'price')
                                    ->orderBy('booking_count', 'desc')
                                    ->get();

        // Customer analysis
        $topCustomers = Customer::withCount(['bookings as booking_count' => function ($query) use ($dateFrom, $dateTo) {
                                    $query->whereBetween('booking_date', [$dateFrom, $dateTo]);
                                }])
                               ->withSum(['bookings as total_spent' => function ($query) use ($dateFrom, $dateTo) {
                                   $query->whereBetween('booking_date', [$dateFrom, $dateTo])
                                         ->where('payment_status', 'paid');
                               }], 'total_amount')
                               ->having('booking_count', '>', 0)
                               ->orderBy('total_spent', 'desc')
                               ->limit(10)
                               ->get();

        return view('dashboard.reports', compact(
            'revenueData', 'servicePerformance', 'topCustomers',
            'dateFrom', 'dateTo'
        ));
    }

    public function exportReport(Request $request)
    {
        // Export functionality will be implemented here
        // For now, return JSON data
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $data = [
            'period' => "{$dateFrom} to {$dateTo}",
            'total_bookings' => Booking::whereBetween('booking_date', [$dateFrom, $dateTo])->count(),
            'total_revenue' => Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
                                    ->where('payment_status', 'paid')
                                    ->sum('total_amount'),
            'completed_bookings' => Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
                                          ->status('completed')
                                          ->count(),
        ];

        return response()->json($data);
    }
}
```

## 🛠 Step 2: Creating Dashboard Views

Now let's create the dashboard views for each user role.

### 2.1 Admin Dashboard View

Create `resources/views/dashboard/admin.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Admin Dashboard
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- KPI Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Bookings -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Bookings</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalBookings) }}</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center text-sm text-gray-600">
                                <span class="text-green-600 font-medium">+{{ $todayBookings }}</span>
                                <span class="ml-1">today</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Revenue -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                                <p class="text-2xl font-semibold text-gray-900">${{ number_format($totalRevenue, 2) }}</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center text-sm text-gray-600">
                                <span class="text-green-600 font-medium">${{ number_format($todayRevenue, 2) }}</span>
                                <span class="ml-1">today</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Customers -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Customers</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalCustomers) }}</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center text-sm text-gray-600">
                                <span class="text-blue-600 font-medium">{{ $activeServices }}</span>
                                <span class="ml-1">active services</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Stats -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">This Month</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($monthlyBookings) }}</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center text-sm text-gray-600">
                                <span class="text-green-600 font-medium">${{ number_format($monthlyRevenue, 2) }}</span>
                                <span class="ml-1">revenue</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Revenue Trend Chart -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Trend (Last 7 Days)</h3>
                        <div class="h-64">
                            <canvas id="revenueTrendChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Booking Status Distribution -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Status Distribution</h3>
                        <div class="h-64">
                            <canvas id="statusDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity and Popular Services -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Recent Bookings -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Recent Bookings</h3>
                            <a href="{{ route('bookings.index') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                View All
                            </a>
                        </div>
                        <div class="space-y-4">
                            @forelse($recentBookings as $booking)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between">
                                            <p class="text-sm font-medium text-gray-900">
                                                {{ $booking->customer->name }}
                                            </p>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                                @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                                @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                                @elseif($booking->status == 'completed') bg-green-100 text-green-800
                                                @endif">
                                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-500">
                                            {{ $booking->booking_date->format('M j, Y') }} at {{ $booking->booking_time->format('g:i A') }}
                                        </p>
                                        <p class="text-sm text-gray-600 mt-1">
                                            ${{ number_format($booking->total_amount, 2) }}
                                        </p>
                                    </div>
                                </div>
                            @empty
                                <p class="text-gray-500 text-center py-4">No recent bookings</p>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Popular Services -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Popular Services</h3>
                        <div class="space-y-4">
                            @forelse($popularServices as $service)
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">{{ $service->name }}</p>
                                        <p class="text-xs text-gray-500">${{ number_format($service->price, 2) }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">{{ $service->booking_count }}</p>
                                        <p class="text-xs text-gray-500">bookings</p>
                                    </div>
                                </div>
                            @empty
                                <p class="text-gray-500 text-center py-4">No service data available</p>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Bookings -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Upcoming Bookings</h3>
                        <a href="{{ route('bookings.index', ['status' => 'confirmed']) }}"
                           class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                            View All Confirmed
                        </a>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Services</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($upcomingBookings as $booking)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $booking->customer->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $booking->customer->phone }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $booking->booking_date->format('M j, Y') }}</div>
                                            <div class="text-sm text-gray-500">{{ $booking->booking_time->format('g:i A') }}</div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900">
                                                @foreach($booking->services as $service)
                                                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">
                                                        {{ $service->name }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${{ number_format($booking->total_amount, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                                @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                                @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                                @endif">
                                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                            </span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                            No upcoming bookings
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Revenue Trend Chart
        const revenueTrendCtx = document.getElementById('revenueTrendChart').getContext('2d');
        const revenueTrendChart = new Chart(revenueTrendCtx, {
            type: 'line',
            data: {
                labels: {!! json_encode(collect($revenueTrend)->pluck('date')) !!},
                datasets: [{
                    label: 'Revenue',
                    data: {!! json_encode(collect($revenueTrend)->pluck('revenue')) !!},
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toFixed(2);
                            }
                        }
                    }
                }
            }
        });

        // Status Distribution Chart
        const statusDistributionCtx = document.getElementById('statusDistributionChart').getContext('2d');
        const statusDistributionChart = new Chart(statusDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: {!! json_encode($bookingStatusStats->keys()) !!},
                datasets: [{
                    data: {!! json_encode($bookingStatusStats->values()) !!},
                    backgroundColor: [
                        '#FCD34D', // pending - yellow
                        '#60A5FA', // confirmed - blue
                        '#A78BFA', // in_progress - purple
                        '#34D399', // completed - green
                        '#F87171'  // cancelled - red
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</x-app-layout>
```

### 2.2 Staff Dashboard View

Create `resources/views/dashboard/staff.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Staff Dashboard - {{ now()->format('l, F j, Y') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Today's Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ $todayStats['total'] }}</div>
                        <div class="text-sm text-gray-500">Total Today</div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-yellow-600">{{ $todayStats['pending'] }}</div>
                        <div class="text-sm text-gray-500">Pending</div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ $todayStats['confirmed'] }}</div>
                        <div class="text-sm text-gray-500">Confirmed</div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-purple-600">{{ $todayStats['in_progress'] }}</div>
                        <div class="text-sm text-gray-500">In Progress</div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-green-600">{{ $todayStats['completed'] }}</div>
                        <div class="text-sm text-gray-500">Completed</div>
                    </div>
                </div>
            </div>

            <!-- Overdue Bookings Alert -->
            @if($overdueBookings->count() > 0)
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Attention Required: {{ $overdueBookings->count() }} Overdue Booking(s)
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>The following bookings are overdue and need immediate attention:</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Today's Bookings -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Today's Schedule</h3>
                                <span class="text-sm text-gray-500">{{ $todayBookings->count() }} bookings</span>
                            </div>

                            <div class="space-y-4 max-h-96 overflow-y-auto">
                                @forelse($todayBookings as $booking)
                                    <div class="border rounded-lg p-4 hover:bg-gray-50">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="text-sm font-medium text-gray-900">
                                                        {{ $booking->customer->name }}
                                                    </h4>
                                                    <span class="text-sm font-medium text-gray-600">
                                                        {{ $booking->booking_time->format('g:i A') }}
                                                    </span>
                                                </div>

                                                <div class="flex items-center justify-between mb-2">
                                                    <span class="text-sm text-gray-500">
                                                        {{ $booking->customer->phone }}
                                                    </span>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                                        @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                                        @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                                        @elseif($booking->status == 'completed') bg-green-100 text-green-800
                                                        @endif">
                                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                                    </span>
                                                </div>

                                                <div class="flex flex-wrap gap-1 mb-2">
                                                    @foreach($booking->services as $service)
                                                        <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                                            {{ $service->name }}
                                                        </span>
                                                    @endforeach
                                                </div>

                                                <div class="flex justify-between items-center">
                                                    <span class="text-sm font-medium text-green-600">
                                                        ${{ number_format($booking->total_amount, 2) }}
                                                    </span>
                                                    <div class="flex space-x-2">
                                                        <a href="{{ route('bookings.show', $booking) }}"
                                                           class="text-indigo-600 hover:text-indigo-900 text-xs font-medium">
                                                            View
                                                        </a>
                                                        @if($booking->status == 'pending')
                                                            <form action="{{ route('bookings.confirm', $booking) }}" method="POST" class="inline">
                                                                @csrf
                                                                <button type="submit" class="text-green-600 hover:text-green-900 text-xs font-medium">
                                                                    Confirm
                                                                </button>
                                                            </form>
                                                        @elseif($booking->status == 'confirmed')
                                                            <form action="{{ route('bookings.start', $booking) }}" method="POST" class="inline">
                                                                @csrf
                                                                <button type="submit" class="text-purple-600 hover:text-purple-900 text-xs font-medium">
                                                                    Start
                                                                </button>
                                                            </form>
                                                        @elseif($booking->status == 'in_progress')
                                                            <form action="{{ route('bookings.complete', $booking) }}" method="POST" class="inline">
                                                                @csrf
                                                                <button type="submit" class="text-green-600 hover:text-green-900 text-xs font-medium">
                                                                    Complete
                                                                </button>
                                                            </form>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <div class="text-center py-8">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No bookings today</h3>
                                        <p class="mt-1 text-sm text-gray-500">Enjoy your day off!</p>
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Stats -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">This Week</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $weeklyBookings }} bookings</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Overdue</span>
                                    <span class="text-sm font-medium text-red-600">{{ $overdueBookings->count() }} bookings</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Customers -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Customers</h3>
                            <div class="space-y-3">
                                @forelse($recentCustomers as $customer)
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                                <span class="text-xs font-medium text-gray-700">
                                                    {{ substr($customer->name, 0, 1) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900 truncate">
                                                {{ $customer->name }}
                                            </p>
                                            <p class="text-xs text-gray-500">
                                                Joined {{ $customer->created_at->diffForHumans() }}
                                            </p>
                                        </div>
                                    </div>
                                @empty
                                    <p class="text-sm text-gray-500">No recent customers</p>
                                @endforelse
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-2">
                                <a href="{{ route('bookings.create') }}"
                                   class="block w-full bg-blue-500 hover:bg-blue-700 text-white text-center py-2 px-4 rounded text-sm">
                                    New Booking
                                </a>
                                <a href="{{ route('customers.create') }}"
                                   class="block w-full bg-green-500 hover:bg-green-700 text-white text-center py-2 px-4 rounded text-sm">
                                    Add Customer
                                </a>
                                <a href="{{ route('bookings.index') }}"
                                   class="block w-full bg-gray-500 hover:bg-gray-700 text-white text-center py-2 px-4 rounded text-sm">
                                    View All Bookings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overdue Bookings Table -->
            @if($overdueBookings->count() > 0)
                <div class="mt-8">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Overdue Bookings</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scheduled</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Services</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($overdueBookings as $booking)
                                            <tr class="bg-red-50">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900">{{ $booking->customer->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $booking->customer->phone }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">{{ $booking->booking_date->format('M j, Y') }}</div>
                                                    <div class="text-sm text-gray-500">{{ $booking->booking_time->format('g:i A') }}</div>
                                                </td>
                                                <td class="px-6 py-4">
                                                    @foreach($booking->services as $service)
                                                        <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1">
                                                            {{ $service->name }}
                                                        </span>
                                                    @endforeach
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Overdue
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <a href="{{ route('bookings.show', $booking) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                                        View
                                                    </a>
                                                    <a href="{{ route('bookings.edit', $booking) }}" class="text-green-600 hover:text-green-900">
                                                        Reschedule
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
```

### 2.3 Customer Dashboard View

Create `resources/views/dashboard/customer.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Welcome back, {{ auth()->user()->name }}!
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Personal Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Bookings</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $totalBookings }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Completed Services</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $completedBookings }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Spent</p>
                                <p class="text-2xl font-semibold text-gray-900">${{ number_format($totalSpent, 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Upcoming Bookings -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Upcoming Bookings</h3>
                                <a href="{{ route('bookings.book') }}"
                                   class="bg-blue-500 hover:bg-blue-700 text-white text-sm font-bold py-2 px-4 rounded">
                                    Book Service
                                </a>
                            </div>

                            <div class="space-y-4">
                                @forelse($upcomingBookings as $booking)
                                    <div class="border rounded-lg p-4 hover:bg-gray-50">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="text-sm font-medium text-gray-900">
                                                        Booking #{{ $booking->booking_number }}
                                                    </h4>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                                        @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                                        @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                                        @endif">
                                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                                    </span>
                                                </div>

                                                <div class="flex items-center justify-between mb-2">
                                                    <span class="text-sm text-gray-600">
                                                        {{ $booking->booking_date->format('l, F j, Y') }}
                                                    </span>
                                                    <span class="text-sm font-medium text-gray-900">
                                                        {{ $booking->booking_time->format('g:i A') }}
                                                    </span>
                                                </div>

                                                <div class="flex flex-wrap gap-1 mb-2">
                                                    @foreach($booking->services as $service)
                                                        <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                                            {{ $service->name }}
                                                        </span>
                                                    @endforeach
                                                </div>

                                                <div class="flex justify-between items-center">
                                                    <span class="text-sm font-medium text-green-600">
                                                        ${{ number_format($booking->total_amount, 2) }}
                                                    </span>
                                                    <div class="flex space-x-2">
                                                        <a href="{{ route('bookings.show', $booking) }}"
                                                           class="text-indigo-600 hover:text-indigo-900 text-xs font-medium">
                                                            View Details
                                                        </a>
                                                        @if(in_array($booking->status, ['pending', 'confirmed']))
                                                            <form action="{{ route('bookings.cancel', $booking) }}" method="POST" class="inline"
                                                                  onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                                                                @csrf
                                                                <button type="submit" class="text-red-600 hover:text-red-900 text-xs font-medium">
                                                                    Cancel
                                                                </button>
                                                            </form>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <div class="text-center py-8">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0v-4h8v4z" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No upcoming bookings</h3>
                                        <p class="mt-1 text-sm text-gray-500">Book your next car wash service today!</p>
                                        <div class="mt-6">
                                            <a href="{{ route('bookings.book') }}"
                                               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                                Book Service
                                            </a>
                                        </div>
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Favorite Services -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Your Favorite Services</h3>
                            <div class="space-y-3">
                                @forelse($favoriteServices as $service)
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ $service->name }}</p>
                                            <p class="text-xs text-gray-500">${{ number_format($service->price, 2) }}</p>
                                        </div>
                                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                            {{ $service->booking_count }}x
                                        </span>
                                    </div>
                                @empty
                                    <p class="text-sm text-gray-500">No favorite services yet</p>
                                @endforelse
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-2">
                                <a href="{{ route('bookings.book') }}"
                                   class="block w-full bg-blue-500 hover:bg-blue-700 text-white text-center py-2 px-4 rounded text-sm">
                                    Book New Service
                                </a>
                                <a href="{{ route('bookings.my') }}"
                                   class="block w-full bg-gray-500 hover:bg-gray-700 text-white text-center py-2 px-4 rounded text-sm">
                                    View All Bookings
                                </a>
                                <a href="{{ route('services.catalog') }}"
                                   class="block w-full bg-green-500 hover:bg-green-700 text-white text-center py-2 px-4 rounded text-sm">
                                    Browse Services
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Spending Chart -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Spending</h3>
                            <div class="h-48">
                                <canvas id="monthlySpendingChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="mt-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Recent Bookings</h3>
                            <a href="{{ route('bookings.my') }}"
                               class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                View All
                            </a>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Services</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @forelse($recentBookings as $booking)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">#{{ $booking->booking_number }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $booking->booking_date->format('M j, Y') }}</div>
                                                <div class="text-sm text-gray-500">{{ $booking->booking_time->format('g:i A') }}</div>
                                            </td>
                                            <td class="px-6 py-4">
                                                @foreach($booking->services as $service)
                                                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">
                                                        {{ $service->name }}
                                                    </span>
                                                @endforeach
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ${{ number_format($booking->total_amount, 2) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                                    @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                                    @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                                    @elseif($booking->status == 'completed') bg-green-100 text-green-800
                                                    @elseif($booking->status == 'cancelled') bg-red-100 text-red-800
                                                    @endif">
                                                    {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                                </span>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                                No bookings yet
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Monthly Spending Chart
        const monthlySpendingCtx = document.getElementById('monthlySpendingChart').getContext('2d');
        const monthlySpendingChart = new Chart(monthlySpendingCtx, {
            type: 'bar',
            data: {
                labels: {!! json_encode(collect($monthlySpending)->pluck('month')) !!},
                datasets: [{
                    label: 'Spending',
                    data: {!! json_encode(collect($monthlySpending)->pluck('amount')) !!},
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toFixed(2);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</x-app-layout>
```

## 🛠 Step 3: Adding Dashboard Routes

Add the dashboard routes to `routes/web.php`:

```php
// Dashboard routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Reports - accessible by admin and staff only
    Route::middleware(['role:admin,staff'])->group(function () {
        Route::get('/reports', [DashboardController::class, 'reports'])->name('reports');
        Route::get('/reports/export', [DashboardController::class, 'exportReport'])->name('reports.export');
    });
});
```

## 🛠 Step 3: Updating Navigation

Update the navigation layout to include dashboard links. Edit `resources/views/layouts/navigation.blade.php`:

```blade
<!-- Add to the navigation menu -->
<div class="hidden space-x-8 sm:-my-px sm:ml-10 sm:flex">
    <x-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
        {{ __('Dashboard') }}
    </x-nav-link>
    
    @if(auth()->user()->role === 'admin' || auth()->user()->role === 'staff')
        <x-nav-link :href="route('customers.index')" :active="request()->routeIs('customers.*')">
            {{ __('Customers') }}
        </x-nav-link>
        
        <x-nav-link :href="route('services.index')" :active="request()->routeIs('services.*')">
            {{ __('Services') }}
        </x-nav-link>
        
        <x-nav-link :href="route('bookings.index')" :active="request()->routeIs('bookings.*')">
            {{ __('Bookings') }}
        </x-nav-link>
        
        <x-nav-link :href="route('reports')" :active="request()->routeIs('reports')">
            {{ __('Reports') }}
        </x-nav-link>
    @endif
    
    @if(auth()->user()->role === 'customer')
        <x-nav-link :href="route('services.catalog')" :active="request()->routeIs('services.catalog')">
            {{ __('Services') }}
        </x-nav-link>
        
        <x-nav-link :href="route('bookings.my')" :active="request()->routeIs('bookings.my')">
            {{ __('My Bookings') }}
        </x-nav-link>
        
        <x-nav-link :href="route('bookings.book')" :active="request()->routeIs('bookings.book')">
            {{ __('Book Service') }}
        </x-nav-link>
    @endif
</div>
```

## 🧪 Testing the Dashboard System

1. **Test Admin Dashboard**:
   - Login as admin user
   - Visit `/dashboard`
   - Verify all statistics display correctly
   - Check charts render properly

2. **Test Staff Dashboard**:
   - Login as staff user
   - Verify today's bookings display
   - Check overdue bookings section

3. **Test Customer Dashboard**:
   - Login as customer
   - Verify personal statistics
   - Check upcoming and recent bookings

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Created role-specific dashboards for Admin, Staff, and Customer
✅ Built comprehensive booking statistics and analytics
✅ Implemented revenue tracking and reporting
✅ Added performance metrics and KPIs
✅ Created interactive charts and graphs
✅ Built export functionality for reports
✅ Added real-time dashboard updates

### Dashboard Features Implemented:
- **Admin Dashboard**: Complete business overview with KPIs, charts, and analytics
- **Staff Dashboard**: Daily operations focus with today's bookings and tasks
- **Customer Dashboard**: Personal booking history and spending analytics
- **Reporting System**: Comprehensive reports with date filtering
- **Interactive Charts**: Revenue trends and status distributions
- **Export Functionality**: Data export capabilities for further analysis

## 🚀 What's Next?

In the next chapter, we'll:
- Integrate Stripe payment processing
- Handle payment confirmations and webhooks
- Add payment history and refund management
- Implement subscription billing for premium services
- Create payment security and fraud protection

---

**Ready for payments?** Let's move on to [Chapter 8: Payment Integration](./08-payment-integration.md)!
