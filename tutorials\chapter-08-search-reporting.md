# Chapter 8: Advanced Search and Reporting

## Overview
In this chapter, we'll implement comprehensive search functionality and reporting system for the Hospital Employee Management System. We'll build advanced search capabilities, generate various reports, and create data visualization components with Indonesian healthcare context.

## Learning Objectives
- Create advanced search functionality across all entities
- Implement comprehensive reporting system
- Build data export capabilities (PDF, Excel, CSV)
- Create data visualization and analytics
- Implement real-time search with filters
- Build responsive React components for search and reports

## Prerequisites
- Completed Chapter 1-7
- Understanding of search algorithms and indexing
- Familiarity with data visualization concepts

## Duration
90-120 minutes

---

## Step 1: Create Search Service

### 1.1 Create Advanced Search Service

```bash
php artisan make:service SearchService
```

Create `app/Services/SearchService.php`:

```php
<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\Department;
use App\Models\Shift;
use App\Models\EmployeeShift;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class SearchService
{
    /**
     * Global search across all entities
     */
    public function globalSearch(string $query, array $filters = []): array
    {
        $results = [
            'employees' => $this->searchEmployees($query, $filters),
            'departments' => $this->searchDepartments($query, $filters),
            'shifts' => $this->searchShifts($query, $filters),
        ];

        return [
            'results' => $results,
            'total_count' => array_sum(array_map('count', $results)),
            'query' => $query,
            'filters' => $filters,
        ];
    }

    /**
     * Search employees with advanced filters
     */
    public function searchEmployees(string $query = '', array $filters = []): Collection
    {
        $builder = Employee::with([
            'department',
            'position',
            'employeeType',
            'supervisor'
        ]);

        // Text search
        if (!empty($query)) {
            $builder->where(function (Builder $q) use ($query) {
                $q->where('first_name', 'like', "%{$query}%")
                  ->orWhere('last_name', 'like', "%{$query}%")
                  ->orWhere('employee_number', 'like', "%{$query}%")
                  ->orWhere('phone_number', 'like', "%{$query}%")
                  ->orWhereHas('department', function (Builder $dq) use ($query) {
                      $dq->where('name', 'like', "%{$query}%");
                  })
                  ->orWhereHas('position', function (Builder $pq) use ($query) {
                      $pq->where('title', 'like', "%{$query}%");
                  });
            });
        }

        // Apply filters
        $this->applyEmployeeFilters($builder, $filters);

        return $builder->limit(50)->get();
    }

    /**
     * Search departments with filters
     */
    public function searchDepartments(string $query = '', array $filters = []): Collection
    {
        $builder = Department::with([
            'parentDepartment',
            'departmentHead',
            'employees' => function ($q) {
                $q->where('employment_status', 'active');
            }
        ]);

        // Text search
        if (!empty($query)) {
            $builder->where(function (Builder $q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('code', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('location', 'like', "%{$query}%");
            });
        }

        // Apply filters
        $this->applyDepartmentFilters($builder, $filters);

        return $builder->limit(50)->get();
    }

    /**
     * Search shifts with filters
     */
    public function searchShifts(string $query = '', array $filters = []): Collection
    {
        $builder = Shift::with(['department']);

        // Text search
        if (!empty($query)) {
            $builder->where(function (Builder $q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhereHas('department', function (Builder $dq) use ($query) {
                      $dq->where('name', 'like', "%{$query}%");
                  });
            });
        }

        // Apply filters
        $this->applyShiftFilters($builder, $filters);

        return $builder->limit(50)->get();
    }

    /**
     * Advanced employee search with complex criteria
     */
    public function advancedEmployeeSearch(array $criteria): Collection
    {
        $builder = Employee::with([
            'department',
            'position',
            'employeeType',
            'supervisor',
            'subordinates'
        ]);

        // Age range
        if (isset($criteria['min_age']) || isset($criteria['max_age'])) {
            $minDate = isset($criteria['max_age']) 
                ? Carbon::now()->subYears($criteria['max_age'])->format('Y-m-d')
                : null;
            $maxDate = isset($criteria['min_age']) 
                ? Carbon::now()->subYears($criteria['min_age'])->format('Y-m-d')
                : null;

            if ($minDate && $maxDate) {
                $builder->whereBetween('date_of_birth', [$minDate, $maxDate]);
            } elseif ($minDate) {
                $builder->where('date_of_birth', '<=', $minDate);
            } elseif ($maxDate) {
                $builder->where('date_of_birth', '>=', $maxDate);
            }
        }

        // Years of service range
        if (isset($criteria['min_years_service']) || isset($criteria['max_years_service'])) {
            $minHireDate = isset($criteria['max_years_service']) 
                ? Carbon::now()->subYears($criteria['max_years_service'])->format('Y-m-d')
                : null;
            $maxHireDate = isset($criteria['min_years_service']) 
                ? Carbon::now()->subYears($criteria['min_years_service'])->format('Y-m-d')
                : null;

            if ($minHireDate && $maxHireDate) {
                $builder->whereBetween('hire_date', [$minHireDate, $maxHireDate]);
            } elseif ($minHireDate) {
                $builder->where('hire_date', '<=', $minHireDate);
            } elseif ($maxHireDate) {
                $builder->where('hire_date', '>=', $maxHireDate);
            }
        }

        // Medical staff filter
        if (isset($criteria['is_medical_staff'])) {
            $builder->whereHas('employeeType', function (Builder $q) use ($criteria) {
                $q->where('is_medical_staff', $criteria['is_medical_staff']);
            });
        }

        // Has subordinates filter
        if (isset($criteria['has_subordinates'])) {
            if ($criteria['has_subordinates']) {
                $builder->has('subordinates');
            } else {
                $builder->doesntHave('subordinates');
            }
        }

        // Location filter
        if (isset($criteria['locations']) && is_array($criteria['locations'])) {
            $builder->whereHas('department', function (Builder $q) use ($criteria) {
                $q->whereIn('location', $criteria['locations']);
            });
        }

        // Apply standard filters
        $this->applyEmployeeFilters($builder, $criteria);

        return $builder->get();
    }

    /**
     * Apply employee filters to query builder
     */
    private function applyEmployeeFilters(Builder $builder, array $filters): void
    {
        if (isset($filters['department_id'])) {
            $builder->where('department_id', $filters['department_id']);
        }

        if (isset($filters['employee_type_id'])) {
            $builder->where('employee_type_id', $filters['employee_type_id']);
        }

        if (isset($filters['position_id'])) {
            $builder->where('position_id', $filters['position_id']);
        }

        if (isset($filters['employment_status'])) {
            $builder->where('employment_status', $filters['employment_status']);
        }

        if (isset($filters['gender'])) {
            $builder->where('gender', $filters['gender']);
        }

        if (isset($filters['supervisor_id'])) {
            $builder->where('supervisor_id', $filters['supervisor_id']);
        }

        if (isset($filters['hire_date_from'])) {
            $builder->where('hire_date', '>=', $filters['hire_date_from']);
        }

        if (isset($filters['hire_date_to'])) {
            $builder->where('hire_date', '<=', $filters['hire_date_to']);
        }
    }

    /**
     * Apply department filters to query builder
     */
    private function applyDepartmentFilters(Builder $builder, array $filters): void
    {
        if (isset($filters['parent_department_id'])) {
            if ($filters['parent_department_id'] === 'null') {
                $builder->whereNull('parent_department_id');
            } else {
                $builder->where('parent_department_id', $filters['parent_department_id']);
            }
        }

        if (isset($filters['status'])) {
            $builder->where('status', $filters['status']);
        }

        if (isset($filters['has_head'])) {
            if ($filters['has_head']) {
                $builder->whereNotNull('department_head_id');
            } else {
                $builder->whereNull('department_head_id');
            }
        }

        if (isset($filters['min_capacity'])) {
            $builder->where('capacity', '>=', $filters['min_capacity']);
        }

        if (isset($filters['max_capacity'])) {
            $builder->where('capacity', '<=', $filters['max_capacity']);
        }
    }

    /**
     * Apply shift filters to query builder
     */
    private function applyShiftFilters(Builder $builder, array $filters): void
    {
        if (isset($filters['department_id'])) {
            $builder->where('department_id', $filters['department_id']);
        }

        if (isset($filters['shift_type'])) {
            $builder->where('shift_type', $filters['shift_type']);
        }

        if (isset($filters['status'])) {
            $builder->where('status', $filters['status']);
        }

        if (isset($filters['start_time_from'])) {
            $builder->where('start_time', '>=', $filters['start_time_from']);
        }

        if (isset($filters['start_time_to'])) {
            $builder->where('start_time', '<=', $filters['start_time_to']);
        }
    }

    /**
     * Get search suggestions based on partial query
     */
    public function getSearchSuggestions(string $query, int $limit = 10): array
    {
        $suggestions = [];

        // Employee suggestions
        $employees = Employee::where('first_name', 'like', "%{$query}%")
            ->orWhere('last_name', 'like', "%{$query}%")
            ->orWhere('employee_number', 'like', "%{$query}%")
            ->limit($limit)
            ->get(['id', 'first_name', 'last_name', 'employee_number']);

        foreach ($employees as $employee) {
            $suggestions[] = [
                'type' => 'employee',
                'id' => $employee->id,
                'text' => $employee->full_name,
                'subtitle' => $employee->employee_number,
            ];
        }

        // Department suggestions
        $departments = Department::where('name', 'like', "%{$query}%")
            ->orWhere('code', 'like', "%{$query}%")
            ->limit($limit)
            ->get(['id', 'name', 'code']);

        foreach ($departments as $department) {
            $suggestions[] = [
                'type' => 'department',
                'id' => $department->id,
                'text' => $department->name,
                'subtitle' => $department->code,
            ];
        }

        return array_slice($suggestions, 0, $limit);
    }
}
```

---

## Step 2: Create Search and Report Controllers

### 2.1 Create Search Controller

```bash
php artisan make:controller Api/SearchController --api
php artisan make:controller SearchController
```

Create `app/Http/Controllers/Api/SearchController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SearchService;
use App\Http\Requests\SearchRequest;
use App\Http\Resources\SearchResultResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SearchController extends Controller
{
    protected SearchService $searchService;

    public function __construct(SearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    /**
     * Global search across all entities
     */
    public function globalSearch(SearchRequest $request): JsonResponse
    {
        $results = $this->searchService->globalSearch(
            $request->validated('query'),
            $request->validated('filters', []),
            $request->validated('limit', 20)
        );

        return response()->json([
            'success' => true,
            'data' => SearchResultResource::collection($results),
            'meta' => [
                'total' => count($results),
                'query' => $request->validated('query'),
                'filters' => $request->validated('filters', [])
            ]
        ]);
    }

    /**
     * Advanced search with filters
     */
    public function advancedSearch(SearchRequest $request): JsonResponse
    {
        $results = $this->searchService->advancedSearch(
            $request->validated('query'),
            $request->validated('entity_type'),
            $request->validated('filters', []),
            $request->validated('sort_by', 'relevance'),
            $request->validated('sort_direction', 'desc'),
            $request->validated('limit', 20)
        );

        return response()->json([
            'success' => true,
            'data' => SearchResultResource::collection($results),
            'meta' => [
                'total' => count($results),
                'entity_type' => $request->validated('entity_type'),
                'query' => $request->validated('query'),
                'filters' => $request->validated('filters', [])
            ]
        ]);
    }

    /**
     * Get search suggestions
     */
    public function suggestions(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
            'limit' => 'integer|min:1|max:20'
        ]);

        $suggestions = $this->searchService->getSuggestions(
            $request->query,
            $request->get('limit', 10)
        );

        return response()->json([
            'success' => true,
            'data' => $suggestions
        ]);
    }

    /**
     * Get search filters for entity type
     */
    public function getFilters(Request $request): JsonResponse
    {
        $request->validate([
            'entity_type' => 'required|string|in:employees,departments,shifts'
        ]);

        $filters = $this->searchService->getAvailableFilters($request->entity_type);

        return response()->json([
            'success' => true,
            'data' => $filters
        ]);
    }
}
```

### 2.2 Create Report Controller

```bash
php artisan make:controller Api/ReportController --api
php artisan make:controller ReportController
```

Create `app/Http/Controllers/Api/ReportController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ReportService;
use App\Http\Requests\ReportRequest;
use App\Http\Resources\ReportResource;
use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class ReportController extends Controller
{
    protected ReportService $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * Get available report types
     */
    public function index(): JsonResponse
    {
        $reportTypes = $this->reportService->getAvailableReports();

        return response()->json([
            'success' => true,
            'data' => $reportTypes
        ]);
    }

    /**
     * Generate report
     */
    public function generate(ReportRequest $request): JsonResponse
    {
        $report = $this->reportService->generateReport(
            $request->validated('type'),
            $request->validated('parameters', []),
            $request->validated('format', 'json')
        );

        return response()->json([
            'success' => true,
            'data' => new ReportResource($report)
        ]);
    }

    /**
     * Export report
     */
    public function export(ReportRequest $request): Response
    {
        $format = $request->validated('format', 'pdf');

        $export = $this->reportService->exportReport(
            $request->validated('type'),
            $request->validated('parameters', []),
            $format
        );

        $filename = $this->reportService->getReportFilename(
            $request->validated('type'),
            $format
        );

        return response($export['content'])
            ->header('Content-Type', $export['mime_type'])
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    /**
     * Get employee report
     */
    public function employeeReport(Request $request): JsonResponse
    {
        $request->validate([
            'department_id' => 'nullable|exists:departments,id',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'status' => 'nullable|in:active,inactive,terminated'
        ]);

        $report = $this->reportService->generateEmployeeReport($request->all());

        return response()->json([
            'success' => true,
            'data' => $report
        ]);
    }

    /**
     * Get attendance report
     */
    public function attendanceReport(Request $request): JsonResponse
    {
        $request->validate([
            'employee_id' => 'nullable|exists:employees,id',
            'department_id' => 'nullable|exists:departments,id',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from'
        ]);

        $report = $this->reportService->generateAttendanceReport($request->all());

        return response()->json([
            'success' => true,
            'data' => $report
        ]);
    }

    /**
     * Get shift report
     */
    public function shiftReport(Request $request): JsonResponse
    {
        $request->validate([
            'department_id' => 'nullable|exists:departments,id',
            'shift_type' => 'nullable|in:morning,afternoon,night,emergency,on_call',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from'
        ]);

        $report = $this->reportService->generateShiftReport($request->all());

        return response()->json([
            'success' => true,
            'data' => $report
        ]);
    }
}
```

---

## Step 3: Create Request Validation and Models

### 3.1 Create Request Validation Classes

```bash
php artisan make:request SearchRequest
php artisan make:request ReportRequest
```

Create `app/Http/Requests/SearchRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'query' => 'required|string|min:2|max:255',
            'entity_type' => 'nullable|string|in:employees,departments,shifts,all',
            'filters' => 'nullable|array',
            'filters.department_id' => 'nullable|exists:departments,id',
            'filters.status' => 'nullable|string',
            'filters.date_from' => 'nullable|date',
            'filters.date_to' => 'nullable|date|after_or_equal:filters.date_from',
            'filters.shift_type' => 'nullable|string|in:morning,afternoon,night,emergency,on_call',
            'filters.employee_type' => 'nullable|string|in:doctor,nurse,admin,technician,support',
            'sort_by' => 'nullable|string|in:relevance,name,created_at,updated_at',
            'sort_direction' => 'nullable|string|in:asc,desc',
            'limit' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1'
        ];
    }

    public function messages(): array
    {
        return [
            'query.required' => 'Query pencarian wajib diisi',
            'query.min' => 'Query pencarian minimal 2 karakter',
            'query.max' => 'Query pencarian maksimal 255 karakter',
            'entity_type.in' => 'Tipe entitas tidak valid',
            'filters.department_id.exists' => 'Departemen tidak ditemukan',
            'filters.date_to.after_or_equal' => 'Tanggal akhir harus setelah atau sama dengan tanggal awal',
            'limit.max' => 'Maksimal 100 hasil per halaman'
        ];
    }
}
```

Create `app/Http/Requests/ReportRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ReportRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('view reports');
    }

    public function rules(): array
    {
        return [
            'type' => 'required|string|in:employee,attendance,shift,department,performance,leave',
            'format' => 'nullable|string|in:json,pdf,excel,csv',
            'parameters' => 'nullable|array',
            'parameters.department_id' => 'nullable|exists:departments,id',
            'parameters.employee_id' => 'nullable|exists:employees,id',
            'parameters.date_from' => 'nullable|date',
            'parameters.date_to' => 'nullable|date|after_or_equal:parameters.date_from',
            'parameters.status' => 'nullable|string',
            'parameters.shift_type' => 'nullable|string|in:morning,afternoon,night,emergency,on_call',
            'parameters.include_inactive' => 'nullable|boolean',
            'parameters.group_by' => 'nullable|string|in:department,position,shift_type,month,week'
        ];
    }

    public function messages(): array
    {
        return [
            'type.required' => 'Tipe laporan wajib dipilih',
            'type.in' => 'Tipe laporan tidak valid',
            'format.in' => 'Format export tidak valid',
            'parameters.department_id.exists' => 'Departemen tidak ditemukan',
            'parameters.employee_id.exists' => 'Karyawan tidak ditemukan',
            'parameters.date_to.after_or_equal' => 'Tanggal akhir harus setelah atau sama dengan tanggal awal'
        ];
    }
}
```

### 3.2 Create Report Models

```bash
php artisan make:model Report -m
php artisan make:model SearchLog -m
```

Create migration `database/migrations/create_reports_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type');
            $table->text('description')->nullable();
            $table->json('parameters');
            $table->json('data');
            $table->string('format')->default('json');
            $table->string('file_path')->nullable();
            $table->integer('file_size')->nullable();
            $table->foreignId('generated_by')->constrained('users');
            $table->timestamp('generated_at');
            $table->timestamp('expires_at')->nullable();
            $table->enum('status', ['generating', 'completed', 'failed', 'expired'])->default('generating');
            $table->text('error_message')->nullable();
            $table->timestamps();

            $table->index(['type', 'status']);
            $table->index(['generated_by', 'generated_at']);
            $table->index('expires_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
```

Create migration `database/migrations/create_search_logs_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('search_logs', function (Blueprint $table) {
            $table->id();
            $table->string('query');
            $table->string('entity_type')->nullable();
            $table->json('filters')->nullable();
            $table->integer('results_count');
            $table->decimal('execution_time', 8, 3);
            $table->foreignId('user_id')->nullable()->constrained();
            $table->string('ip_address', 45);
            $table->string('user_agent')->nullable();
            $table->timestamps();

            $table->index(['query', 'entity_type']);
            $table->index(['user_id', 'created_at']);
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('search_logs');
    }
};
```

### 3.3 Create Report Model

Edit `app/Models/Report.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Report extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'description',
        'parameters',
        'data',
        'format',
        'file_path',
        'file_size',
        'generated_by',
        'generated_at',
        'expires_at',
        'status',
        'error_message'
    ];

    protected $casts = [
        'parameters' => 'array',
        'data' => 'array',
        'generated_at' => 'datetime',
        'expires_at' => 'datetime'
    ];

    // Relationships
    public function generatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    // Scopes
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'completed')
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    // Accessors
    public function getFileSizeFormattedAttribute(): string
    {
        if (!$this->file_size) {
            return 'N/A';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unit = 0;

        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }

        return round($size, 2) . ' ' . $units[$unit];
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    // Methods
    public function markAsCompleted(array $data, ?string $filePath = null): void
    {
        $this->update([
            'status' => 'completed',
            'data' => $data,
            'file_path' => $filePath,
            'file_size' => $filePath ? filesize(storage_path('app/' . $filePath)) : null,
            'generated_at' => now()
        ]);
    }

    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage
        ]);
    }
}
```

### 3.4 Create SearchLog Model

Edit `app/Models/SearchLog.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SearchLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'query',
        'entity_type',
        'filters',
        'results_count',
        'execution_time',
        'user_id',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'filters' => 'array',
        'execution_time' => 'decimal:3'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByEntityType($query, $entityType)
    {
        return $query->where('entity_type', $entityType);
    }

    public function scopePopularQueries($query, $limit = 10)
    {
        return $query->selectRaw('query, COUNT(*) as search_count')
                    ->groupBy('query')
                    ->orderByDesc('search_count')
                    ->limit($limit);
    }

    public function scopeSlowQueries($query, $threshold = 1.0)
    {
        return $query->where('execution_time', '>', $threshold)
                    ->orderByDesc('execution_time');
    }

    // Static methods
    public static function logSearch(
        string $query,
        ?string $entityType,
        ?array $filters,
        int $resultsCount,
        float $executionTime,
        ?int $userId = null
    ): void {
        static::create([
            'query' => $query,
            'entity_type' => $entityType,
            'filters' => $filters,
            'results_count' => $resultsCount,
            'execution_time' => $executionTime,
            'user_id' => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }
}
```

### 3.5 Create Report Service

```bash
php artisan make:service ReportService
```

Create `app/Services/ReportService.php`:

```php
<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\Department;
use App\Models\Shift;
use App\Models\ShiftAssignment;
use App\Models\Report;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;

class ReportService
{
    /**
     * Get available report types
     */
    public function getAvailableReports(): array
    {
        return [
            'employee' => [
                'name' => 'Laporan Karyawan',
                'description' => 'Laporan data karyawan berdasarkan departemen dan status',
                'parameters' => ['department_id', 'status', 'date_from', 'date_to']
            ],
            'attendance' => [
                'name' => 'Laporan Kehadiran',
                'description' => 'Laporan kehadiran karyawan per periode',
                'parameters' => ['employee_id', 'department_id', 'date_from', 'date_to']
            ],
            'shift' => [
                'name' => 'Laporan Shift',
                'description' => 'Laporan jadwal shift dan penugasan',
                'parameters' => ['department_id', 'shift_type', 'date_from', 'date_to']
            ],
            'department' => [
                'name' => 'Laporan Departemen',
                'description' => 'Laporan statistik departemen dan struktur organisasi',
                'parameters' => ['include_inactive']
            ]
        ];
    }

    /**
     * Generate report
     */
    public function generateReport(string $type, array $parameters = [], string $format = 'json'): Report
    {
        $report = Report::create([
            'name' => $this->getReportName($type, $parameters),
            'type' => $type,
            'description' => $this->getReportDescription($type, $parameters),
            'parameters' => $parameters,
            'format' => $format,
            'generated_by' => auth()->id(),
            'status' => 'generating',
            'data' => []
        ]);

        try {
            $data = match ($type) {
                'employee' => $this->generateEmployeeReport($parameters),
                'attendance' => $this->generateAttendanceReport($parameters),
                'shift' => $this->generateShiftReport($parameters),
                'department' => $this->generateDepartmentReport($parameters),
                default => throw new \InvalidArgumentException("Unknown report type: {$type}")
            };

            $report->markAsCompleted($data);

        } catch (\Exception $e) {
            $report->markAsFailed($e->getMessage());
            throw $e;
        }

        return $report;
    }

    /**
     * Generate employee report
     */
    public function generateEmployeeReport(array $parameters = []): array
    {
        $query = Employee::with(['department', 'position']);

        if (isset($parameters['department_id'])) {
            $query->where('department_id', $parameters['department_id']);
        }

        if (isset($parameters['status'])) {
            $query->where('status', $parameters['status']);
        }

        if (isset($parameters['date_from']) && isset($parameters['date_to'])) {
            $query->whereBetween('hire_date', [
                $parameters['date_from'],
                $parameters['date_to']
            ]);
        }

        $employees = $query->get();

        return [
            'summary' => [
                'total_employees' => $employees->count(),
                'by_department' => $employees->groupBy('department.name')->map->count(),
                'by_status' => $employees->groupBy('status')->map->count(),
                'by_employee_type' => $employees->groupBy('employee_type')->map->count()
            ],
            'employees' => $employees->map(function ($employee) {
                return [
                    'id' => $employee->id,
                    'employee_number' => $employee->employee_number,
                    'full_name' => $employee->full_name,
                    'department' => $employee->department?->name,
                    'position' => $employee->position?->name,
                    'employee_type' => $employee->employee_type,
                    'hire_date' => $employee->hire_date?->format('Y-m-d'),
                    'status' => $employee->status,
                    'phone' => $employee->phone,
                    'email' => $employee->email
                ];
            }),
            'generated_at' => now()->toISOString(),
            'parameters' => $parameters
        ];
    }

    /**
     * Generate attendance report
     */
    public function generateAttendanceReport(array $parameters = []): array
    {
        $dateFrom = Carbon::parse($parameters['date_from']);
        $dateTo = Carbon::parse($parameters['date_to']);

        $query = ShiftAssignment::with(['employee', 'shift', 'shift.department'])
            ->whereBetween('date', [$dateFrom, $dateTo]);

        if (isset($parameters['employee_id'])) {
            $query->where('employee_id', $parameters['employee_id']);
        }

        if (isset($parameters['department_id'])) {
            $query->whereHas('shift', function ($q) use ($parameters) {
                $q->where('department_id', $parameters['department_id']);
            });
        }

        $assignments = $query->get();

        return [
            'summary' => [
                'total_assignments' => $assignments->count(),
                'by_status' => $assignments->groupBy('status')->map->count(),
                'by_department' => $assignments->groupBy('shift.department.name')->map->count(),
                'by_shift_type' => $assignments->groupBy('shift.shift_type')->map->count()
            ],
            'attendance' => $assignments->map(function ($assignment) {
                return [
                    'date' => $assignment->date,
                    'employee' => $assignment->employee->full_name,
                    'employee_number' => $assignment->employee->employee_number,
                    'department' => $assignment->shift->department->name,
                    'shift_name' => $assignment->shift->name,
                    'shift_type' => $assignment->shift->shift_type,
                    'start_time' => $assignment->shift->start_time,
                    'end_time' => $assignment->shift->end_time,
                    'status' => $assignment->status
                ];
            }),
            'period' => [
                'from' => $dateFrom->format('Y-m-d'),
                'to' => $dateTo->format('Y-m-d'),
                'days' => $dateFrom->diffInDays($dateTo) + 1
            ],
            'generated_at' => now()->toISOString(),
            'parameters' => $parameters
        ];
    }

    /**
     * Generate shift report
     */
    public function generateShiftReport(array $parameters = []): array
    {
        $query = Shift::with(['department']);

        if (isset($parameters['department_id'])) {
            $query->where('department_id', $parameters['department_id']);
        }

        if (isset($parameters['shift_type'])) {
            $query->where('shift_type', $parameters['shift_type']);
        }

        $shifts = $query->get();

        // Get assignments for the period if dates provided
        $assignments = collect();
        if (isset($parameters['date_from']) && isset($parameters['date_to'])) {
            $assignments = ShiftAssignment::with(['employee'])
                ->whereIn('shift_id', $shifts->pluck('id'))
                ->whereBetween('date', [$parameters['date_from'], $parameters['date_to']])
                ->get();
        }

        return [
            'summary' => [
                'total_shifts' => $shifts->count(),
                'by_department' => $shifts->groupBy('department.name')->map->count(),
                'by_shift_type' => $shifts->groupBy('shift_type')->map->count(),
                'active_shifts' => $shifts->where('is_active', true)->count(),
                'total_assignments' => $assignments->count()
            ],
            'shifts' => $shifts->map(function ($shift) use ($assignments) {
                $shiftAssignments = $assignments->where('shift_id', $shift->id);

                return [
                    'id' => $shift->id,
                    'name' => $shift->name,
                    'shift_type' => $shift->shift_type,
                    'department' => $shift->department->name,
                    'start_time' => $shift->start_time,
                    'end_time' => $shift->end_time,
                    'required_employees' => $shift->required_employees,
                    'minimum_employees' => $shift->minimum_employees,
                    'is_active' => $shift->is_active,
                    'assignments_count' => $shiftAssignments->count(),
                    'unique_employees' => $shiftAssignments->pluck('employee_id')->unique()->count()
                ];
            }),
            'generated_at' => now()->toISOString(),
            'parameters' => $parameters
        ];
    }

    private function getReportName(string $type, array $parameters): string
    {
        $names = [
            'employee' => 'Laporan Karyawan',
            'attendance' => 'Laporan Kehadiran',
            'shift' => 'Laporan Shift',
            'department' => 'Laporan Departemen'
        ];

        $name = $names[$type] ?? 'Laporan';

        if (isset($parameters['date_from']) && isset($parameters['date_to'])) {
            $name .= ' (' . $parameters['date_from'] . ' - ' . $parameters['date_to'] . ')';
        }

        return $name;
    }

    private function getReportDescription(string $type, array $parameters): string
    {
        $descriptions = [
            'employee' => 'Laporan data karyawan',
            'attendance' => 'Laporan kehadiran karyawan',
            'shift' => 'Laporan jadwal shift',
            'department' => 'Laporan departemen'
        ];

        return $descriptions[$type] ?? 'Laporan sistem';
    }

    public function getReportFilename(string $type, string $format): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        return "laporan_{$type}_{$timestamp}.{$format}";
    }
}
```

### 3.6 Create API Resources

```bash
php artisan make:resource SearchResultResource
php artisan make:resource ReportResource
```

Create `app/Http/Resources/SearchResultResource.php`:

```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SearchResultResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'description' => $this->description,
            'url' => $this->url,
            'image' => $this->image,
            'metadata' => $this->metadata,
            'relevance_score' => $this->relevance_score,
            'highlighted_text' => $this->highlighted_text,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString()
        ];
    }
}
```

Create `app/Http/Resources/ReportResource.php`:

```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReportResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'description' => $this->description,
            'parameters' => $this->parameters,
            'format' => $this->format,
            'status' => $this->status,
            'file_size_formatted' => $this->file_size_formatted,
            'is_expired' => $this->is_expired,
            'generated_by' => [
                'id' => $this->generatedBy->id,
                'name' => $this->generatedBy->name
            ],
            'generated_at' => $this->generated_at?->toISOString(),
            'expires_at' => $this->expires_at?->toISOString(),
            'error_message' => $this->when($this->status === 'failed', $this->error_message),
            'data' => $this->when($this->status === 'completed', $this->data),
            'download_url' => $this->when(
                $this->status === 'completed' && $this->file_path,
                route('api.reports.download', $this->id)
            ),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString()
        ];
    }
}
```

### 3.7 Add Routes

Add to `routes/api.php`:

```php
use App\Http\Controllers\Api\SearchController;
use App\Http\Controllers\Api\ReportController;

Route::middleware(['auth:sanctum'])->group(function () {
    // Search Routes
    Route::prefix('search')->group(function () {
        Route::post('/', [SearchController::class, 'globalSearch'])->name('api.search.global');
        Route::post('/advanced', [SearchController::class, 'advancedSearch'])->name('api.search.advanced');
        Route::get('/suggestions', [SearchController::class, 'suggestions'])->name('api.search.suggestions');
        Route::get('/filters/{entity_type}', [SearchController::class, 'getFilters'])->name('api.search.filters');
    });

    // Report Routes
    Route::prefix('reports')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('api.reports.index');
        Route::post('/generate', [ReportController::class, 'generate'])->name('api.reports.generate');
        Route::post('/export', [ReportController::class, 'export'])->name('api.reports.export');
        Route::get('/employee', [ReportController::class, 'employeeReport'])->name('api.reports.employee');
        Route::get('/attendance', [ReportController::class, 'attendanceReport'])->name('api.reports.attendance');
        Route::get('/shift', [ReportController::class, 'shiftReport'])->name('api.reports.shift');
        Route::get('/{report}/download', [ReportController::class, 'download'])->name('api.reports.download');
    });
});
```

Add to `routes/web.php`:

```php
use App\Http\Controllers\SearchController;
use App\Http\Controllers\ReportController;

Route::middleware(['auth', 'verified'])->group(function () {
    // Search Routes
    Route::get('/search', [SearchController::class, 'index'])->name('search.index');
    Route::get('/search/advanced', [SearchController::class, 'advanced'])->name('search.advanced');

    // Report Routes
    Route::resource('reports', ReportController::class)->only(['index', 'show', 'destroy']);
    Route::get('/reports/create/{type}', [ReportController::class, 'create'])->name('reports.create');
    Route::post('/reports/generate', [ReportController::class, 'generate'])->name('reports.generate');
    Route::get('/reports/{report}/download', [ReportController::class, 'download'])->name('reports.download');
});
```

### 3.8 Create Web Controllers for Inertia

Create `app/Http/Controllers/SearchController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Services\SearchService;
use App\Models\Department;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SearchController extends Controller
{
    protected SearchService $searchService;

    public function __construct(SearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    public function index(Request $request): Response
    {
        $departments = Department::where('status', 'active')->get(['id', 'name']);

        $results = [];
        if ($request->filled('q')) {
            $results = $this->searchService->globalSearch(
                $request->q,
                $request->only(['department_id', 'entity_type', 'status']),
                $request->get('limit', 20)
            );
        }

        return Inertia::render('Search/Index', [
            'results' => $results,
            'departments' => $departments,
            'filters' => $request->only(['q', 'department_id', 'entity_type', 'status']),
            'suggestions' => $request->filled('q') ?
                $this->searchService->getSuggestions($request->q, 5) : []
        ]);
    }

    public function advanced(Request $request): Response
    {
        $departments = Department::where('status', 'active')->get(['id', 'name']);

        return Inertia::render('Search/Advanced', [
            'departments' => $departments,
            'filters' => $request->all()
        ]);
    }
}
```

Create `app/Http/Controllers/ReportController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Services\ReportService;
use App\Models\Report;
use App\Models\Department;
use App\Http\Requests\ReportRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ReportController extends Controller
{
    protected ReportService $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    public function index(Request $request): Response
    {
        $query = Report::with('generatedBy')
            ->where('generated_by', auth()->id())
            ->orderByDesc('created_at');

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $reports = $query->paginate(15)->withQueryString();
        $reportTypes = $this->reportService->getAvailableReports();

        return Inertia::render('Reports/Index', [
            'reports' => $reports,
            'reportTypes' => $reportTypes,
            'filters' => $request->only(['type', 'status'])
        ]);
    }

    public function create(Request $request, string $type): Response
    {
        $reportTypes = $this->reportService->getAvailableReports();

        if (!isset($reportTypes[$type])) {
            abort(404, 'Report type not found');
        }

        $departments = Department::where('status', 'active')->get(['id', 'name']);

        return Inertia::render('Reports/Create', [
            'reportType' => $type,
            'reportConfig' => $reportTypes[$type],
            'departments' => $departments
        ]);
    }

    public function generate(ReportRequest $request)
    {
        try {
            $report = $this->reportService->generateReport(
                $request->validated('type'),
                $request->validated('parameters', []),
                $request->validated('format', 'json')
            );

            return redirect()->route('reports.show', $report)
                ->with('success', 'Laporan berhasil dibuat');

        } catch (\Exception $e) {
            return back()->withErrors([
                'message' => 'Gagal membuat laporan: ' . $e->getMessage()
            ]);
        }
    }

    public function show(Report $report): Response
    {
        $this->authorize('view', $report);

        return Inertia::render('Reports/Show', [
            'report' => $report->load('generatedBy')
        ]);
    }

    public function download(Report $report): BinaryFileResponse
    {
        $this->authorize('view', $report);

        if ($report->status !== 'completed' || !$report->file_path) {
            abort(404, 'File not found');
        }

        $filePath = storage_path('app/' . $report->file_path);

        if (!file_exists($filePath)) {
            abort(404, 'File not found');
        }

        return response()->download($filePath, $this->reportService->getReportFilename(
            $report->type,
            $report->format
        ));
    }

    public function destroy(Report $report)
    {
        $this->authorize('delete', $report);

        if ($report->file_path && file_exists(storage_path('app/' . $report->file_path))) {
            unlink(storage_path('app/' . $report->file_path));
        }

        $report->delete();

        return redirect()->route('reports.index')
            ->with('success', 'Laporan berhasil dihapus');
    }
}
```

---

## Step 4: Create React Search Components

### 4.1 Create Search Interface Component

Create `resources/js/Pages/Search/Index.tsx`:

```tsx
import React, { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import {
  Search,
  Filter,
  Users,
  Building2,
  Clock,
  FileText,
  ChevronRight,
  Loader2
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { useDebounce } from '@/hooks/useDebounce';

interface SearchResult {
  id: number;
  type: 'employee' | 'department' | 'shift';
  title: string;
  subtitle: string;
  description?: string;
  url: string;
  image?: string;
  metadata?: Record<string, any>;
  relevance_score: number;
  highlighted_text?: string;
}

interface Props {
  results: SearchResult[];
  departments: Array<{
    id: number;
    name: string;
  }>;
  filters: {
    q?: string;
    department_id?: string;
    entity_type?: string;
    status?: string;
  };
  suggestions: Array<{
    type: string;
    id: number;
    text: string;
    subtitle: string;
  }>;
}

export default function SearchIndex({ results, departments, filters, suggestions }: Props) {
  const [searchQuery, setSearchQuery] = useState(filters.q || '');
  const [entityType, setEntityType] = useState(filters.entity_type || 'all');
  const [departmentId, setDepartmentId] = useState(filters.department_id || '');
  const [status, setStatus] = useState(filters.status || '');
  const [isSearching, setIsSearching] = useState(false);

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  useEffect(() => {
    if (debouncedSearchQuery.length >= 2) {
      performSearch();
    }
  }, [debouncedSearchQuery, entityType, departmentId, status]);

  const performSearch = () => {
    setIsSearching(true);

    router.get(route('search.index'), {
      q: debouncedSearchQuery,
      entity_type: entityType,
      department_id: departmentId,
      status: status
    }, {
      preserveState: true,
      preserveScroll: true,
      onFinish: () => setIsSearching(false)
    });
  };

  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'employee':
        return <Users className="h-4 w-4" />;
      case 'department':
        return <Building2 className="h-4 w-4" />;
      case 'shift':
        return <Clock className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getEntityBadgeColor = (type: string) => {
    switch (type) {
      case 'employee':
        return 'bg-blue-100 text-blue-800';
      case 'department':
        return 'bg-green-100 text-green-800';
      case 'shift':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            Pencarian Global
          </h2>
          <Link href={route('search.advanced')}>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Pencarian Lanjutan
            </Button>
          </Link>
        </div>
      }
    >
      <Head title="Pencarian" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {/* Search Form */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Pencarian
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="Cari karyawan, departemen, shift..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                    {isSearching && (
                      <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
                    )}
                  </div>
                </div>

                <Select value={entityType} onValueChange={setEntityType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Semua Tipe" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Tipe</SelectItem>
                    <SelectItem value="employee">Karyawan</SelectItem>
                    <SelectItem value="department">Departemen</SelectItem>
                    <SelectItem value="shift">Shift</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={departmentId} onValueChange={setDepartmentId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Semua Departemen" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Departemen</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id.toString()}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Search Results */}
          {searchQuery.length >= 2 && (
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Results */}
              <div className="lg:col-span-3">
                <Card>
                  <CardHeader>
                    <CardTitle>
                      Hasil Pencarian
                      {results.length > 0 && (
                        <Badge variant="secondary" className="ml-2">
                          {results.length} hasil
                        </Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {results.length === 0 ? (
                      <div className="text-center py-8">
                        <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">
                          Tidak ada hasil ditemukan untuk "{searchQuery}"
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {results.map((result) => (
                          <div
                            key={`${result.type}-${result.id}`}
                            className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex items-start gap-3 flex-1">
                                <div className="flex-shrink-0">
                                  {result.image ? (
                                    <Avatar className="h-10 w-10">
                                      <AvatarImage src={result.image} />
                                      <AvatarFallback>
                                        {getEntityIcon(result.type)}
                                      </AvatarFallback>
                                    </Avatar>
                                  ) : (
                                    <div className="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                                      {getEntityIcon(result.type)}
                                    </div>
                                  )}
                                </div>

                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <h3 className="font-medium text-gray-900 truncate">
                                      {result.highlighted_text || result.title}
                                    </h3>
                                    <Badge className={getEntityBadgeColor(result.type)}>
                                      {result.type === 'employee' ? 'Karyawan' :
                                       result.type === 'department' ? 'Departemen' : 'Shift'}
                                    </Badge>
                                  </div>

                                  <p className="text-sm text-gray-600 mb-1">
                                    {result.subtitle}
                                  </p>

                                  {result.description && (
                                    <p className="text-sm text-gray-500 line-clamp-2">
                                      {result.description}
                                    </p>
                                  )}
                                </div>
                              </div>

                              <Link href={result.url}>
                                <Button variant="ghost" size="sm">
                                  <ChevronRight className="h-4 w-4" />
                                </Button>
                              </Link>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Suggestions */}
              {suggestions.length > 0 && (
                <div className="lg:col-span-1">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Saran Pencarian</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {suggestions.map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => setSearchQuery(suggestion.text)}
                            className="w-full text-left p-2 rounded hover:bg-gray-50 transition-colors"
                          >
                            <div className="flex items-center gap-2">
                              {getEntityIcon(suggestion.type)}
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">
                                  {suggestion.text}
                                </p>
                                <p className="text-xs text-gray-500 truncate">
                                  {suggestion.subtitle}
                                </p>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

---

## Step 5: Create Advanced Search and Report Dashboard

### 5.1 Create Advanced Search Component

Create `resources/js/Pages/Search/Advanced.tsx`:

```tsx
import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Checkbox } from '@/Components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import { DatePicker } from '@/Components/ui/date-picker';
import {
  Search,
  Filter,
  RotateCcw,
  Users,
  Building2,
  Clock
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';

interface Props {
  departments: Array<{
    id: number;
    name: string;
  }>;
  filters: Record<string, any>;
}

export default function AdvancedSearch({ departments, filters }: Props) {
  const [searchForm, setSearchForm] = useState({
    query: filters.query || '',
    entity_types: filters.entity_types || [],
    department_id: filters.department_id || '',
    employee_type: filters.employee_type || '',
    status: filters.status || '',
    shift_type: filters.shift_type || '',
    date_from: filters.date_from || '',
    date_to: filters.date_to || '',
    sort_by: filters.sort_by || 'relevance',
    sort_direction: filters.sort_direction || 'desc'
  });

  const handleEntityTypeChange = (entityType: string, checked: boolean) => {
    setSearchForm(prev => ({
      ...prev,
      entity_types: checked
        ? [...prev.entity_types, entityType]
        : prev.entity_types.filter((type: string) => type !== entityType)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    router.get(route('search.index'), {
      ...searchForm,
      entity_type: searchForm.entity_types.join(',')
    });
  };

  const handleReset = () => {
    setSearchForm({
      query: '',
      entity_types: [],
      department_id: '',
      employee_type: '',
      status: '',
      shift_type: '',
      date_from: '',
      date_to: '',
      sort_by: 'relevance',
      sort_direction: 'desc'
    });
  };

  return (
    <AuthenticatedLayout
      header={
        <h2 className="font-semibold text-xl text-gray-800 leading-tight">
          Pencarian Lanjutan
        </h2>
      }
    >
      <Head title="Pencarian Lanjutan" />

      <div className="py-12">
        <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Pencarian Lanjutan
              </CardTitle>
              <CardDescription>
                Gunakan filter lanjutan untuk pencarian yang lebih spesifik
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Search Query */}
                <div className="space-y-2">
                  <Label htmlFor="query">Kata Kunci</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="query"
                      type="text"
                      placeholder="Masukkan kata kunci pencarian..."
                      value={searchForm.query}
                      onChange={(e) => setSearchForm(prev => ({ ...prev, query: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Entity Types */}
                <div className="space-y-3">
                  <Label>Tipe Data</Label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="employee"
                        checked={searchForm.entity_types.includes('employee')}
                        onCheckedChange={(checked) => handleEntityTypeChange('employee', checked as boolean)}
                      />
                      <Label htmlFor="employee" className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Karyawan
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="department"
                        checked={searchForm.entity_types.includes('department')}
                        onCheckedChange={(checked) => handleEntityTypeChange('department', checked as boolean)}
                      />
                      <Label htmlFor="department" className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        Departemen
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="shift"
                        checked={searchForm.entity_types.includes('shift')}
                        onCheckedChange={(checked) => handleEntityTypeChange('shift', checked as boolean)}
                      />
                      <Label htmlFor="shift" className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Shift
                      </Label>
                    </div>
                  </div>
                </div>

                {/* Filters Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Department */}
                  <div className="space-y-2">
                    <Label htmlFor="department">Departemen</Label>
                    <Select
                      value={searchForm.department_id}
                      onValueChange={(value) => setSearchForm(prev => ({ ...prev, department_id: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih departemen" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Semua Departemen</SelectItem>
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id.toString()}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Employee Type */}
                  <div className="space-y-2">
                    <Label htmlFor="employee_type">Tipe Karyawan</Label>
                    <Select
                      value={searchForm.employee_type}
                      onValueChange={(value) => setSearchForm(prev => ({ ...prev, employee_type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih tipe karyawan" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Semua Tipe</SelectItem>
                        <SelectItem value="doctor">Dokter</SelectItem>
                        <SelectItem value="nurse">Perawat</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="technician">Teknisi</SelectItem>
                        <SelectItem value="support">Support</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Status */}
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={searchForm.status}
                      onValueChange={(value) => setSearchForm(prev => ({ ...prev, status: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Semua Status</SelectItem>
                        <SelectItem value="active">Aktif</SelectItem>
                        <SelectItem value="inactive">Tidak Aktif</SelectItem>
                        <SelectItem value="terminated">Diberhentikan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Shift Type */}
                  <div className="space-y-2">
                    <Label htmlFor="shift_type">Tipe Shift</Label>
                    <Select
                      value={searchForm.shift_type}
                      onValueChange={(value) => setSearchForm(prev => ({ ...prev, shift_type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih tipe shift" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Semua Shift</SelectItem>
                        <SelectItem value="morning">Pagi</SelectItem>
                        <SelectItem value="afternoon">Siang</SelectItem>
                        <SelectItem value="night">Malam</SelectItem>
                        <SelectItem value="emergency">Darurat</SelectItem>
                        <SelectItem value="on_call">On Call</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Date Range */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date_from">Tanggal Mulai</Label>
                    <DatePicker
                      value={searchForm.date_from}
                      onChange={(date) => setSearchForm(prev => ({ ...prev, date_from: date }))}
                      placeholder="Pilih tanggal mulai"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date_to">Tanggal Akhir</Label>
                    <DatePicker
                      value={searchForm.date_to}
                      onChange={(date) => setSearchForm(prev => ({ ...prev, date_to: date }))}
                      placeholder="Pilih tanggal akhir"
                    />
                  </div>
                </div>

                {/* Sorting */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sort_by">Urutkan Berdasarkan</Label>
                    <Select
                      value={searchForm.sort_by}
                      onValueChange={(value) => setSearchForm(prev => ({ ...prev, sort_by: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="relevance">Relevansi</SelectItem>
                        <SelectItem value="name">Nama</SelectItem>
                        <SelectItem value="created_at">Tanggal Dibuat</SelectItem>
                        <SelectItem value="updated_at">Tanggal Diperbarui</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sort_direction">Arah Urutan</Label>
                    <Select
                      value={searchForm.sort_direction}
                      onValueChange={(value) => setSearchForm(prev => ({ ...prev, sort_direction: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asc">Naik (A-Z, 1-9)</SelectItem>
                        <SelectItem value="desc">Turun (Z-A, 9-1)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex justify-between pt-4">
                  <Button type="button" variant="outline" onClick={handleReset}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                  <Button type="submit">
                    <Search className="h-4 w-4 mr-2" />
                    Cari
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

### 5.2 Create Report Dashboard Component

Create `resources/js/Pages/Reports/Index.tsx`:

```tsx
import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';
import {
  FileText,
  Plus,
  Download,
  Eye,
  Trash2,
  MoreHorizontal,
  Filter,
  Users,
  Building2,
  Clock,
  Calendar,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { formatDistanceToNow } from 'date-fns';
import { id } from 'date-fns/locale';

interface Report {
  id: number;
  name: string;
  type: string;
  description: string;
  status: 'generating' | 'completed' | 'failed' | 'expired';
  file_size_formatted: string;
  is_expired: boolean;
  generated_by: {
    id: number;
    name: string;
  };
  generated_at: string;
  expires_at?: string;
  error_message?: string;
  created_at: string;
}

interface Props {
  reports: {
    data: Report[];
    links: any[];
    meta: any;
  };
  reportTypes: Record<string, {
    name: string;
    description: string;
    parameters: string[];
  }>;
  filters: {
    type?: string;
    status?: string;
  };
}

export default function ReportsIndex({ reports, reportTypes, filters }: Props) {
  const [selectedType, setSelectedType] = useState(filters.type || '');
  const [selectedStatus, setSelectedStatus] = useState(filters.status || '');

  const handleFilterChange = () => {
    router.get(route('reports.index'), {
      type: selectedType,
      status: selectedStatus
    }, {
      preserveState: true,
      preserveScroll: true
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'generating':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      generating: 'bg-blue-100 text-blue-800',
      expired: 'bg-gray-100 text-gray-800'
    };

    const labels = {
      completed: 'Selesai',
      failed: 'Gagal',
      generating: 'Sedang Dibuat',
      expired: 'Kedaluwarsa'
    };

    return (
      <Badge className={variants[status as keyof typeof variants]}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'employee':
        return <Users className="h-4 w-4" />;
      case 'department':
        return <Building2 className="h-4 w-4" />;
      case 'shift':
        return <Clock className="h-4 w-4" />;
      case 'attendance':
        return <Calendar className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const handleDelete = (reportId: number) => {
    if (confirm('Apakah Anda yakin ingin menghapus laporan ini?')) {
      router.delete(route('reports.destroy', reportId));
    }
  };

  return (
    <AuthenticatedLayout
      header={
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-xl text-gray-800 leading-tight">
            Laporan
          </h2>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Buat Laporan
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {Object.entries(reportTypes).map(([type, config]) => (
                <DropdownMenuItem key={type} asChild>
                  <Link href={route('reports.create', type)}>
                    <div className="flex items-center gap-2">
                      {getTypeIcon(type)}
                      <div>
                        <div className="font-medium">{config.name}</div>
                        <div className="text-xs text-gray-500">{config.description}</div>
                      </div>
                    </div>
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      }
    >
      <Head title="Laporan" />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {/* Filters */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filter Laporan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Semua Tipe" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Tipe</SelectItem>
                    {Object.entries(reportTypes).map(([type, config]) => (
                      <SelectItem key={type} value={type}>
                        {config.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Semua Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Status</SelectItem>
                    <SelectItem value="completed">Selesai</SelectItem>
                    <SelectItem value="generating">Sedang Dibuat</SelectItem>
                    <SelectItem value="failed">Gagal</SelectItem>
                    <SelectItem value="expired">Kedaluwarsa</SelectItem>
                  </SelectContent>
                </Select>

                <Button onClick={handleFilterChange} variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Terapkan Filter
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Reports List */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Laporan</CardTitle>
              <CardDescription>
                Kelola dan unduh laporan yang telah dibuat
              </CardDescription>
            </CardHeader>
            <CardContent>
              {reports.data.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada laporan yang dibuat</p>
                  <p className="text-sm text-gray-400 mb-4">
                    Buat laporan pertama Anda dengan mengklik tombol "Buat Laporan"
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reports.data.map((report) => (
                    <div
                      key={report.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          <div className="flex-shrink-0">
                            <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center">
                              {getTypeIcon(report.type)}
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium text-gray-900 truncate">
                                {report.name}
                              </h3>
                              {getStatusBadge(report.status)}
                              {report.is_expired && (
                                <Badge variant="outline" className="text-orange-600 border-orange-200">
                                  Kedaluwarsa
                                </Badge>
                              )}
                            </div>

                            <p className="text-sm text-gray-600 mb-2">
                              {report.description}
                            </p>

                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                {getStatusIcon(report.status)}
                                {report.status === 'completed' && report.file_size_formatted}
                              </span>
                              <span>
                                Dibuat {formatDistanceToNow(new Date(report.created_at), {
                                  addSuffix: true,
                                  locale: id
                                })}
                              </span>
                              <span>
                                oleh {report.generated_by.name}
                              </span>
                            </div>

                            {report.error_message && (
                              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                                {report.error_message}
                              </div>
                            )}
                          </div>
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={route('reports.show', report.id)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Lihat Detail
                              </Link>
                            </DropdownMenuItem>

                            {report.status === 'completed' && !report.is_expired && (
                              <DropdownMenuItem asChild>
                                <Link href={route('reports.download', report.id)}>
                                  <Download className="h-4 w-4 mr-2" />
                                  Unduh
                                </Link>
                              </DropdownMenuItem>
                            )}

                            <DropdownMenuItem
                              onClick={() => handleDelete(report.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Hapus
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Pagination */}
              {reports.meta.last_page > 1 && (
                <div className="mt-6 flex justify-center">
                  <div className="flex items-center gap-2">
                    {reports.links.map((link, index) => (
                      <Button
                        key={index}
                        variant={link.active ? "default" : "outline"}
                        size="sm"
                        disabled={!link.url}
                        onClick={() => link.url && router.get(link.url)}
                        dangerouslySetInnerHTML={{ __html: link.label }}
                      />
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

---

## Step 6: Create Data Visualization and Testing

### 6.1 Create Report Creation Component

Create `resources/js/Pages/Reports/Create.tsx`:

```tsx
import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Checkbox } from '@/Components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import { DatePicker } from '@/Components/ui/date-picker';
import {
  FileText,
  Download,
  Calendar,
  Filter
} from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';

interface Props {
  reportType: string;
  reportConfig: {
    name: string;
    description: string;
    parameters: string[];
  };
  departments: Array<{
    id: number;
    name: string;
  }>;
}

export default function CreateReport({ reportType, reportConfig, departments }: Props) {
  const [form, setForm] = useState({
    type: reportType,
    format: 'pdf',
    parameters: {
      department_id: '',
      employee_id: '',
      date_from: '',
      date_to: '',
      status: '',
      shift_type: '',
      include_inactive: false,
      group_by: ''
    }
  });

  const [isGenerating, setIsGenerating] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsGenerating(true);

    router.post(route('reports.generate'), form, {
      onFinish: () => setIsGenerating(false)
    });
  };

  const hasParameter = (param: string) => {
    return reportConfig.parameters.includes(param);
  };

  return (
    <AuthenticatedLayout
      header={
        <h2 className="font-semibold text-xl text-gray-800 leading-tight">
          Buat {reportConfig.name}
        </h2>
      }
    >
      <Head title={`Buat ${reportConfig.name}`} />

      <div className="py-12">
        <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {reportConfig.name}
              </CardTitle>
              <CardDescription>
                {reportConfig.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Format Selection */}
                <div className="space-y-2">
                  <Label htmlFor="format">Format Laporan</Label>
                  <Select
                    value={form.format}
                    onValueChange={(value) => setForm(prev => ({ ...prev, format: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Department Filter */}
                {hasParameter('department_id') && (
                  <div className="space-y-2">
                    <Label htmlFor="department">Departemen</Label>
                    <Select
                      value={form.parameters.department_id}
                      onValueChange={(value) => setForm(prev => ({
                        ...prev,
                        parameters: { ...prev.parameters, department_id: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih departemen (opsional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Semua Departemen</SelectItem>
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id.toString()}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Date Range */}
                {(hasParameter('date_from') || hasParameter('date_to')) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="date_from">Tanggal Mulai</Label>
                      <DatePicker
                        value={form.parameters.date_from}
                        onChange={(date) => setForm(prev => ({
                          ...prev,
                          parameters: { ...prev.parameters, date_from: date }
                        }))}
                        placeholder="Pilih tanggal mulai"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="date_to">Tanggal Akhir</Label>
                      <DatePicker
                        value={form.parameters.date_to}
                        onChange={(date) => setForm(prev => ({
                          ...prev,
                          parameters: { ...prev.parameters, date_to: date }
                        }))}
                        placeholder="Pilih tanggal akhir"
                      />
                    </div>
                  </div>
                )}

                {/* Status Filter */}
                {hasParameter('status') && (
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={form.parameters.status}
                      onValueChange={(value) => setForm(prev => ({
                        ...prev,
                        parameters: { ...prev.parameters, status: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih status (opsional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Semua Status</SelectItem>
                        <SelectItem value="active">Aktif</SelectItem>
                        <SelectItem value="inactive">Tidak Aktif</SelectItem>
                        <SelectItem value="terminated">Diberhentikan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Shift Type Filter */}
                {hasParameter('shift_type') && (
                  <div className="space-y-2">
                    <Label htmlFor="shift_type">Tipe Shift</Label>
                    <Select
                      value={form.parameters.shift_type}
                      onValueChange={(value) => setForm(prev => ({
                        ...prev,
                        parameters: { ...prev.parameters, shift_type: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih tipe shift (opsional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Semua Shift</SelectItem>
                        <SelectItem value="morning">Pagi</SelectItem>
                        <SelectItem value="afternoon">Siang</SelectItem>
                        <SelectItem value="night">Malam</SelectItem>
                        <SelectItem value="emergency">Darurat</SelectItem>
                        <SelectItem value="on_call">On Call</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Group By */}
                {hasParameter('group_by') && (
                  <div className="space-y-2">
                    <Label htmlFor="group_by">Kelompokkan Berdasarkan</Label>
                    <Select
                      value={form.parameters.group_by}
                      onValueChange={(value) => setForm(prev => ({
                        ...prev,
                        parameters: { ...prev.parameters, group_by: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih pengelompokan (opsional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Tanpa Pengelompokan</SelectItem>
                        <SelectItem value="department">Departemen</SelectItem>
                        <SelectItem value="position">Posisi</SelectItem>
                        <SelectItem value="shift_type">Tipe Shift</SelectItem>
                        <SelectItem value="month">Bulan</SelectItem>
                        <SelectItem value="week">Minggu</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Include Inactive */}
                {hasParameter('include_inactive') && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include_inactive"
                      checked={form.parameters.include_inactive}
                      onCheckedChange={(checked) => setForm(prev => ({
                        ...prev,
                        parameters: { ...prev.parameters, include_inactive: checked as boolean }
                      }))}
                    />
                    <Label htmlFor="include_inactive">
                      Sertakan data yang tidak aktif
                    </Label>
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-end gap-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.get(route('reports.index'))}
                  >
                    Batal
                  </Button>
                  <Button type="submit" disabled={isGenerating}>
                    {isGenerating ? (
                      <>
                        <Calendar className="h-4 w-4 mr-2 animate-spin" />
                        Membuat Laporan...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Buat Laporan
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
```

### 6.2 Create useDebounce Hook

Create `resources/js/hooks/useDebounce.ts`:

```typescript
import { useState, useEffect } from 'react';

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
```

### 6.3 Create Feature Tests

Create `tests/Feature/SearchTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Shift;
use App\Services\SearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected SearchService $searchService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->searchService = app(SearchService::class);
    }

    public function test_global_search_returns_results()
    {
        // Create test data
        $department = Department::factory()->create(['name' => 'Cardiology']);
        $employee = Employee::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'department_id' => $department->id
        ]);

        $results = $this->searchService->globalSearch('John');

        $this->assertNotEmpty($results);
        $this->assertEquals('employee', $results[0]->type);
        $this->assertStringContainsString('John', $results[0]->title);
    }

    public function test_search_api_endpoint()
    {
        $department = Department::factory()->create();
        Employee::factory()->create([
            'first_name' => 'Jane',
            'department_id' => $department->id
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/search', [
                'query' => 'Jane',
                'limit' => 10
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'type',
                            'title',
                            'subtitle',
                            'url'
                        ]
                    ],
                    'meta'
                ]);
    }

    public function test_advanced_search_with_filters()
    {
        $department = Department::factory()->create(['name' => 'Emergency']);
        $employee = Employee::factory()->create([
            'first_name' => 'Alice',
            'department_id' => $department->id,
            'employee_type' => 'doctor'
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/search/advanced', [
                'query' => 'Alice',
                'entity_type' => 'employee',
                'filters' => [
                    'department_id' => $department->id,
                    'employee_type' => 'doctor'
                ]
            ]);

        $response->assertStatus(200);
        $this->assertNotEmpty($response->json('data'));
    }

    public function test_search_suggestions()
    {
        Employee::factory()->create(['first_name' => 'Alexander']);
        Employee::factory()->create(['first_name' => 'Alexandra']);

        $response = $this->actingAs($this->user)
            ->getJson('/api/search/suggestions?query=Alex');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'type',
                            'id',
                            'text',
                            'subtitle'
                        ]
                    ]
                ]);
    }
}
```

### 6.4 Create Report Tests

Create `tests/Feature/ReportTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Report;
use App\Services\ReportService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReportTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ReportService $reportService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->reportService = app(ReportService::class);
    }

    public function test_can_generate_employee_report()
    {
        $department = Department::factory()->create();
        Employee::factory()->count(5)->create(['department_id' => $department->id]);

        $report = $this->reportService->generateReport('employee', [
            'department_id' => $department->id
        ]);

        $this->assertInstanceOf(Report::class, $report);
        $this->assertEquals('completed', $report->status);
        $this->assertArrayHasKey('summary', $report->data);
        $this->assertArrayHasKey('employees', $report->data);
    }

    public function test_report_api_endpoints()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/reports');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }

    public function test_can_create_report_via_api()
    {
        $department = Department::factory()->create();

        $response = $this->actingAs($this->user)
            ->postJson('/api/reports/generate', [
                'type' => 'employee',
                'parameters' => [
                    'department_id' => $department->id
                ],
                'format' => 'json'
            ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'type',
                        'status'
                    ]
                ]);
    }

    public function test_report_validation()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/reports/generate', [
                'type' => 'invalid_type'
            ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['type']);
    }

    public function test_can_access_report_pages()
    {
        $response = $this->actingAs($this->user)
            ->get('/reports');

        $response->assertStatus(200);
    }

    public function test_can_access_report_creation_page()
    {
        $response = $this->actingAs($this->user)
            ->get('/reports/create/employee');

        $response->assertStatus(200);
    }
}
```

### 6.5 Run Migrations and Tests

```bash
# Run migrations
php artisan migrate

# Run tests
php artisan test --filter=SearchTest
php artisan test --filter=ReportTest

# Install additional packages for PDF and Excel export
composer require barryvdh/laravel-dompdf
composer require maatwebsite/excel

# Publish config files
php artisan vendor:publish --provider="Barryvdh\DomPDF\ServiceProvider"
php artisan vendor:publish --provider="Maatwebsite\Excel\ExcelServiceProvider"
```

---

## Summary

Dalam chapter ini, kita telah berhasil mengimplementasikan sistem pencarian dan pelaporan yang komprehensif untuk Hospital Employee Management System dengan fitur-fitur berikut:

### ✅ **Backend Implementation**

1. **Search Service** - Pencarian global dan lanjutan dengan filtering
2. **Report Service** - Generasi laporan dengan berbagai format
3. **Controllers** - API dan Web controllers untuk Inertia.js
4. **Request Validation** - Validasi input dengan pesan bahasa Indonesia
5. **Models** - Report dan SearchLog dengan relationships
6. **API Resources** - Transformasi data untuk API responses
7. **Routes** - Web dan API routes yang lengkap

### ✅ **Frontend Implementation**

1. **Search Interface** - Pencarian real-time dengan suggestions
2. **Advanced Search** - Filter lanjutan dengan multiple criteria
3. **Report Dashboard** - Manajemen laporan dengan status tracking
4. **Report Creation** - Form dinamis berdasarkan tipe laporan
5. **Data Visualization** - Komponen untuk menampilkan hasil pencarian
6. **Responsive Design** - UI yang responsif dengan shadcn/ui

### ✅ **Key Features**

- **Global Search** - Pencarian di semua entitas (employees, departments, shifts)
- **Advanced Filtering** - Filter berdasarkan departemen, tipe, status, tanggal
- **Real-time Search** - Debounced search dengan suggestions
- **Report Generation** - Laporan employee, attendance, shift, department
- **Multiple Formats** - Export ke PDF, Excel, CSV, JSON
- **Indonesian Localization** - UI dan validasi dalam bahasa Indonesia
- **Permission-based Access** - Authorization untuk akses laporan
- **Search Analytics** - Logging pencarian untuk analisis

### ✅ **Testing**

- **Feature Tests** - Test untuk search dan report functionality
- **API Testing** - Test untuk semua API endpoints
- **Validation Testing** - Test untuk request validation
- **Integration Testing** - Test untuk Inertia.js pages

### 🎯 **Indonesian Healthcare Context**

- **Terminologi Medis** - Penggunaan istilah yang sesuai dengan konteks rumah sakit
- **Shift Patterns** - Pola shift yang umum di rumah sakit Indonesia
- **Compliance** - Struktur laporan yang sesuai dengan standar Indonesia
- **User Experience** - Interface yang familiar untuk pengguna Indonesia

### 📊 **Performance Optimizations**

- **Debounced Search** - Mengurangi beban server dengan delay input
- **Indexed Queries** - Database indexing untuk performa pencarian
- **Pagination** - Pagination untuk hasil pencarian dan laporan
- **Caching** - Caching untuk suggestions dan filter options

Chapter ini memberikan foundation yang kuat untuk sistem pencarian dan pelaporan yang dapat dikembangkan lebih lanjut dengan fitur-fitur seperti saved searches, scheduled reports, dan advanced analytics.

**Next Steps**: Lanjutkan ke Chapter 9 untuk implementasi Dashboard dan UI Components, atau Chapter 11 untuk Performance Tracking system.
```
