# Chapter 2: Database Setup and Configuration

Welcome to Chapter 2! In this chapter, we'll set up our database, understand Laravel migrations, and create the foundational database structure for our Car Wash Management System.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Understand <PERSON><PERSON>'s database configuration
- Set up MySQL database for the project
- Master Laravel migrations and schema building
- Create the core database tables for our application
- Learn about database relationships in Laravel
- Set up database seeding for test data

## 📋 What We'll Cover

1. Database installation and configuration
2. Understanding Laravel migrations
3. Creating our core database tables
4. Setting up model relationships
5. Database seeding and factories
6. Testing our database setup

## 🛠 Step 1: Database Installation

### Installing MySQL

#### Windows
1. **Download MySQL Community Server:**
   - Visit [https://dev.mysql.com/downloads/mysql/](https://dev.mysql.com/downloads/mysql/)
   - Download MySQL Community Server
   - Run the installer and follow the setup wizard
   - Remember your root password!

#### macOS
```bash
# Using Homebrew
brew install mysql

# Start MySQL service
brew services start mysql

# Secure installation (optional but recommended)
mysql_secure_installation
```

#### Linux (Ubuntu/Debian)
```bash
# Update package list
sudo apt update

# Install MySQL
sudo apt install mysql-server

# Secure installation
sudo mysql_secure_installation

# Start MySQL service
sudo systemctl start mysql
sudo systemctl enable mysql
```

### Alternative: Using SQLite (Simpler for Development)

If you prefer a simpler setup for development, you can use SQLite:

```bash
# Install SQLite (usually pre-installed on most systems)
# Windows: Download from https://sqlite.org/download.html
# macOS: Already installed
# Linux: sudo apt install sqlite3
```

## 🛠 Step 2: Database Configuration

### Creating the Database

#### For MySQL:
```bash
# Connect to MySQL
mysql -u root -p

# Create database
CREATE DATABASE car_wash_management;

# Create a dedicated user (optional but recommended)
CREATE USER 'carwash_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON car_wash_management.* TO 'carwash_user'@'localhost';
FLUSH PRIVILEGES;

# Exit MySQL
EXIT;
```

#### For SQLite:
No manual database creation needed - Laravel will create the file automatically.

### Configuring Laravel Database Connection

Edit your `.env` file:

#### For MySQL:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=car_wash_management
DB_USERNAME=carwash_user
DB_PASSWORD=secure_password
```

#### For SQLite:
```env
DB_CONNECTION=sqlite
DB_DATABASE=/absolute/path/to/database/database.sqlite
# Or for relative path:
# DB_DATABASE=database/database.sqlite
```

If using SQLite, create the database file:
```bash
# Create database directory if it doesn't exist
mkdir -p database

# Create empty SQLite database file
touch database/database.sqlite
```

### Test Database Connection

```bash
# Test the database connection
php artisan migrate:status
```

If successful, you should see a message about migration table creation.

## 🛠 Step 3: Understanding Laravel Migrations

Migrations are like version control for your database. They allow you to:
- Define database schema in PHP code
- Share database structure with your team
- Roll back database changes
- Keep database structure in sync across environments

### Basic Migration Commands

```bash
# Create a new migration
php artisan make:migration create_table_name

# Run all pending migrations
php artisan migrate

# Rollback the last batch of migrations
php artisan migrate:rollback

# Check migration status
php artisan migrate:status

# Reset all migrations (careful!)
php artisan migrate:reset

# Refresh migrations (reset + migrate)
php artisan migrate:refresh
```

## 🛠 Step 4: Creating Our Core Database Tables

Let's create the essential tables for our car wash management system.

### 1. Users Table (Already exists)

Laravel comes with a users migration. Let's examine it:

```bash
# Look at the existing users migration
cat database/migrations/*_create_users_table.php
```

### 2. Customers Table

```bash
# Create customers migration
php artisan make:migration create_customers_table
```

Edit `database/migrations/xxxx_xx_xx_xxxxxx_create_customers_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('zip_code')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
```

### 3. Service Categories Table

```bash
# Create service categories migration
php artisan make:migration create_service_categories_table
```

Edit the migration file:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('service_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('icon')->nullable(); // For UI icons
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('service_categories');
    }
};
```

### 4. Services Table

```bash
# Create services migration
php artisan make:migration create_services_table
```

Edit the migration file:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_category_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 8, 2); // 999999.99 max
            $table->integer('duration_minutes'); // Service duration
            $table->boolean('is_active')->default(true);
            $table->string('image')->nullable(); // Service image
            $table->json('features')->nullable(); // Service features as JSON
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
```

### 5. Bookings Table

```bash
# Create bookings migration
php artisan make:migration create_bookings_table
```

Edit the migration file:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->string('booking_number')->unique(); // e.g., CW-2024-001
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Staff member
            $table->datetime('scheduled_at');
            $table->enum('status', [
                'pending',
                'confirmed', 
                'in_progress',
                'completed',
                'cancelled',
                'no_show'
            ])->default('pending');
            $table->decimal('total_amount', 10, 2);
            $table->decimal('paid_amount', 10, 2)->default(0);
            $table->enum('payment_status', [
                'pending',
                'partial',
                'paid',
                'refunded'
            ])->default('pending');
            $table->string('vehicle_make')->nullable();
            $table->string('vehicle_model')->nullable();
            $table->string('vehicle_year')->nullable();
            $table->string('vehicle_color')->nullable();
            $table->string('license_plate')->nullable();
            $table->text('special_instructions')->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
```

### 6. Booking Services Table (Pivot Table)

```bash
# Create booking services migration
php artisan make:migration create_booking_services_table
```

Edit the migration file:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('booking_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->decimal('price', 8, 2); // Price at time of booking
            $table->integer('quantity')->default(1);
            $table->text('notes')->nullable();
            $table->timestamps();
            
            // Ensure unique booking-service combinations
            $table->unique(['booking_id', 'service_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('booking_services');
    }
};
```

## 🛠 Step 5: Running the Migrations

Now let's create all our tables:

```bash
# Run all migrations
php artisan migrate
```

You should see output like:
```
Migrating: 2014_10_12_000000_create_users_table
Migrated:  2014_10_12_000000_create_users_table (45.67ms)
Migrating: 2014_10_12_100000_create_password_reset_tokens_table
Migrated:  2014_10_12_100000_create_password_reset_tokens_table (32.89ms)
Migrating: 2019_08_19_000000_create_failed_jobs_table
Migrated:  2019_08_19_000000_create_failed_jobs_table (28.45ms)
Migrating: 2019_12_14_000001_create_personal_access_tokens_table
Migrated:  2019_12_14_000001_create_personal_access_tokens_table (41.23ms)
Migrating: xxxx_xx_xx_xxxxxx_create_customers_table
Migrated:  xxxx_xx_xx_xxxxxx_create_customers_table (35.67ms)
Migrating: xxxx_xx_xx_xxxxxx_create_service_categories_table
Migrated:  xxxx_xx_xx_xxxxxx_create_service_categories_table (28.34ms)
Migrating: xxxx_xx_xx_xxxxxx_create_services_table
Migrated:  xxxx_xx_xx_xxxxxx_create_services_table (42.12ms)
Migrating: xxxx_xx_xx_xxxxxx_create_bookings_table
Migrated:  xxxx_xx_xx_xxxxxx_create_bookings_table (56.78ms)
Migrating: xxxx_xx_xx_xxxxxx_create_booking_services_table
Migrated:  xxxx_xx_xx_xxxxxx_create_booking_services_table (38.91ms)
```

### Verify Tables Were Created

```bash
# Check migration status
php artisan migrate:status
```

For MySQL, you can also check directly:
```bash
mysql -u carwash_user -p car_wash_management -e "SHOW TABLES;"
```

## 🛠 Step 6: Creating Database Seeders

Seeders allow us to populate our database with test data. Let's create seeders for our tables.

### 1. Service Categories Seeder

```bash
# Create service categories seeder
php artisan make:seeder ServiceCategorySeeder
```

Edit `database/seeders/ServiceCategorySeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ServiceCategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Basic Wash',
                'description' => 'Essential car washing services',
                'icon' => 'car-wash',
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Premium Wash',
                'description' => 'Premium car washing with additional services',
                'icon' => 'star',
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Detailing',
                'description' => 'Professional car detailing services',
                'icon' => 'sparkles',
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Specialty Services',
                'description' => 'Specialized automotive services',
                'icon' => 'tools',
                'is_active' => true,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('service_categories')->insert($categories);
    }
}
```

### 2. Services Seeder

```bash
# Create services seeder
php artisan make:seeder ServiceSeeder
```

Edit `database/seeders/ServiceSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ServiceSeeder extends Seeder
{
    public function run(): void
    {
        $services = [
            // Basic Wash Services (category_id: 1)
            [
                'service_category_id' => 1,
                'name' => 'Express Wash',
                'description' => 'Quick exterior wash and dry',
                'price' => 15.99,
                'duration_minutes' => 15,
                'is_active' => true,
                'features' => json_encode(['Exterior wash', 'Machine dry', 'Tire cleaning']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'service_category_id' => 1,
                'name' => 'Standard Wash',
                'description' => 'Exterior wash with interior vacuum',
                'price' => 25.99,
                'duration_minutes' => 30,
                'is_active' => true,
                'features' => json_encode(['Exterior wash', 'Interior vacuum', 'Window cleaning', 'Tire shine']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            
            // Premium Wash Services (category_id: 2)
            [
                'service_category_id' => 2,
                'name' => 'Deluxe Wash',
                'description' => 'Complete wash with wax protection',
                'price' => 39.99,
                'duration_minutes' => 45,
                'is_active' => true,
                'features' => json_encode(['Premium wash', 'Hand wax', 'Interior detail', 'Dashboard treatment']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'service_category_id' => 2,
                'name' => 'Ultimate Wash',
                'description' => 'Our most comprehensive wash package',
                'price' => 59.99,
                'duration_minutes' => 60,
                'is_active' => true,
                'features' => json_encode(['Premium wash', 'Ceramic coating', 'Full interior detail', 'Engine bay cleaning']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            
            // Detailing Services (category_id: 3)
            [
                'service_category_id' => 3,
                'name' => 'Interior Detailing',
                'description' => 'Deep cleaning of vehicle interior',
                'price' => 89.99,
                'duration_minutes' => 120,
                'is_active' => true,
                'features' => json_encode(['Deep vacuum', 'Leather treatment', 'Fabric protection', 'Odor elimination']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'service_category_id' => 3,
                'name' => 'Exterior Detailing',
                'description' => 'Professional exterior paint correction',
                'price' => 149.99,
                'duration_minutes' => 180,
                'is_active' => true,
                'features' => json_encode(['Paint correction', 'Clay bar treatment', 'Premium wax', 'Headlight restoration']),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('services')->insert($services);
    }
}
```

### 3. Update DatabaseSeeder

Edit `database/seeders/DatabaseSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            ServiceCategorySeeder::class,
            ServiceSeeder::class,
        ]);
    }
}
```

### Run the Seeders

```bash
# Run all seeders
php artisan db:seed
```

## 🧪 Testing Our Database Setup

Let's verify everything is working correctly:

### 1. Check Tables and Data

```bash
# For MySQL
mysql -u carwash_user -p car_wash_management -e "
SELECT 'Service Categories' as Table_Name, COUNT(*) as Record_Count FROM service_categories
UNION ALL
SELECT 'Services', COUNT(*) FROM services
UNION ALL
SELECT 'Customers', COUNT(*) FROM customers
UNION ALL
SELECT 'Bookings', COUNT(*) FROM bookings;"
```

### 2. Test with Tinker

Laravel Tinker allows you to interact with your application from the command line:

```bash
# Start Tinker
php artisan tinker
```

In Tinker, try these commands:

```php
// Check service categories
DB::table('service_categories')->count();

// Get all services with their categories
DB::table('services')
    ->join('service_categories', 'services.service_category_id', '=', 'service_categories.id')
    ->select('services.name as service_name', 'service_categories.name as category_name', 'services.price')
    ->get();

// Exit Tinker
exit
```

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Installed and configured MySQL/SQLite database  
✅ Set up Laravel database configuration  
✅ Created comprehensive database migrations  
✅ Understood Laravel migration system  
✅ Created database seeders with test data  
✅ Tested your database setup  
✅ Learned to use Laravel Tinker for database testing  

### Database Structure Created:
- **Users**: Authentication and staff management
- **Customers**: Customer information and contact details
- **Service Categories**: Organizing services into categories
- **Services**: Individual car wash services with pricing
- **Bookings**: Customer appointments and scheduling
- **Booking Services**: Many-to-many relationship between bookings and services

## 🚀 What's Next?

In the next chapter, we'll:
- Set up Laravel Breeze for authentication
- Create user registration and login functionality
- Customize authentication views for our car wash theme
- Implement password reset functionality

## 💡 Pro Tips

1. **Always backup your database** before running migrations in production
2. **Use descriptive migration names** for better organization
3. **Test your migrations** with rollback before deploying
4. **Use seeders for consistent test data** across environments
5. **Keep your .env file secure** and never commit it to version control

## 🔧 Troubleshooting

### Common Issues and Solutions

**Issue: "Access denied for user"**
- Solution: Check your database credentials in `.env`

**Issue: "Database does not exist"**
- Solution: Create the database manually in MySQL

**Issue: "Migration table not found"**
- Solution: Run `php artisan migrate:install`

**Issue: Foreign key constraint errors**
- Solution: Ensure parent tables exist before creating foreign keys

---

**Ready for authentication?** Let's move on to [Chapter 3: Authentication System](./03-authentication.md) where we'll implement user login and registration!
