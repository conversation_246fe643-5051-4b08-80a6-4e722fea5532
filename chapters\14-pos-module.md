# Chapter 14: POS (Point of Sale) Module

Welcome to Chapter 14! In this chapter, we'll build a comprehensive Point of Sale (POS) system that integrates seamlessly with our existing car wash management system, providing real-time transaction processing, receipt generation, and cash drawer management.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Build a real-time transaction processing interface
- Implement product/service selection with dynamic pricing
- Add support for multiple payment methods (cash, card, digital)
- Create receipt generation and printing functionality
- Build daily sales reporting and cash drawer management
- Integrate POS with existing booking and service systems
- Add inventory tracking for retail products
- Implement discount and promotion management

## 📋 What We'll Cover

1. Setting up POS database structure
2. Creating POS transaction models
3. Building the POS interface
4. Implementing payment processing
5. Receipt generation and printing
6. Cash drawer management
7. Sales reporting and analytics
8. Integration with existing systems

## 🛠 Step 1: Database Structure for POS

First, let's create the necessary migrations for our POS system:

```bash
# Create POS-related migrations
php artisan make:migration create_pos_transactions_table
php artisan make:migration create_pos_transaction_items_table
php artisan make:migration create_products_table
php artisan make:migration create_cash_drawers_table
php artisan make:migration create_pos_sessions_table
php artisan make:migration create_discounts_table
```

Edit `database/migrations/create_pos_transactions_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pos_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_number')->unique();
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Cashier
            $table->foreignId('pos_session_id')->constrained()->onDelete('cascade');
            $table->decimal('subtotal', 10, 2);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('amount_paid', 10, 2);
            $table->decimal('change_amount', 10, 2)->default(0);
            $table->enum('payment_method', ['cash', 'card', 'digital_wallet', 'bank_transfer', 'mixed']);
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded'])->default('pending');
            $table->json('payment_details')->nullable(); // Store payment method details
            $table->text('notes')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->index(['transaction_number']);
            $table->index(['customer_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index(['pos_session_id']);
            $table->index(['status', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pos_transactions');
    }
};
```

Edit `database/migrations/create_pos_transaction_items_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pos_transaction_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pos_transaction_id')->constrained()->onDelete('cascade');
            $table->string('item_type'); // 'service' or 'product'
            $table->unsignedBigInteger('item_id'); // service_id or product_id
            $table->string('item_name'); // Store name for historical purposes
            $table->decimal('unit_price', 10, 2);
            $table->integer('quantity');
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_price', 10, 2);
            $table->json('item_details')->nullable(); // Store additional item details
            $table->timestamps();

            $table->index(['pos_transaction_id']);
            $table->index(['item_type', 'item_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pos_transaction_items');
    }
};
```

Edit `database/migrations/create_products_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('sku')->unique();
            $table->text('description')->nullable();
            $table->string('category');
            $table->decimal('price', 10, 2);
            $table->decimal('cost', 10, 2)->nullable();
            $table->integer('stock_quantity')->default(0);
            $table->integer('min_stock_level')->default(0);
            $table->string('unit')->default('piece'); // piece, bottle, liter, etc.
            $table->string('barcode')->nullable()->unique();
            $table->string('image_path')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('track_inventory')->default(true);
            $table->json('attributes')->nullable(); // Size, color, etc.
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index(['sku']);
            $table->index(['barcode']);
            $table->index(['stock_quantity', 'min_stock_level']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
```

Edit `database/migrations/create_cash_drawers_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cash_drawers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('location');
            $table->decimal('opening_balance', 10, 2)->default(0);
            $table->decimal('current_balance', 10, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->json('denominations')->nullable(); // Track cash denominations
            $table->timestamps();

            $table->index(['is_active']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cash_drawers');
    }
};
```

Edit `database/migrations/create_pos_sessions_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pos_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Cashier
            $table->foreignId('cash_drawer_id')->constrained()->onDelete('cascade');
            $table->decimal('opening_balance', 10, 2);
            $table->decimal('closing_balance', 10, 2)->nullable();
            $table->decimal('expected_balance', 10, 2)->nullable();
            $table->decimal('cash_sales', 10, 2)->default(0);
            $table->decimal('card_sales', 10, 2)->default(0);
            $table->decimal('digital_sales', 10, 2)->default(0);
            $table->integer('transaction_count')->default(0);
            $table->timestamp('opened_at');
            $table->timestamp('closed_at')->nullable();
            $table->enum('status', ['open', 'closed'])->default('open');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['cash_drawer_id', 'status']);
            $table->index(['opened_at', 'closed_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pos_sessions');
    }
};
```

Edit `database/migrations/create_discounts_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['percentage', 'fixed_amount']);
            $table->decimal('value', 10, 2);
            $table->decimal('minimum_amount', 10, 2)->nullable();
            $table->decimal('maximum_discount', 10, 2)->nullable();
            $table->integer('usage_limit')->nullable();
            $table->integer('used_count')->default(0);
            $table->boolean('is_active')->default(true);
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->json('applicable_items')->nullable(); // Services/products this applies to
            $table->timestamps();

            $table->index(['code', 'is_active']);
            $table->index(['start_date', 'end_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('discounts');
    }
};
```

Run the migrations:

```bash
php artisan migrate
```

## 🛠 Step 2: Creating POS Models

Create the models for our POS system:

```bash
# Create POS models
php artisan make:model PosTransaction
php artisan make:model PosTransactionItem
php artisan make:model Product
php artisan make:model CashDrawer
php artisan make:model PosSession
php artisan make:model Discount
```

Edit `app/Models/PosTransaction.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PosTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_number',
        'customer_id',
        'user_id',
        'pos_session_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'amount_paid',
        'change_amount',
        'payment_method',
        'status',
        'payment_details',
        'notes',
        'completed_at',
    ];

    protected $casts = [
        'payment_details' => 'array',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'change_amount' => 'decimal:2',
        'completed_at' => 'datetime',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';

    // Payment method constants
    const PAYMENT_CASH = 'cash';
    const PAYMENT_CARD = 'card';
    const PAYMENT_DIGITAL_WALLET = 'digital_wallet';
    const PAYMENT_BANK_TRANSFER = 'bank_transfer';
    const PAYMENT_MIXED = 'mixed';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->transaction_number)) {
                $transaction->transaction_number = static::generateTransactionNumber();
            }
        });
    }

    public static function generateTransactionNumber(): string
    {
        $prefix = 'POS';
        $date = now()->format('Ymd');
        $lastTransaction = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastTransaction ? 
            intval(substr($lastTransaction->transaction_number, -4)) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function posSession(): BelongsTo
    {
        return $this->belongsTo(PosSession::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(PosTransactionItem::class);
    }

    // Accessors
    public function getFormattedTotalAttribute(): string
    {
        return '$' . number_format($this->total_amount, 2);
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_REFUNDED => 'Refunded',
            default => 'Unknown',
        };
    }

    public function getPaymentMethodLabelAttribute(): string
    {
        return match($this->payment_method) {
            self::PAYMENT_CASH => 'Cash',
            self::PAYMENT_CARD => 'Card',
            self::PAYMENT_DIGITAL_WALLET => 'Digital Wallet',
            self::PAYMENT_BANK_TRANSFER => 'Bank Transfer',
            self::PAYMENT_MIXED => 'Mixed Payment',
            default => 'Unknown',
        };
    }

    // Methods
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->change_amount = max(0, $this->amount_paid - $this->total_amount);
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'completed_at' => now(),
        ]);
    }

    public function canBeRefunded(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }
}
```

## 🛠 Step 5: Creating POS Interface Views

Now let's create the comprehensive POS interface for cashiers and managers.

### 5.1 POS Main Interface

Create `resources/views/pos/index.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                POS Terminal - {{ $currentSession ? 'Session #' . $currentSession->id : 'No Active Session' }}
            </h2>
            <div class="flex space-x-2">
                @if(!$currentSession)
                    <button onclick="openSession()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Open Session
                    </button>
                @else
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                        Cash: ${{ number_format($currentSession->current_cash_amount, 2) }}
                    </span>
                    <button onclick="closeSession()" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Close Session
                    </button>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(!$currentSession)
                <!-- No Active Session -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                    <h3 class="text-lg font-medium text-yellow-800 mb-2">No Active POS Session</h3>
                    <p class="text-yellow-700 mb-4">Please open a POS session to start processing transactions.</p>
                    <button onclick="openSession()" class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-6 rounded">
                        Open New Session
                    </button>
                </div>
            @else
                <!-- POS Interface -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Product/Service Selection -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Search and Categories -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-4">
                                <div class="flex space-x-4 mb-4">
                                    <input type="text" id="product-search" placeholder="Search products/services..."
                                           class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <button onclick="scanBarcode()" class="bg-blue-500 hover:bg-blue-700 text-white px-4 py-2 rounded">
                                        Scan Barcode
                                    </button>
                                </div>

                                <!-- Category Tabs -->
                                <div class="flex space-x-2 mb-4">
                                    <button onclick="filterCategory('all')" class="category-btn active bg-blue-500 text-white px-4 py-2 rounded">
                                        All
                                    </button>
                                    <button onclick="filterCategory('services')" class="category-btn bg-gray-200 text-gray-700 px-4 py-2 rounded">
                                        Services
                                    </button>
                                    <button onclick="filterCategory('products')" class="category-btn bg-gray-200 text-gray-700 px-4 py-2 rounded">
                                        Products
                                    </button>
                                    <button onclick="filterCategory('packages')" class="category-btn bg-gray-200 text-gray-700 px-4 py-2 rounded">
                                        Packages
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Product Grid -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-4">
                                <div id="product-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                    <!-- Services -->
                                    @foreach($services as $service)
                                        <div class="product-item border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-blue-50"
                                             data-category="services" onclick="addToCart('service', {{ $service->id }}, '{{ $service->name }}', {{ $service->price }})">
                                            <div class="text-center">
                                                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                                    </svg>
                                                </div>
                                                <h3 class="font-medium text-gray-900 text-sm">{{ $service->name }}</h3>
                                                <p class="text-blue-600 font-bold">${{ number_format($service->price, 2) }}</p>
                                                <p class="text-xs text-gray-500">{{ $service->duration }} min</p>
                                            </div>
                                        </div>
                                    @endforeach

                                    <!-- Products -->
                                    @foreach($products as $product)
                                        <div class="product-item border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-green-50"
                                             data-category="products" onclick="addToCart('product', {{ $product->id }}, '{{ $product->name }}', {{ $product->price }})">
                                            <div class="text-center">
                                                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                                    </svg>
                                                </div>
                                                <h3 class="font-medium text-gray-900 text-sm">{{ $product->name }}</h3>
                                                <p class="text-green-600 font-bold">${{ number_format($product->price, 2) }}</p>
                                                <p class="text-xs text-gray-500">Stock: {{ $product->stock_quantity }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart and Checkout -->
                    <div class="space-y-6">
                        <!-- Customer Selection -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-4">
                                <h3 class="font-medium text-gray-900 mb-3">Customer</h3>
                                <div class="flex space-x-2">
                                    <select id="customer-select" class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Walk-in Customer</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}">{{ $customer->name }} - {{ $customer->phone }}</option>
                                        @endforeach
                                    </select>
                                    <button onclick="addNewCustomer()" class="bg-blue-500 hover:bg-blue-700 text-white px-3 py-2 rounded">
                                        +
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Shopping Cart -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <h3 class="font-medium text-gray-900">Cart</h3>
                                    <button onclick="clearCart()" class="text-red-600 hover:text-red-800 text-sm">Clear All</button>
                                </div>

                                <div id="cart-items" class="space-y-2 mb-4 max-h-64 overflow-y-auto">
                                    <!-- Cart items will be populated by JavaScript -->
                                </div>

                                <!-- Cart Totals -->
                                <div class="border-t pt-4 space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span>Subtotal:</span>
                                        <span id="subtotal">$0.00</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span>Tax:</span>
                                        <span id="tax-amount">$0.00</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span>Discount:</span>
                                        <span id="discount-amount" class="text-red-600">-$0.00</span>
                                    </div>
                                    <div class="flex justify-between font-bold text-lg border-t pt-2">
                                        <span>Total:</span>
                                        <span id="total-amount">$0.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Discount and Payment -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-4 space-y-4">
                                <!-- Discount Code -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Discount Code</label>
                                    <div class="flex space-x-2">
                                        <input type="text" id="discount-code" placeholder="Enter discount code"
                                               class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <button onclick="applyDiscount()" class="bg-purple-500 hover:bg-purple-700 text-white px-4 py-2 rounded">
                                            Apply
                                        </button>
                                    </div>
                                </div>

                                <!-- Payment Method -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                                    <select id="payment-method" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="cash">Cash</option>
                                        <option value="card">Credit/Debit Card</option>
                                        <option value="digital_wallet">Digital Wallet</option>
                                        <option value="bank_transfer">Bank Transfer</option>
                                    </select>
                                </div>

                                <!-- Cash Payment Fields -->
                                <div id="cash-payment" class="space-y-2">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Amount Received</label>
                                        <input type="number" id="amount-received" step="0.01"
                                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                               onchange="calculateChange()">
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span>Change:</span>
                                        <span id="change-amount" class="font-bold">$0.00</span>
                                    </div>
                                </div>

                                <!-- Checkout Button -->
                                <button onclick="processPayment()" id="checkout-btn"
                                        class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-4 rounded text-lg disabled:opacity-50"
                                        disabled>
                                    Process Payment
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Session Management Modal -->
    <div id="session-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4" id="session-modal-title">Open POS Session</h3>
                <form id="session-form">
                    <div class="mb-4">
                        <label for="opening-cash" class="block text-sm font-medium text-gray-700">Opening Cash Amount</label>
                        <input type="number" id="opening-cash" step="0.01" required
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeModal()"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Open Session
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let cart = [];
        let currentCustomer = null;
        let currentDiscount = null;

        // Cart Management
        function addToCart(type, id, name, price) {
            const existingItem = cart.find(item => item.type === type && item.id === id);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    type: type,
                    id: id,
                    name: name,
                    price: parseFloat(price),
                    quantity: 1
                });
            }

            updateCartDisplay();
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
        }

        function updateQuantity(index, quantity) {
            if (quantity <= 0) {
                removeFromCart(index);
            } else {
                cart[index].quantity = parseInt(quantity);
                updateCartDisplay();
            }
        }

        function clearCart() {
            cart = [];
            updateCartDisplay();
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cart-items');
            const subtotalEl = document.getElementById('subtotal');
            const taxAmountEl = document.getElementById('tax-amount');
            const discountAmountEl = document.getElementById('discount-amount');
            const totalAmountEl = document.getElementById('total-amount');
            const checkoutBtn = document.getElementById('checkout-btn');

            // Clear cart display
            cartItems.innerHTML = '';

            let subtotal = 0;

            // Display cart items
            cart.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;

                cartItems.innerHTML += `
                    <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <div class="flex-1">
                            <p class="font-medium text-sm">${item.name}</p>
                            <p class="text-xs text-gray-500">$${item.price.toFixed(2)} each</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="number" value="${item.quantity}" min="1"
                                   class="w-16 text-center rounded border-gray-300"
                                   onchange="updateQuantity(${index}, this.value)">
                            <span class="font-medium">$${itemTotal.toFixed(2)}</span>
                            <button onclick="removeFromCart(${index})" class="text-red-600 hover:text-red-800">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                `;
            });

            // Calculate totals
            const taxRate = 0.1; // 10% tax
            const taxAmount = subtotal * taxRate;
            const discountAmount = currentDiscount ? (subtotal * currentDiscount.percentage / 100) : 0;
            const total = subtotal + taxAmount - discountAmount;

            // Update display
            subtotalEl.textContent = `$${subtotal.toFixed(2)}`;
            taxAmountEl.textContent = `$${taxAmount.toFixed(2)}`;
            discountAmountEl.textContent = `-$${discountAmount.toFixed(2)}`;
            totalAmountEl.textContent = `$${total.toFixed(2)}`;

            // Enable/disable checkout button
            checkoutBtn.disabled = cart.length === 0;

            // Update change calculation if cash payment
            calculateChange();
        }

        function calculateChange() {
            const total = parseFloat(document.getElementById('total-amount').textContent.replace('$', ''));
            const received = parseFloat(document.getElementById('amount-received').value) || 0;
            const change = received - total;

            document.getElementById('change-amount').textContent = `$${Math.max(0, change).toFixed(2)}`;
        }

        // Session Management
        function openSession() {
            document.getElementById('session-modal').classList.remove('hidden');
        }

        function closeSession() {
            if (confirm('Are you sure you want to close the current session?')) {
                // Implementation for closing session
                window.location.reload();
            }
        }

        function closeModal() {
            document.getElementById('session-modal').classList.add('hidden');
        }

        // Category Filtering
        function filterCategory(category) {
            const items = document.querySelectorAll('.product-item');
            const buttons = document.querySelectorAll('.category-btn');

            // Update button styles
            buttons.forEach(btn => {
                btn.classList.remove('bg-blue-500', 'text-white');
                btn.classList.add('bg-gray-200', 'text-gray-700');
            });
            event.target.classList.remove('bg-gray-200', 'text-gray-700');
            event.target.classList.add('bg-blue-500', 'text-white');

            // Filter items
            items.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Payment Processing
        function processPayment() {
            if (cart.length === 0) return;

            const paymentMethod = document.getElementById('payment-method').value;
            const customerId = document.getElementById('customer-select').value;

            // Validate cash payment
            if (paymentMethod === 'cash') {
                const total = parseFloat(document.getElementById('total-amount').textContent.replace('$', ''));
                const received = parseFloat(document.getElementById('amount-received').value) || 0;

                if (received < total) {
                    alert('Insufficient payment amount');
                    return;
                }
            }

            // Process the transaction
            const transactionData = {
                customer_id: customerId || null,
                items: cart,
                payment_method: paymentMethod,
                discount_code: currentDiscount ? currentDiscount.code : null,
                amount_received: document.getElementById('amount-received').value || null
            };

            // Send to server
            fetch('{{ route("pos.process-transaction") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(transactionData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Transaction completed successfully!');
                    clearCart();
                    // Optionally print receipt
                    if (confirm('Print receipt?')) {
                        printReceipt(data.transaction_id);
                    }
                } else {
                    alert('Error processing transaction: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error processing transaction');
            });
        }

        function printReceipt(transactionId) {
            window.open(`{{ route('pos.receipt', '') }}/${transactionId}`, '_blank');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCartDisplay();
        });
    </script>
</x-app-layout>
```

### 5.2 Receipt Template

Create `resources/views/pos/receipt.blade.php`:

```blade
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt #{{ $transaction->transaction_number }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            max-width: 300px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .receipt-info {
            margin-bottom: 15px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        .items {
            margin-bottom: 15px;
        }
        .item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .item-details {
            flex: 1;
        }
        .item-price {
            text-align: right;
            min-width: 60px;
        }
        .totals {
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        .payment-info {
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        .footer {
            text-align: center;
            border-top: 1px dashed #000;
            padding-top: 10px;
            font-size: 10px;
        }
        @media print {
            body { margin: 0; padding: 10px; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Car Wash POS') }}</div>
        <div>123 Main Street, City, State 12345</div>
        <div>Phone: (*************</div>
        <div>Email: <EMAIL></div>
    </div>

    <!-- Receipt Information -->
    <div class="receipt-info">
        <div><strong>Receipt #:</strong> {{ $transaction->transaction_number }}</div>
        <div><strong>Date:</strong> {{ $transaction->created_at->format('M d, Y H:i:s') }}</div>
        <div><strong>Cashier:</strong> {{ $transaction->user->name }}</div>
        @if($transaction->customer)
            <div><strong>Customer:</strong> {{ $transaction->customer->name }}</div>
            @if($transaction->customer->phone)
                <div><strong>Phone:</strong> {{ $transaction->customer->phone }}</div>
            @endif
        @else
            <div><strong>Customer:</strong> Walk-in</div>
        @endif
    </div>

    <!-- Items -->
    <div class="items">
        @foreach($transaction->items as $item)
            <div class="item">
                <div class="item-details">
                    <div>{{ $item->name }}</div>
                    @if($item->quantity > 1)
                        <div style="font-size: 10px;">{{ $item->quantity }} x ${{ number_format($item->unit_price, 2) }}</div>
                    @endif
                </div>
                <div class="item-price">${{ number_format($item->total_price, 2) }}</div>
            </div>
        @endforeach
    </div>

    <!-- Totals -->
    <div class="totals">
        <div class="total-line">
            <span>Subtotal:</span>
            <span>${{ number_format($transaction->subtotal, 2) }}</span>
        </div>
        @if($transaction->discount_amount > 0)
            <div class="total-line">
                <span>Discount:</span>
                <span>-${{ number_format($transaction->discount_amount, 2) }}</span>
            </div>
        @endif
        <div class="total-line">
            <span>Tax:</span>
            <span>${{ number_format($transaction->tax_amount, 2) }}</span>
        </div>
        <div class="total-line grand-total">
            <span>TOTAL:</span>
            <span>${{ number_format($transaction->total_amount, 2) }}</span>
        </div>
    </div>

    <!-- Payment Information -->
    <div class="payment-info">
        <div class="total-line">
            <span>Payment Method:</span>
            <span>{{ ucfirst(str_replace('_', ' ', $transaction->payment_method)) }}</span>
        </div>
        @if($transaction->payment_method === 'cash')
            <div class="total-line">
                <span>Amount Paid:</span>
                <span>${{ number_format($transaction->amount_paid, 2) }}</span>
            </div>
            @if($transaction->change_amount > 0)
                <div class="total-line">
                    <span>Change:</span>
                    <span>${{ number_format($transaction->change_amount, 2) }}</span>
                </div>
            @endif
        @endif
    </div>

    <!-- Footer -->
    <div class="footer">
        <div>Thank you for your business!</div>
        <div>Visit us again soon</div>
        <div style="margin-top: 10px;">
            <div>Follow us on social media</div>
            <div>@carwashcompany</div>
        </div>
        @if($transaction->customer && $transaction->customer->email)
            <div style="margin-top: 10px;">
                <div>Receipt emailed to:</div>
                <div>{{ $transaction->customer->email }}</div>
            </div>
        @endif
    </div>

    <!-- Print Controls -->
    <div class="no-print" style="margin-top: 20px; text-align: center;">
        <button onclick="window.print()" style="padding: 10px 20px; font-size: 14px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Print Receipt
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; font-size: 14px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>

    <script>
        // Auto-print when page loads
        window.addEventListener('load', function() {
            // Small delay to ensure content is fully loaded
            setTimeout(function() {
                window.print();
            }, 500);
        });
    </script>
</body>
</html>
```

### 5.3 Sales Reports Dashboard

Create `resources/views/pos/reports.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                POS Sales Reports
            </h2>
            <div class="flex space-x-2">
                <button onclick="exportReport('pdf')" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Export PDF
                </button>
                <button onclick="exportReport('excel')" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Export Excel
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Date Range Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="GET" action="{{ route('pos.reports') }}" class="flex items-end space-x-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                            <input type="date" name="start_date" id="start_date" value="{{ request('start_date', now()->startOfMonth()->format('Y-m-d')) }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                            <input type="date" name="end_date" id="end_date" value="{{ request('end_date', now()->format('Y-m-d')) }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="cashier" class="block text-sm font-medium text-gray-700">Cashier</label>
                            <select name="cashier" id="cashier" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Cashiers</option>
                                @foreach($cashiers as $cashier)
                                    <option value="{{ $cashier->id }}" {{ request('cashier') == $cashier->id ? 'selected' : '' }}>
                                        {{ $cashier->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Filter
                        </button>
                    </form>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Sales</p>
                                <p class="text-2xl font-semibold text-gray-900">${{ number_format($summary['total_sales'], 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Transactions</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($summary['total_transactions']) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Average Sale</p>
                                <p class="text-2xl font-semibold text-gray-900">${{ number_format($summary['average_sale'], 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Items Sold</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($summary['total_items']) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Daily Sales Chart -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Daily Sales Trend</h3>
                        <canvas id="dailySalesChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Payment Methods Chart -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Methods</h3>
                        <canvas id="paymentMethodsChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Top Products/Services -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Top Services -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Top Services</h3>
                        <div class="space-y-3">
                            @foreach($topServices as $service)
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                                    <div>
                                        <p class="font-medium">{{ $service->name }}</p>
                                        <p class="text-sm text-gray-500">{{ $service->total_quantity }} sold</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold">${{ number_format($service->total_revenue, 2) }}</p>
                                        <p class="text-sm text-gray-500">Revenue</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Top Products -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Top Products</h3>
                        <div class="space-y-3">
                            @foreach($topProducts as $product)
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                                    <div>
                                        <p class="font-medium">{{ $product->name }}</p>
                                        <p class="text-sm text-gray-500">{{ $product->total_quantity }} sold</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold">${{ number_format($product->total_revenue, 2) }}</p>
                                        <p class="text-sm text-gray-500">Revenue</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Transactions</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction #</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cashier</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($recentTransactions as $transaction)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $transaction->transaction_number }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $transaction->created_at->format('M d, Y H:i') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $transaction->customer ? $transaction->customer->name : 'Walk-in' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $transaction->user->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($transaction->payment_method === 'cash') bg-green-100 text-green-800
                                                @elseif($transaction->payment_method === 'card') bg-blue-100 text-blue-800
                                                @else bg-purple-100 text-purple-800 @endif">
                                                {{ ucfirst(str_replace('_', ' ', $transaction->payment_method)) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            ${{ number_format($transaction->total_amount, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="{{ route('pos.receipt', $transaction) }}" target="_blank"
                                               class="text-indigo-600 hover:text-indigo-900 mr-3">View Receipt</a>
                                            @if($transaction->canBeRefunded())
                                                <button onclick="refundTransaction({{ $transaction->id }})"
                                                        class="text-red-600 hover:text-red-900">Refund</button>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $recentTransactions->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Daily Sales Chart
        const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
        new Chart(dailySalesCtx, {
            type: 'line',
            data: {
                labels: {!! json_encode($dailySalesData['labels']) !!},
                datasets: [{
                    label: 'Daily Sales',
                    data: {!! json_encode($dailySalesData['data']) !!},
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Payment Methods Chart
        const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
        new Chart(paymentMethodsCtx, {
            type: 'doughnut',
            data: {
                labels: {!! json_encode($paymentMethodsData['labels']) !!},
                datasets: [{
                    data: {!! json_encode($paymentMethodsData['data']) !!},
                    backgroundColor: [
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(147, 51, 234, 0.8)',
                        'rgba(245, 158, 11, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        function exportReport(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', format);
            window.open(`{{ route('pos.reports') }}?${params.toString()}`, '_blank');
        }

        function refundTransaction(transactionId) {
            if (confirm('Are you sure you want to refund this transaction?')) {
                fetch(`/pos/transactions/${transactionId}/refund`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Transaction refunded successfully');
                        location.reload();
                    } else {
                        alert('Error processing refund: ' + data.message);
                    }
                });
            }
        }
    </script>
</x-app-layout>
```

## 🧪 Testing the POS System

1. **Test Transaction Processing**:
   - Create transactions with different payment methods
   - Test inventory updates for products
   - Verify receipt generation

2. **Test Cash Drawer Management**:
   - Open and close POS sessions
   - Track cash flow and balancing
   - Generate session reports

3. **Test Integration**:
   - Verify integration with existing booking system
   - Test customer data synchronization
   - Validate reporting accuracy

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Built a comprehensive POS system with real-time transaction processing
✅ Implemented product/service selection with dynamic pricing
✅ Added support for multiple payment methods
✅ Created receipt generation and printing functionality
✅ Built daily sales reporting and cash drawer management
✅ Integrated POS with existing booking and service systems
✅ Added inventory tracking for retail products
✅ Implemented discount and promotion management

### POS Features Implemented:
- **Real-time Transaction Processing**: Fast and efficient checkout process
- **Multi-payment Support**: Cash, card, digital wallet, and bank transfer options
- **Inventory Management**: Automatic stock updates and low-stock alerts
- **Receipt System**: Professional receipt generation with printing support
- **Cash Drawer Management**: Complete session tracking and balancing
- **Discount System**: Flexible promotion and discount code management
- **Integration**: Seamless integration with existing car wash systems
- **Reporting**: Comprehensive sales analytics and reporting

## 🚀 What's Next?

In the next chapter, we'll:
- Build a digital queue management system
- Create real-time queue status displays
- Implement estimated wait times
- Add digital signage for customer information
- Integrate queue system with bookings

---

**Ready for queue management?** Let's move on to [Chapter 15: Queue Display System](./15-queue-display-system.md)!
