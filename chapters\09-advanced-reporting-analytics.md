# Chapter 9: Advanced Reporting and Analytics

Welcome to Chapter 9! In this chapter, we'll build a comprehensive reporting and analytics system with advanced charts, revenue tracking, customer insights, and business intelligence features.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create advanced reporting with interactive charts
- Build revenue tracking and forecasting
- Implement customer analytics and insights
- Add performance metrics and KPIs
- Create exportable reports in multiple formats
- Build real-time analytics dashboard
- Implement data visualization with Chart.js

## 📋 What We'll Cover

1. Creating advanced reporting controller
2. Building revenue analytics and forecasting
3. Implementing customer behavior analytics
4. Creating service performance metrics
5. Adding export functionality (PDF, Excel, CSV)
6. Building interactive charts and visualizations
7. Creating scheduled reports
8. Testing the analytics system

## 🛠 Step 1: Installing Required Packages

First, let's install packages for advanced reporting:

```bash
# Install packages for PDF generation and Excel export
composer require barryvdh/laravel-dompdf
composer require maatwebsite/excel

# Publish configuration files
php artisan vendor:publish --provider="Barryvdh\DomPDF\ServiceProvider"
php artisan vendor:publish --provider="Maatwebsite\Excel\ExcelServiceProvider" --tag=config
```

## 🛠 Step 2: Creating Advanced Analytics Controller

Let's create a comprehensive analytics controller:

```bash
# Create analytics controller
php artisan make:controller AnalyticsController
```

Edit `app/Http/Controllers/AnalyticsController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Service;
use App\Models\Payment;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;

class AnalyticsController extends Controller
{
    public function index(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Revenue Analytics
        $revenueAnalytics = $this->getRevenueAnalytics($dateFrom, $dateTo);
        
        // Customer Analytics
        $customerAnalytics = $this->getCustomerAnalytics($dateFrom, $dateTo);
        
        // Service Performance
        $servicePerformance = $this->getServicePerformance($dateFrom, $dateTo);
        
        // Booking Trends
        $bookingTrends = $this->getBookingTrends($dateFrom, $dateTo);
        
        // Payment Analytics
        $paymentAnalytics = $this->getPaymentAnalytics($dateFrom, $dateTo);

        return view('analytics.index', compact(
            'revenueAnalytics', 'customerAnalytics', 'servicePerformance',
            'bookingTrends', 'paymentAnalytics', 'dateFrom', 'dateTo'
        ));
    }

    private function getRevenueAnalytics($dateFrom, $dateTo)
    {
        // Daily revenue for the period
        $dailyRevenue = Payment::successful()
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->where('type', Payment::TYPE_PAYMENT)
            ->selectRaw('DATE(created_at) as date, SUM(amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Monthly revenue comparison
        $currentMonthRevenue = Payment::successful()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->where('type', Payment::TYPE_PAYMENT)
            ->sum('amount');

        $previousMonthRevenue = Payment::successful()
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->where('type', Payment::TYPE_PAYMENT)
            ->sum('amount');

        $revenueGrowth = $previousMonthRevenue > 0 
            ? (($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100 
            : 0;

        // Revenue by service category
        $revenueByCategory = Service::join('booking_services', 'services.id', '=', 'booking_services.service_id')
            ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
            ->join('service_categories', 'services.service_category_id', '=', 'service_categories.id')
            ->join('payments', 'bookings.id', '=', 'payments.booking_id')
            ->where('payments.status', Payment::STATUS_SUCCEEDED)
            ->where('payments.type', Payment::TYPE_PAYMENT)
            ->whereBetween('payments.created_at', [$dateFrom, $dateTo])
            ->selectRaw('service_categories.name as category, SUM(booking_services.price * booking_services.quantity) as revenue')
            ->groupBy('service_categories.id', 'service_categories.name')
            ->orderBy('revenue', 'desc')
            ->get();

        // Average order value
        $averageOrderValue = Payment::successful()
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->where('type', Payment::TYPE_PAYMENT)
            ->avg('amount');

        return [
            'daily_revenue' => $dailyRevenue,
            'current_month_revenue' => $currentMonthRevenue,
            'previous_month_revenue' => $previousMonthRevenue,
            'revenue_growth' => round($revenueGrowth, 2),
            'revenue_by_category' => $revenueByCategory,
            'average_order_value' => round($averageOrderValue, 2),
        ];
    }

    private function getCustomerAnalytics($dateFrom, $dateTo)
    {
        // New customers in period
        $newCustomers = Customer::whereBetween('created_at', [$dateFrom, $dateTo])->count();
        
        // Customer retention rate
        $totalCustomers = Customer::count();
        $returningCustomers = Customer::whereHas('bookings', function ($query) use ($dateFrom, $dateTo) {
            $query->whereBetween('booking_date', [$dateFrom, $dateTo]);
        })->whereHas('bookings', function ($query) use ($dateFrom) {
            $query->where('booking_date', '<', $dateFrom);
        })->count();

        $retentionRate = $totalCustomers > 0 ? ($returningCustomers / $totalCustomers) * 100 : 0;

        // Customer lifetime value
        $customerLTV = Customer::withSum(['bookings as total_spent' => function ($query) {
            $query->where('payment_status', 'paid');
        }], 'total_amount')->avg('total_spent') ?? 0;

        // Top customers by revenue
        $topCustomers = Customer::withSum(['bookings as total_spent' => function ($query) use ($dateFrom, $dateTo) {
            $query->whereBetween('booking_date', [$dateFrom, $dateTo])
                  ->where('payment_status', 'paid');
        }], 'total_amount')
        ->having('total_spent', '>', 0)
        ->orderBy('total_spent', 'desc')
        ->limit(10)
        ->get();

        // Customer acquisition by month
        $customerAcquisition = Customer::selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count')
            ->whereBetween('created_at', [now()->subMonths(11)->startOfMonth(), now()->endOfMonth()])
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                return [
                    'period' => Carbon::create($item->year, $item->month)->format('M Y'),
                    'count' => $item->count
                ];
            });

        return [
            'new_customers' => $newCustomers,
            'retention_rate' => round($retentionRate, 2),
            'customer_ltv' => round($customerLTV, 2),
            'top_customers' => $topCustomers,
            'customer_acquisition' => $customerAcquisition,
        ];
    }

    private function getServicePerformance($dateFrom, $dateTo)
    {
        // Most popular services
        $popularServices = Service::withCount(['bookingServices as booking_count' => function ($query) use ($dateFrom, $dateTo) {
            $query->whereHas('booking', function ($q) use ($dateFrom, $dateTo) {
                $q->whereBetween('booking_date', [$dateFrom, $dateTo]);
            });
        }])
        ->withSum(['bookingServices as revenue' => function ($query) use ($dateFrom, $dateTo) {
            $query->whereHas('booking', function ($q) use ($dateFrom, $dateTo) {
                $q->whereBetween('booking_date', [$dateFrom, $dateTo])
                  ->where('payment_status', 'paid');
            });
        }], 'price')
        ->having('booking_count', '>', 0)
        ->orderBy('booking_count', 'desc')
        ->limit(10)
        ->get();

        // Service profitability
        $serviceProfitability = Service::select('services.*')
            ->withSum(['bookingServices as total_revenue' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereHas('booking', function ($q) use ($dateFrom, $dateTo) {
                    $q->whereBetween('booking_date', [$dateFrom, $dateTo])
                      ->where('payment_status', 'paid');
                });
            }], 'price')
            ->withCount(['bookingServices as booking_count' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereHas('booking', function ($q) use ($dateFrom, $dateTo) {
                    $q->whereBetween('booking_date', [$dateFrom, $dateTo]);
                });
            }])
            ->having('booking_count', '>', 0)
            ->orderBy('total_revenue', 'desc')
            ->get()
            ->map(function ($service) {
                $service->profit_margin = $service->total_revenue > 0 
                    ? (($service->total_revenue - ($service->booking_count * ($service->price * 0.3))) / $service->total_revenue) * 100 
                    : 0;
                return $service;
            });

        return [
            'popular_services' => $popularServices,
            'service_profitability' => $serviceProfitability,
        ];
    }

    private function getBookingTrends($dateFrom, $dateTo)
    {
        // Booking status distribution
        $statusDistribution = Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        // Hourly booking distribution
        $hourlyDistribution = Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
            ->selectRaw('HOUR(booking_time) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour');

        // Daily booking trends
        $dailyBookings = Booking::whereBetween('booking_date', [$dateFrom, $dateTo])
            ->selectRaw('DATE(booking_date) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Booking conversion rate
        $totalBookings = Booking::whereBetween('created_at', [$dateFrom, $dateTo])->count();
        $completedBookings = Booking::whereBetween('created_at', [$dateFrom, $dateTo])
            ->where('status', 'completed')->count();
        $conversionRate = $totalBookings > 0 ? ($completedBookings / $totalBookings) * 100 : 0;

        return [
            'status_distribution' => $statusDistribution,
            'hourly_distribution' => $hourlyDistribution,
            'daily_bookings' => $dailyBookings,
            'conversion_rate' => round($conversionRate, 2),
        ];
    }

    private function getPaymentAnalytics($dateFrom, $dateTo)
    {
        // Payment method distribution
        $paymentMethods = Payment::successful()
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->where('type', Payment::TYPE_PAYMENT)
            ->selectRaw('JSON_EXTRACT(payment_method_details, "$.type") as method, COUNT(*) as count, SUM(amount) as total')
            ->groupBy('method')
            ->get();

        // Failed payment analysis
        $failedPayments = Payment::failed()
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->count();

        $totalPaymentAttempts = Payment::whereBetween('created_at', [$dateFrom, $dateTo])
            ->where('type', Payment::TYPE_PAYMENT)
            ->count();

        $failureRate = $totalPaymentAttempts > 0 ? ($failedPayments / $totalPaymentAttempts) * 100 : 0;

        // Refund analysis
        $totalRefunds = Payment::where('type', Payment::TYPE_REFUND)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->sum('amount');

        $refundRate = Payment::successful()
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->where('type', Payment::TYPE_PAYMENT)
            ->sum('amount');

        $refundPercentage = $refundRate > 0 ? (abs($totalRefunds) / $refundRate) * 100 : 0;

        return [
            'payment_methods' => $paymentMethods,
            'failure_rate' => round($failureRate, 2),
            'refund_percentage' => round($refundPercentage, 2),
            'total_refunds' => abs($totalRefunds),
        ];
    }

    public function exportPDF(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $data = [
            'revenue_analytics' => $this->getRevenueAnalytics($dateFrom, $dateTo),
            'customer_analytics' => $this->getCustomerAnalytics($dateFrom, $dateTo),
            'service_performance' => $this->getServicePerformance($dateFrom, $dateTo),
            'booking_trends' => $this->getBookingTrends($dateFrom, $dateTo),
            'payment_analytics' => $this->getPaymentAnalytics($dateFrom, $dateTo),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'generated_at' => now()->format('M j, Y g:i A'),
        ];

        $pdf = PDF::loadView('analytics.pdf-report', $data);
        
        return $pdf->download("analytics-report-{$dateFrom}-to-{$dateTo}.pdf");
    }

    public function exportExcel(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        return Excel::download(
            new AnalyticsExport($dateFrom, $dateTo),
            "analytics-report-{$dateFrom}-to-{$dateTo}.xlsx"
        );
    }
}
```

## 🛠 Step 3: Creating Export Classes

Let's create the Excel export class:

```bash
# Create export class
php artisan make:export AnalyticsExport
```

Edit `app/Exports/AnalyticsExport.php`:

```php
<?php

namespace App\Exports;

use App\Http\Controllers\AnalyticsController;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;

class AnalyticsExport implements WithMultipleSheets
{
    protected $dateFrom;
    protected $dateTo;

    public function __construct($dateFrom, $dateTo)
    {
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
    }

    public function sheets(): array
    {
        return [
            new RevenueSheet($this->dateFrom, $this->dateTo),
            new CustomersSheet($this->dateFrom, $this->dateTo),
            new ServicesSheet($this->dateFrom, $this->dateTo),
            new BookingsSheet($this->dateFrom, $this->dateTo),
        ];
    }
}

class RevenueSheet implements FromCollection, WithHeadings, WithTitle
{
    protected $dateFrom;
    protected $dateTo;

    public function __construct($dateFrom, $dateTo)
    {
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
    }

    public function collection()
    {
        // Implementation for revenue data collection
        return collect([]);
    }

    public function headings(): array
    {
        return ['Date', 'Revenue', 'Transactions', 'Average Order Value'];
    }

    public function title(): string
    {
        return 'Revenue Analytics';
    }
}

// Similar classes for CustomersSheet, ServicesSheet, BookingsSheet...
```

## 🛠 Step 4: Creating Analytics Routes

Add analytics routes to `routes/web.php`:

```php
// Analytics routes - accessible by admin and staff only
Route::middleware(['auth', 'role:admin,staff'])->group(function () {
    Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('/analytics/export/pdf', [AnalyticsController::class, 'exportPDF'])->name('analytics.export.pdf');
    Route::get('/analytics/export/excel', [AnalyticsController::class, 'exportExcel'])->name('analytics.export.excel');
});
```

## 🛠 Step 5: Creating Analytics Views

Create the analytics dashboard view `resources/views/analytics/index.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Advanced Analytics') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('analytics.export.pdf', request()->query()) }}"
                   class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm">
                    Export PDF
                </a>
                <a href="{{ route('analytics.export.excel', request()->query()) }}"
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm">
                    Export Excel
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Date Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="GET" class="flex items-end space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">From Date</label>
                            <input type="date" name="date_from" value="{{ $dateFrom }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">To Date</label>
                            <input type="date" name="date_to" value="{{ $dateTo }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        </div>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                            Apply Filter
                        </button>
                    </form>
                </div>
            </div>

            <!-- Revenue Analytics -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Analytics</h3>

                    <!-- Revenue KPIs -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-blue-600">Current Month</p>
                            <p class="text-2xl font-bold text-blue-900">
                                ${{ number_format($revenueAnalytics['current_month_revenue'], 2) }}
                            </p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-green-600">Previous Month</p>
                            <p class="text-2xl font-bold text-green-900">
                                ${{ number_format($revenueAnalytics['previous_month_revenue'], 2) }}
                            </p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <p class="text-sm text-purple-600">Growth Rate</p>
                            <p class="text-2xl font-bold {{ $revenueAnalytics['revenue_growth'] >= 0 ? 'text-green-900' : 'text-red-900' }}">
                                {{ $revenueAnalytics['revenue_growth'] }}%
                            </p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <p class="text-sm text-orange-600">Avg Order Value</p>
                            <p class="text-2xl font-bold text-orange-900">
                                ${{ number_format($revenueAnalytics['average_order_value'], 2) }}
                            </p>
                        </div>
                    </div>

                    <!-- Revenue Chart -->
                    <div class="h-64">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Customer Analytics -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Analytics</h3>

                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">New Customers</span>
                                <span class="font-semibold">{{ $customerAnalytics['new_customers'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Retention Rate</span>
                                <span class="font-semibold">{{ $customerAnalytics['retention_rate'] }}%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Customer LTV</span>
                                <span class="font-semibold">${{ number_format($customerAnalytics['customer_ltv'], 2) }}</span>
                            </div>
                        </div>

                        <!-- Customer Acquisition Chart -->
                        <div class="mt-6 h-48">
                            <canvas id="customerAcquisitionChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Service Performance -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Top Services</h3>

                        <div class="space-y-3">
                            @foreach($servicePerformance['popular_services']->take(5) as $service)
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                                    <div>
                                        <p class="font-medium">{{ $service->name }}</p>
                                        <p class="text-sm text-gray-500">{{ $service->booking_count }} bookings</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold">${{ number_format($service->revenue, 2) }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Trends -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Trends</h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Status Distribution -->
                        <div>
                            <h4 class="font-medium mb-3">Status Distribution</h4>
                            <canvas id="statusChart" class="h-48"></canvas>
                        </div>

                        <!-- Hourly Distribution -->
                        <div>
                            <h4 class="font-medium mb-3">Peak Hours</h4>
                            <canvas id="hourlyChart" class="h-48"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Analytics -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Analytics</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-red-600">{{ $paymentAnalytics['failure_rate'] }}%</p>
                            <p class="text-sm text-gray-500">Failure Rate</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-orange-600">{{ $paymentAnalytics['refund_percentage'] }}%</p>
                            <p class="text-sm text-gray-500">Refund Rate</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-purple-600">${{ number_format($paymentAnalytics['total_refunds'], 2) }}</p>
                            <p class="text-sm text-gray-500">Total Refunds</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: {!! json_encode($revenueAnalytics['daily_revenue']->pluck('date')) !!},
                datasets: [{
                    label: 'Daily Revenue',
                    data: {!! json_encode($revenueAnalytics['daily_revenue']->pluck('revenue')) !!},
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        });

        // Customer Acquisition Chart
        const customerCtx = document.getElementById('customerAcquisitionChart').getContext('2d');
        new Chart(customerCtx, {
            type: 'bar',
            data: {
                labels: {!! json_encode($customerAnalytics['customer_acquisition']->pluck('period')) !!},
                datasets: [{
                    label: 'New Customers',
                    data: {!! json_encode($customerAnalytics['customer_acquisition']->pluck('count')) !!},
                    backgroundColor: 'rgba(34, 197, 94, 0.8)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Status Distribution Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: {!! json_encode($bookingTrends['status_distribution']->keys()) !!},
                datasets: [{
                    data: {!! json_encode($bookingTrends['status_distribution']->values()) !!},
                    backgroundColor: [
                        '#FCD34D', '#60A5FA', '#A78BFA', '#34D399', '#F87171', '#9CA3AF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Hourly Distribution Chart
        const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
        new Chart(hourlyCtx, {
            type: 'bar',
            data: {
                labels: Array.from({length: 24}, (_, i) => i + ':00'),
                datasets: [{
                    label: 'Bookings',
                    data: Array.from({length: 24}, (_, i) => {!! json_encode($bookingTrends['hourly_distribution']) !!}[i] || 0),
                    backgroundColor: 'rgba(168, 85, 247, 0.8)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
</x-app-layout>
```

## 🧪 Testing the Analytics System

1. **Test Analytics Dashboard**:
   - Access analytics with different date ranges
   - Verify all charts render correctly
   - Check data accuracy against database

2. **Test Export Functions**:
   - Export PDF reports
   - Export Excel files
   - Verify data integrity in exports

3. **Test Performance**:
   - Test with large datasets
   - Monitor query performance
   - Optimize slow queries if needed

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Created advanced reporting with interactive charts
✅ Built revenue tracking and forecasting
✅ Implemented customer analytics and insights
✅ Added performance metrics and KPIs
✅ Created exportable reports in multiple formats
✅ Built real-time analytics dashboard
✅ Implemented data visualization with Chart.js

### Analytics Features Implemented:
- **Revenue Analytics**: Daily revenue tracking, growth analysis, and forecasting
- **Customer Insights**: Retention rates, LTV, and acquisition trends
- **Service Performance**: Popular services and profitability analysis
- **Booking Trends**: Status distribution and peak hour analysis
- **Payment Analytics**: Failure rates, refund tracking, and method distribution
- **Export Capabilities**: PDF and Excel report generation
- **Interactive Charts**: Real-time data visualization with Chart.js

## 🚀 What's Next?

In the next chapter, we'll:
- Implement comprehensive user roles and permissions
- Create role-based access control system
- Add staff management and permissions
- Build admin panel for user management
- Create permission-based navigation and features

---

**Ready for user management?** Let's move on to [Chapter 10: User Roles and Permissions](./10-user-roles-permissions.md)!
```
