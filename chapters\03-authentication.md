# Chapter 3: Authentication System

Welcome to Chapter 3! In this chapter, we'll implement a complete authentication system using Laravel Breeze, customize it for our car wash management system, and create user roles for different types of users.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Install and configure <PERSON><PERSON> Breeze for authentication
- Understand Laravel's authentication system
- Customize authentication views for our car wash theme
- Implement user registration and login functionality
- Add user roles (<PERSON><PERSON>, Staff, Customer)
- Create middleware for role-based access control
- Test the complete authentication flow

## 📋 What We'll Cover

1. Installing Laravel Breeze
2. Understanding Laravel authentication
3. Customizing authentication views
4. Adding user roles and permissions
5. Creating role-based middleware
6. Testing authentication functionality
7. Securing routes with authentication

## 🛠 Step 1: Installing Laravel Breeze

Laravel Breeze provides a minimal, simple implementation of <PERSON><PERSON>'s authentication features.

```bash
# Install Laravel Breeze
composer require laravel/breeze --dev

# Install Breeze with Blade templates
php artisan breeze:install blade

# Install and compile frontend dependencies
npm install && npm run dev

# Run migrations (this will create additional auth tables)
php artisan migrate
```

You should see output confirming the installation of authentication views, controllers, and routes.

## 🛠 Step 2: Understanding What Breeze Installed

Let's explore what <PERSON><PERSON> added to our project:

### Authentication Routes
Check `routes/auth.php` - this file contains all authentication routes:
- Registration
- Login/Logout
- Password reset
- Email verification

### Authentication Controllers
Located in `app/Http/Controllers/Auth/`:
- `AuthenticatedSessionController` - Login/logout
- `RegisteredUserController` - User registration
- `PasswordResetLinkController` - Password reset
- `NewPasswordController` - Password reset confirmation

### Authentication Views
Located in `resources/views/auth/`:
- `login.blade.php` - Login form
- `register.blade.php` - Registration form
- `forgot-password.blade.php` - Password reset request
- `reset-password.blade.php` - Password reset form

### Middleware
- `app/Http/Middleware/Authenticate.php` - Redirect unauthenticated users
- `app/Http/Middleware/RedirectIfAuthenticated.php` - Redirect authenticated users

## 🛠 Step 3: Testing Basic Authentication

Let's test the authentication system:

```bash
# Start the development server
php artisan serve
```

Visit these URLs in your browser:
- `http://localhost:8000/register` - Registration page
- `http://localhost:8000/login` - Login page
- `http://localhost:8000/dashboard` - Dashboard (requires login)

Try creating a user account and logging in!

## 🛠 Step 4: Adding User Roles

Let's add role functionality to our users. First, we'll create a migration to add a role column:

```bash
# Create migration to add role to users table
php artisan make:migration add_role_to_users_table
```

Edit the migration file `database/migrations/xxxx_xx_xx_xxxxxx_add_role_to_users_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['admin', 'staff', 'customer'])->default('customer')->after('email');
            $table->boolean('is_active')->default(true)->after('role');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['role', 'is_active']);
        });
    }
};
```

Run the migration:

```bash
php artisan migrate
```

## 🛠 Step 5: Creating User Model Methods

Let's add role-related methods to our User model. Edit `app/Models/User.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'is_active',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    // Role checking methods
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function isStaff(): bool
    {
        return $this->role === 'staff';
    }

    public function isCustomer(): bool
    {
        return $this->role === 'customer';
    }

    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles);
    }

    // Scope for active users
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope for specific roles
    public function scopeRole($query, $role)
    {
        return $query->where('role', $role);
    }
}
```

## 🛠 Step 6: Creating Role-Based Middleware

Let's create middleware to protect routes based on user roles:

```bash
# Create role middleware
php artisan make:middleware RoleMiddleware
```

Edit `app/Http/Middleware/RoleMiddleware.php`:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        if (!$user->is_active) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Your account has been deactivated.');
        }

        if (!$user->hasAnyRole($roles)) {
            abort(403, 'Unauthorized access.');
        }

        return $next($request);
    }
}
```

Register the middleware in `bootstrap/app.php`:

```php
<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => \App\Http\Middleware\RoleMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
```

## 🛠 Step 7: Customizing Registration for Role Selection

Let's modify the registration form to allow role selection. First, update the registration controller.

Edit `app/Http/Controllers/Auth/RegisteredUserController.php`:

```php
<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    public function create(): View
    {
        return view('auth.register');
    }

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'in:admin,staff,customer'],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
        ]);

        event(new Registered($user));

        Auth::login($user);

        // Redirect based on role
        return $this->redirectBasedOnRole($user);
    }

    private function redirectBasedOnRole(User $user): RedirectResponse
    {
        return match($user->role) {
            'admin' => redirect()->route('admin.dashboard'),
            'staff' => redirect()->route('staff.dashboard'),
            'customer' => redirect()->route('customer.dashboard'),
            default => redirect()->route('dashboard'),
        };
    }
}
```

## 🛠 Step 8: Customizing Authentication Views

Let's customize the authentication views to match our car wash theme. First, let's update the registration form.

Edit `resources/views/auth/register.blade.php`:

```blade
<x-guest-layout>
    <div class="mb-4 text-center">
        <h2 class="text-2xl font-bold text-gray-900">Join Our Car Wash</h2>
        <p class="text-gray-600">Create your account to start booking services</p>
    </div>

    <form method="POST" action="{{ route('register') }}">
        @csrf

        <!-- Name -->
        <div>
            <x-input-label for="name" :value="__('Full Name')" />
            <x-text-input id="name" class="block mt-1 w-full" type="text" name="name" :value="old('name')" required autofocus autocomplete="name" />
            <x-input-error :messages="$errors->get('name')" class="mt-2" />
        </div>

        <!-- Email Address -->
        <div class="mt-4">
            <x-input-label for="email" :value="__('Email Address')" />
            <x-text-input id="email" class="block mt-1 w-full" type="email" name="email" :value="old('email')" required autocomplete="username" />
            <x-input-error :messages="$errors->get('email')" class="mt-2" />
        </div>

        <!-- Role Selection -->
        <div class="mt-4">
            <x-input-label for="role" :value="__('Account Type')" />
            <select id="role" name="role" class="block mt-1 w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm" required>
                <option value="">Select Account Type</option>
                <option value="customer" {{ old('role') == 'customer' ? 'selected' : '' }}>Customer</option>
                <option value="staff" {{ old('role') == 'staff' ? 'selected' : '' }}>Staff Member</option>
                <option value="admin" {{ old('role') == 'admin' ? 'selected' : '' }}>Administrator</option>
            </select>
            <x-input-error :messages="$errors->get('role')" class="mt-2" />
            <p class="text-sm text-gray-500 mt-1">Choose "Customer" to book services, "Staff" if you work here, or "Administrator" for management access.</p>
        </div>

        <!-- Password -->
        <div class="mt-4">
            <x-input-label for="password" :value="__('Password')" />
            <x-text-input id="password" class="block mt-1 w-full" type="password" name="password" required autocomplete="new-password" />
            <x-input-error :messages="$errors->get('password')" class="mt-2" />
        </div>

        <!-- Confirm Password -->
        <div class="mt-4">
            <x-input-label for="password_confirmation" :value="__('Confirm Password')" />
            <x-text-input id="password_confirmation" class="block mt-1 w-full" type="password" name="password_confirmation" required autocomplete="new-password" />
            <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
        </div>

        <div class="flex items-center justify-end mt-6">
            <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" href="{{ route('login') }}">
                {{ __('Already have an account?') }}
            </a>

            <x-primary-button class="ms-4">
                {{ __('Create Account') }}
            </x-primary-button>
        </div>
    </form>
</x-guest-layout>
```

Now let's update the login form. Edit `resources/views/auth/login.blade.php`:

```blade
<x-guest-layout>
    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <div class="mb-4 text-center">
        <h2 class="text-2xl font-bold text-gray-900">Welcome Back</h2>
        <p class="text-gray-600">Sign in to your car wash account</p>
    </div>

    <form method="POST" action="{{ route('login') }}">
        @csrf

        <!-- Email Address -->
        <div>
            <x-input-label for="email" :value="__('Email Address')" />
            <x-text-input id="email" class="block mt-1 w-full" type="email" name="email" :value="old('email')" required autofocus autocomplete="username" />
            <x-input-error :messages="$errors->get('email')" class="mt-2" />
        </div>

        <!-- Password -->
        <div class="mt-4">
            <x-input-label for="password" :value="__('Password')" />
            <x-text-input id="password" class="block mt-1 w-full" type="password" name="password" required autocomplete="current-password" />
            <x-input-error :messages="$errors->get('password')" class="mt-2" />
        </div>

        <!-- Remember Me -->
        <div class="block mt-4">
            <label for="remember_me" class="inline-flex items-center">
                <input id="remember_me" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="remember">
                <span class="ms-2 text-sm text-gray-600">{{ __('Remember me') }}</span>
            </label>
        </div>

        <div class="flex items-center justify-between mt-6">
            @if (Route::has('password.request'))
                <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" href="{{ route('password.request') }}">
                    {{ __('Forgot password?') }}
                </a>
            @endif

            <div class="flex items-center space-x-4">
                <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" href="{{ route('register') }}">
                    {{ __('Need an account?') }}
                </a>

                <x-primary-button>
                    {{ __('Sign In') }}
                </x-primary-button>
            </div>
        </div>
    </form>
</x-guest-layout>
```

## 🛠 Step 9: Creating Role-Specific Dashboards

Let's create different dashboard routes and views for each user role.

### Update Routes

Edit `routes/web.php`:

```php
<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Default dashboard route (redirects based on role)
Route::get('/dashboard', function () {
    $user = auth()->user();

    return match($user->role) {
        'admin' => redirect()->route('admin.dashboard'),
        'staff' => redirect()->route('staff.dashboard'),
        'customer' => redirect()->route('customer.dashboard'),
        default => view('dashboard'),
    };
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin routes
Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return view('admin.dashboard');
    })->name('dashboard');
});

// Staff routes
Route::middleware(['auth', 'verified', 'role:staff,admin'])->prefix('staff')->name('staff.')->group(function () {
    Route::get('/dashboard', function () {
        return view('staff.dashboard');
    })->name('dashboard');
});

// Customer routes
Route::middleware(['auth', 'verified', 'role:customer,staff,admin'])->prefix('customer')->name('customer.')->group(function () {
    Route::get('/dashboard', function () {
        return view('customer.dashboard');
    })->name('dashboard');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
```

### Create Dashboard Views

Create the admin dashboard view `resources/views/admin/dashboard.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Admin Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Welcome, {{ auth()->user()->name }}!</h3>
                        <p class="text-gray-600">You're logged in as an Administrator.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Quick Stats -->
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-900">Total Users</h4>
                            <p class="text-2xl font-bold text-blue-600">{{ \App\Models\User::count() }}</p>
                        </div>

                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-900">Active Services</h4>
                            <p class="text-2xl font-bold text-green-600">{{ DB::table('services')->where('is_active', true)->count() }}</p>
                        </div>

                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-900">Service Categories</h4>
                            <p class="text-2xl font-bold text-purple-600">{{ DB::table('service_categories')->where('is_active', true)->count() }}</p>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-semibold mb-4">Quick Actions</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-lg text-center transition">
                                Manage Users
                            </a>
                            <a href="#" class="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg text-center transition">
                                Manage Services
                            </a>
                            <a href="#" class="bg-purple-500 hover:bg-purple-600 text-white p-4 rounded-lg text-center transition">
                                View Reports
                            </a>
                            <a href="#" class="bg-orange-500 hover:bg-orange-600 text-white p-4 rounded-lg text-center transition">
                                System Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

Create the staff dashboard view `resources/views/staff/dashboard.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Staff Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Welcome, {{ auth()->user()->name }}!</h3>
                        <p class="text-gray-600">You're logged in as a Staff Member.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Today's Bookings -->
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-900">Today's Bookings</h4>
                            <p class="text-2xl font-bold text-blue-600">0</p>
                            <p class="text-sm text-blue-700">No bookings scheduled for today</p>
                        </div>

                        <!-- Pending Bookings -->
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-yellow-900">Pending Bookings</h4>
                            <p class="text-2xl font-bold text-yellow-600">0</p>
                            <p class="text-sm text-yellow-700">No pending bookings</p>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-semibold mb-4">Quick Actions</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-lg text-center transition">
                                View Bookings
                            </a>
                            <a href="#" class="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg text-center transition">
                                Manage Customers
                            </a>
                            <a href="#" class="bg-purple-500 hover:bg-purple-600 text-white p-4 rounded-lg text-center transition">
                                Service History
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

Create the customer dashboard view `resources/views/customer/dashboard.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Customer Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Welcome back, {{ auth()->user()->name }}!</h3>
                        <p class="text-gray-600">Ready to book your next car wash?</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Next Booking -->
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-900">Next Booking</h4>
                            <p class="text-lg text-green-700">No upcoming bookings</p>
                            <a href="#" class="text-green-600 hover:text-green-800 font-medium">Book a service →</a>
                        </div>

                        <!-- Recent Activity -->
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-900">Recent Activity</h4>
                            <p class="text-lg text-blue-700">No recent bookings</p>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">View history →</a>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-semibold mb-4">Quick Actions</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <a href="#" class="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-lg text-center transition">
                                Book a Service
                            </a>
                            <a href="#" class="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg text-center transition">
                                View My Bookings
                            </a>
                            <a href="#" class="bg-purple-500 hover:bg-purple-600 text-white p-4 rounded-lg text-center transition">
                                Service History
                            </a>
                        </div>
                    </div>

                    <!-- Available Services Preview -->
                    <div class="mt-8">
                        <h4 class="text-lg font-semibold mb-4">Our Services</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @php
                                $services = DB::table('services')
                                    ->join('service_categories', 'services.service_category_id', '=', 'service_categories.id')
                                    ->select('services.*', 'service_categories.name as category_name')
                                    ->where('services.is_active', true)
                                    ->limit(3)
                                    ->get();
                            @endphp

                            @forelse($services as $service)
                                <div class="border rounded-lg p-4 hover:shadow-md transition">
                                    <h5 class="font-semibold text-gray-900">{{ $service->name }}</h5>
                                    <p class="text-sm text-gray-600">{{ $service->category_name }}</p>
                                    <p class="text-lg font-bold text-green-600">${{ number_format($service->price, 2) }}</p>
                                    <p class="text-sm text-gray-500">{{ $service->duration_minutes }} minutes</p>
                                </div>
                            @empty
                                <p class="text-gray-500 col-span-3">No services available at the moment.</p>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

## 🛠 Step 10: Creating a User Seeder

Let's create a seeder to add some test users with different roles:

```bash
# Create user seeder
php artisan make:seeder UserSeeder
```

Edit `database/seeders/UserSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create staff user
        User::create([
            'name' => 'Staff Member',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'staff',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create customer user
        User::create([
            'name' => 'John Customer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'customer',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
    }
}
```

Update `database/seeders/DatabaseSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            UserSeeder::class,
            ServiceCategorySeeder::class,
            ServiceSeeder::class,
        ]);
    }
}
```

Run the seeder:

```bash
php artisan db:seed --class=UserSeeder
```

## 🧪 Testing the Authentication System

Let's test our complete authentication system:

### 1. Test User Registration

1. Visit `http://localhost:8000/register`
2. Create accounts with different roles
3. Verify you're redirected to the correct dashboard

### 2. Test User Login

1. Visit `http://localhost:8000/login`
2. Login with the seeded users:
   - Admin: `<EMAIL>` / `password`
   - Staff: `<EMAIL>` / `password`
   - Customer: `<EMAIL>` / `password`

### 3. Test Role-Based Access

Try accessing different dashboard URLs directly:
- `/admin/dashboard` - Should only work for admin users
- `/staff/dashboard` - Should work for staff and admin users
- `/customer/dashboard` - Should work for all authenticated users

### 4. Test Middleware Protection

Try accessing protected routes while logged out - you should be redirected to login.

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Installed and configured Laravel Breeze
✅ Added user roles (admin, staff, customer)
✅ Created role-based middleware for access control
✅ Customized authentication views for car wash theme
✅ Built role-specific dashboards
✅ Created user seeders for testing
✅ Implemented complete authentication flow
✅ Tested role-based access control

### Authentication Features Implemented:
- **User Registration**: With role selection
- **User Login/Logout**: With role-based redirection
- **Password Reset**: Email-based password recovery
- **Role-Based Access**: Admin, Staff, and Customer roles
- **Dashboard Routing**: Different dashboards for each role
- **Middleware Protection**: Secure route access based on roles

## 🚀 What's Next?

In the next chapter, we'll:
- Create the Customer model and relationships
- Build a complete customer management system
- Implement CRUD operations for customers
- Add form validation and error handling
- Create customer search and filtering functionality

## 💡 Pro Tips

1. **Always hash passwords** - Never store plain text passwords
2. **Use middleware for protection** - Protect routes based on user roles
3. **Validate user input** - Always validate form data on the server side
4. **Test different scenarios** - Test with different user roles and edge cases
5. **Keep authentication simple** - Don't over-complicate the authentication flow

## 🔧 Troubleshooting

### Common Issues and Solutions

**Issue: "Route not found" errors**
- Solution: Make sure you've cleared route cache with `php artisan route:clear`

**Issue: Middleware not working**
- Solution: Check that middleware is properly registered in `bootstrap/app.php`

**Issue: Users can't access their dashboard**
- Solution: Verify the user's role and that they're properly authenticated

**Issue: Registration form not showing role field**
- Solution: Clear view cache with `php artisan view:clear`

---

**Ready to manage customers?** Let's move on to [Chapter 4: Customer Management](./04-customer-management.md) where we'll build a complete customer management system!
