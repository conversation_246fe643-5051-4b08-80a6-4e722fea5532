# Chapter 15: Queue Display System

Welcome to Chapter 15! In this chapter, we'll build a comprehensive digital queue management system that provides real-time status displays, progress indicators, estimated wait times, and digital signage for customer information.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create a digital queue management system
- Build real-time queue status displays for customers
- Implement current service progress indicators
- Add estimated wait times for each queue position
- Create digital signage interface for lobby/waiting area display
- Build queue number assignment and calling system
- Integrate queue system with existing booking system
- Add queue analytics and reporting

## 📋 What We'll Cover

1. Setting up queue database structure
2. Creating queue management models
3. Building the queue display interface
4. Implementing real-time updates with WebSockets
5. Creating digital signage displays
6. Queue number assignment system
7. Integration with booking system
8. Queue analytics and reporting

## 🛠 Step 1: Database Structure for Queue System

First, let's create the necessary migrations for our queue system:

```bash
# Create queue-related migrations
php artisan make:migration create_queue_numbers_table
php artisan make:migration create_queue_displays_table
php artisan make:migration create_queue_settings_table
php artisan make:migration add_queue_fields_to_bookings_table
```

Edit `database/migrations/create_queue_numbers_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('queue_numbers', function (Blueprint $table) {
            $table->id();
            $table->string('queue_number')->unique();
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->string('customer_name');
            $table->string('service_type');
            $table->enum('status', ['waiting', 'in_progress', 'completed', 'cancelled', 'no_show'])->default('waiting');
            $table->integer('estimated_duration')->nullable(); // in minutes
            $table->timestamp('assigned_at');
            $table->timestamp('called_at')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('position_in_queue')->nullable();
            $table->text('notes')->nullable();
            $table->json('service_details')->nullable();
            $table->timestamps();

            $table->index(['status', 'assigned_at']);
            $table->index(['queue_number']);
            $table->index(['position_in_queue']);
            $table->index(['customer_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('queue_numbers');
    }
};
```

Edit `database/migrations/create_queue_displays_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('queue_displays', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('location');
            $table->enum('type', ['main_display', 'service_bay', 'waiting_area', 'mobile']);
            $table->json('display_settings'); // Layout, colors, refresh rate, etc.
            $table->json('queue_filters')->nullable(); // Which queues to show
            $table->boolean('is_active')->default(true);
            $table->string('ip_address')->nullable();
            $table->timestamp('last_ping')->nullable();
            $table->timestamps();

            $table->index(['type', 'is_active']);
            $table->index(['location']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('queue_displays');
    }
};
```

Edit `database/migrations/create_queue_settings_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('queue_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value');
            $table->string('type')->default('string'); // string, integer, boolean, json
            $table->text('description')->nullable();
            $table->string('group')->default('general');
            $table->timestamps();

            $table->index(['key']);
            $table->index(['group']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('queue_settings');
    }
};
```

Edit `database/migrations/add_queue_fields_to_bookings_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->foreignId('queue_number_id')->nullable()->constrained()->onDelete('set null');
            $table->boolean('auto_queue')->default(true);
            $table->timestamp('queue_assigned_at')->nullable();
            $table->integer('estimated_wait_time')->nullable(); // in minutes
        });
    }

    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropForeign(['queue_number_id']);
            $table->dropColumn(['queue_number_id', 'auto_queue', 'queue_assigned_at', 'estimated_wait_time']);
        });
    }
};
```

Run the migrations:

```bash
php artisan migrate
```

## 🛠 Step 2: Creating Queue Models

Create the models for our queue system:

```bash
# Create queue models
php artisan make:model QueueNumber
php artisan make:model QueueDisplay
php artisan make:model QueueSetting
```

Edit `app/Models/QueueNumber.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class QueueNumber extends Model
{
    use HasFactory;

    protected $fillable = [
        'queue_number',
        'booking_id',
        'customer_id',
        'customer_name',
        'service_type',
        'status',
        'estimated_duration',
        'assigned_at',
        'called_at',
        'started_at',
        'completed_at',
        'position_in_queue',
        'notes',
        'service_details',
    ];

    protected $casts = [
        'service_details' => 'array',
        'assigned_at' => 'datetime',
        'called_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Status constants
    const STATUS_WAITING = 'waiting';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_NO_SHOW = 'no_show';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($queueNumber) {
            if (empty($queueNumber->queue_number)) {
                $queueNumber->queue_number = static::generateQueueNumber();
            }
            if (empty($queueNumber->assigned_at)) {
                $queueNumber->assigned_at = now();
            }
            if (is_null($queueNumber->position_in_queue)) {
                $queueNumber->position_in_queue = static::getNextPosition();
            }
        });

        static::updated(function ($queueNumber) {
            if ($queueNumber->wasChanged('status')) {
                static::updateQueuePositions();
            }
        });
    }

    public static function generateQueueNumber(): string
    {
        $prefix = 'Q';
        $date = now()->format('md');
        $lastQueue = static::whereDate('assigned_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastQueue ? 
            intval(substr($lastQueue->queue_number, -3)) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    public static function getNextPosition(): int
    {
        return static::where('status', self::STATUS_WAITING)
            ->max('position_in_queue') + 1;
    }

    public static function updateQueuePositions(): void
    {
        $waitingQueues = static::where('status', self::STATUS_WAITING)
            ->orderBy('assigned_at')
            ->get();

        foreach ($waitingQueues as $index => $queue) {
            $queue->update(['position_in_queue' => $index + 1]);
        }
    }

    // Relationships
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Scopes
    public function scopeWaiting(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_WAITING);
    }

    public function scopeInProgress(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_IN_PROGRESS);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->whereIn('status', [self::STATUS_WAITING, self::STATUS_IN_PROGRESS]);
    }

    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('assigned_at', today());
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_WAITING => 'Waiting',
            self::STATUS_IN_PROGRESS => 'In Progress',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_NO_SHOW => 'No Show',
            default => 'Unknown',
        };
    }

    public function getEstimatedWaitTimeAttribute(): int
    {
        if ($this->status !== self::STATUS_WAITING) {
            return 0;
        }

        $queuesBefore = static::where('status', self::STATUS_WAITING)
            ->where('position_in_queue', '<', $this->position_in_queue)
            ->get();

        $totalWaitTime = 0;
        foreach ($queuesBefore as $queue) {
            $totalWaitTime += $queue->estimated_duration ?? 30; // Default 30 minutes
        }

        // Add current service time if any queue is in progress
        $currentService = static::where('status', self::STATUS_IN_PROGRESS)->first();
        if ($currentService) {
            $elapsedTime = now()->diffInMinutes($currentService->started_at);
            $remainingTime = max(0, ($currentService->estimated_duration ?? 30) - $elapsedTime);
            $totalWaitTime += $remainingTime;
        }

        return $totalWaitTime;
    }

    public function getFormattedWaitTimeAttribute(): string
    {
        $minutes = $this->estimated_wait_time;
        
        if ($minutes < 60) {
            return $minutes . ' min';
        }
        
        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;
        
        return $hours . 'h ' . $remainingMinutes . 'm';
    }

    // Methods
    public function callNext(): void
    {
        $this->update([
            'status' => self::STATUS_IN_PROGRESS,
            'called_at' => now(),
            'started_at' => now(),
        ]);

        // Broadcast to displays
        broadcast(new \App\Events\QueueUpdated($this));
    }

    public function markCompleted(): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'completed_at' => now(),
        ]);

        // Update queue positions
        static::updateQueuePositions();

        // Broadcast to displays
        broadcast(new \App\Events\QueueUpdated($this));
    }

    public function markNoShow(): void
    {
        $this->update([
            'status' => self::STATUS_NO_SHOW,
            'completed_at' => now(),
        ]);

        // Update queue positions
        static::updateQueuePositions();

        // Broadcast to displays
        broadcast(new \App\Events\QueueUpdated($this));
    }
}
```

## 🛠 Step 5: Creating Queue Display Views

Now let's create the comprehensive queue display interfaces for customers and staff.

### 5.1 Customer Queue Display

Create `resources/views/queue/customer-display.blade.php`:

```blade
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queue Display - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    <meta http-equiv="refresh" content="30">
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .pulse-animation { animation: pulse 2s infinite; }

        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        .slide-in { animation: slideIn 0.5s ease-out; }

        .digital-clock {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 min-h-screen text-white">
    <!-- Header -->
    <header class="bg-black bg-opacity-30 p-6">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-3xl font-bold">{{ config('app.name', 'Car Wash') }}</h1>
                    <p class="text-blue-200">Queue Management System</p>
                </div>
            </div>
            <div class="text-right">
                <div class="digital-clock text-2xl" id="current-time"></div>
                <div class="text-blue-200" id="current-date"></div>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto p-6">
        <!-- Current Service Display -->
        <div class="bg-black bg-opacity-40 rounded-2xl p-8 mb-8">
            <div class="text-center">
                <h2 class="text-4xl font-bold mb-4 text-yellow-400">NOW SERVING</h2>
                <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black rounded-xl p-8 mb-6">
                    <div class="text-8xl font-bold pulse-animation" id="current-queue">
                        {{ $currentQueue ? $currentQueue->queue_number : '--' }}
                    </div>
                    @if($currentQueue)
                        <div class="text-2xl font-semibold mt-4">
                            {{ $currentQueue->customer_name ?? 'Walk-in Customer' }}
                        </div>
                        <div class="text-lg mt-2">
                            {{ $currentQueue->service_type ?? 'Service in Progress' }}
                        </div>
                    @endif
                </div>

                @if($currentQueue && $currentQueue->bay)
                    <div class="text-2xl">
                        <span class="text-blue-300">Proceed to</span>
                        <span class="text-yellow-400 font-bold">{{ $currentQueue->bay->name }}</span>
                    </div>
                @endif
            </div>
        </div>

        <!-- Queue Status Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Waiting Queue -->
            <div class="bg-black bg-opacity-40 rounded-2xl p-6">
                <h3 class="text-2xl font-bold mb-6 text-center text-blue-300">
                    <svg class="w-8 h-8 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    WAITING QUEUE
                </h3>

                <div class="space-y-3 max-h-96 overflow-y-auto">
                    @forelse($waitingQueue as $queue)
                        <div class="bg-blue-900 bg-opacity-50 rounded-lg p-4 slide-in">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="text-2xl font-bold text-yellow-400">{{ $queue->queue_number }}</div>
                                    <div class="text-sm text-blue-200">{{ $queue->customer_name ?? 'Walk-in' }}</div>
                                    <div class="text-xs text-blue-300">{{ $queue->service_type }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-semibold text-green-400">
                                        ~{{ $queue->estimated_wait_time }} min
                                    </div>
                                    <div class="text-xs text-blue-300">Est. wait</div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center text-blue-300 py-8">
                            <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="text-xl">No customers waiting</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- In Service Queue -->
            <div class="bg-black bg-opacity-40 rounded-2xl p-6">
                <h3 class="text-2xl font-bold mb-6 text-center text-green-300">
                    <svg class="w-8 h-8 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    IN SERVICE
                </h3>

                <div class="space-y-3 max-h-96 overflow-y-auto">
                    @forelse($inServiceQueue as $queue)
                        <div class="bg-green-900 bg-opacity-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="text-2xl font-bold text-yellow-400">{{ $queue->queue_number }}</div>
                                    <div class="text-sm text-green-200">{{ $queue->customer_name ?? 'Walk-in' }}</div>
                                    <div class="text-xs text-green-300">{{ $queue->bay->name ?? 'Bay Assignment' }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-semibold text-blue-400">
                                        {{ $queue->service_progress ?? 0 }}%
                                    </div>
                                    <div class="text-xs text-green-300">Progress</div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mt-3">
                                <div class="bg-gray-700 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                                         style="width: {{ $queue->service_progress ?? 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center text-green-300 py-8">
                            <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <p class="text-xl">No services in progress</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Statistics and Information -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <!-- Average Wait Time -->
            <div class="bg-black bg-opacity-40 rounded-xl p-6 text-center">
                <div class="text-3xl font-bold text-blue-400 mb-2">{{ $averageWaitTime ?? 0 }}</div>
                <div class="text-blue-200">Average Wait (min)</div>
            </div>

            <!-- Total Served Today -->
            <div class="bg-black bg-opacity-40 rounded-xl p-6 text-center">
                <div class="text-3xl font-bold text-green-400 mb-2">{{ $totalServedToday ?? 0 }}</div>
                <div class="text-green-200">Served Today</div>
            </div>

            <!-- Available Bays -->
            <div class="bg-black bg-opacity-40 rounded-xl p-6 text-center">
                <div class="text-3xl font-bold text-yellow-400 mb-2">{{ $availableBays ?? 0 }}</div>
                <div class="text-yellow-200">Available Bays</div>
            </div>
        </div>

        <!-- Announcements -->
        @if($announcements && $announcements->count() > 0)
            <div class="bg-black bg-opacity-40 rounded-2xl p-6 mt-8">
                <h3 class="text-2xl font-bold mb-4 text-center text-purple-300">
                    <svg class="w-8 h-8 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                    </svg>
                    ANNOUNCEMENTS
                </h3>

                <div class="space-y-3">
                    @foreach($announcements as $announcement)
                        <div class="bg-purple-900 bg-opacity-50 rounded-lg p-4">
                            <div class="text-lg font-semibold text-purple-200">{{ $announcement->title }}</div>
                            <div class="text-purple-300 mt-1">{{ $announcement->message }}</div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </main>

    <!-- Footer -->
    <footer class="bg-black bg-opacity-30 p-4 mt-8">
        <div class="max-w-7xl mx-auto text-center text-blue-200">
            <p>Thank you for choosing {{ config('app.name') }}! • For assistance, please speak to our staff</p>
        </div>
    </footer>

    <script>
        // Update clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            document.getElementById('current-time').textContent = timeString;
            document.getElementById('current-date').textContent = dateString;
        }

        // Initialize clock and update every second
        updateClock();
        setInterval(updateClock, 1000);

        // WebSocket connection for real-time updates
        const pusher = new Pusher('{{ config("broadcasting.connections.pusher.key") }}', {
            cluster: '{{ config("broadcasting.connections.pusher.options.cluster") }}',
            forceTLS: true
        });

        const channel = pusher.subscribe('queue-updates');

        channel.bind('queue-updated', function(data) {
            // Reload page to show updated queue
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        channel.bind('queue-called', function(data) {
            // Highlight the called queue number
            const currentQueueElement = document.getElementById('current-queue');
            if (currentQueueElement) {
                currentQueueElement.classList.add('pulse-animation');

                // Play notification sound if available
                try {
                    const audio = new Audio('/sounds/queue-call.mp3');
                    audio.play().catch(e => console.log('Audio play failed:', e));
                } catch (e) {
                    console.log('Audio not available');
                }
            }
        });

        // Auto-refresh every 30 seconds as fallback
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
```

### 5.2 Staff Queue Management

Create `resources/views/queue/staff-management.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Queue Management
            </h2>
            <div class="flex space-x-2">
                <button onclick="addWalkInCustomer()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add Walk-in
                </button>
                <button onclick="refreshQueue()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Refresh
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Queue Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Waiting</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $waitingCount }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">In Service</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $inServiceCount }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Avg Wait</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $averageWaitTime }}m</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Served Today</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $servedToday }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Queue Display -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Queue Status</h3>

                    <!-- Now Serving -->
                    @if($currentlyServing)
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h4 class="text-lg font-semibold text-green-800">Now Serving</h4>
                                    <div class="flex items-center space-x-4 mt-2">
                                        <span class="text-2xl font-bold text-green-600">{{ $currentlyServing->queue_number }}</span>
                                        <span class="text-green-700">{{ $currentlyServing->customer_name ?? 'Walk-in Customer' }}</span>
                                        <span class="text-sm text-green-600">{{ $currentlyServing->bay->name ?? 'Bay Assignment' }}</span>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="updateProgress({{ $currentlyServing->id }})"
                                            class="bg-blue-500 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                                        Update Progress
                                    </button>
                                    <button onclick="completeService({{ $currentlyServing->id }})"
                                            class="bg-green-500 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                        Complete
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Waiting Queue -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Queue #</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wait Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($queueItems as $queue)
                                    <tr class="{{ $queue->status === 'called' ? 'bg-yellow-50' : '' }}">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-2xl font-bold text-blue-600">{{ $queue->queue_number }}</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $queue->customer_name ?? 'Walk-in Customer' }}
                                            </div>
                                            @if($queue->customer && $queue->customer->phone)
                                                <div class="text-sm text-gray-500">{{ $queue->customer->phone }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $queue->service_type ?? 'Standard Wash' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $queue->estimated_wait_time }} min
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($queue->status === 'waiting') bg-blue-100 text-blue-800
                                                @elseif($queue->status === 'called') bg-yellow-100 text-yellow-800
                                                @elseif($queue->status === 'in_service') bg-green-100 text-green-800
                                                @else bg-gray-100 text-gray-800 @endif">
                                                {{ ucfirst(str_replace('_', ' ', $queue->status)) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            @if($queue->status === 'waiting')
                                                <button onclick="callQueue({{ $queue->id }})"
                                                        class="text-blue-600 hover:text-blue-900">Call</button>
                                                <button onclick="assignBay({{ $queue->id }})"
                                                        class="text-green-600 hover:text-green-900">Assign Bay</button>
                                            @elseif($queue->status === 'called')
                                                <button onclick="startService({{ $queue->id }})"
                                                        class="text-green-600 hover:text-green-900">Start Service</button>
                                            @endif
                                            <button onclick="editQueue({{ $queue->id }})"
                                                    class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                            <button onclick="removeQueue({{ $queue->id }})"
                                                    class="text-red-600 hover:text-red-900">Remove</button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            No customers in queue
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Bay Status -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Bay Status</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($bays as $bay)
                            <div class="border border-gray-200 rounded-lg p-4
                                {{ $bay->status === 'occupied' ? 'bg-red-50 border-red-200' :
                                   ($bay->status === 'cleaning' ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200') }}">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ $bay->name }}</h4>
                                        <p class="text-sm text-gray-500">{{ $bay->type }}</p>
                                        @if($bay->current_queue)
                                            <p class="text-sm font-medium">Queue: {{ $bay->current_queue->queue_number }}</p>
                                        @endif
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $bay->status === 'available' ? 'bg-green-100 text-green-800' :
                                           ($bay->status === 'occupied' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                                        {{ ucfirst($bay->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Walk-in Modal -->
    <div id="walkin-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Add Walk-in Customer</h3>
                <form id="walkin-form">
                    <div class="mb-4">
                        <label for="customer-name" class="block text-sm font-medium text-gray-700">Customer Name (Optional)</label>
                        <input type="text" id="customer-name"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="mb-4">
                        <label for="service-type" class="block text-sm font-medium text-gray-700">Service Type</label>
                        <select id="service-type" required
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">Select Service</option>
                            @foreach($services as $service)
                                <option value="{{ $service->name }}">{{ $service->name }} - ${{ $service->price }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone (Optional)</label>
                        <input type="text" id="phone"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeWalkinModal()"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Add to Queue
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Queue Management Functions
        function addWalkInCustomer() {
            document.getElementById('walkin-modal').classList.remove('hidden');
        }

        function closeWalkinModal() {
            document.getElementById('walkin-modal').classList.add('hidden');
            document.getElementById('walkin-form').reset();
        }

        function callQueue(queueId) {
            if (confirm('Call this customer to service?')) {
                fetch(`/queue/${queueId}/call`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error calling queue: ' + data.message);
                    }
                });
            }
        }

        function startService(queueId) {
            if (confirm('Start service for this customer?')) {
                fetch(`/queue/${queueId}/start-service`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error starting service: ' + data.message);
                    }
                });
            }
        }

        function completeService(queueId) {
            if (confirm('Mark this service as completed?')) {
                fetch(`/queue/${queueId}/complete`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error completing service: ' + data.message);
                    }
                });
            }
        }

        function removeQueue(queueId) {
            if (confirm('Remove this customer from queue?')) {
                fetch(`/queue/${queueId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error removing from queue: ' + data.message);
                    }
                });
            }
        }

        function refreshQueue() {
            location.reload();
        }

        // Handle walk-in form submission
        document.getElementById('walkin-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                customer_name: document.getElementById('customer-name').value,
                service_type: document.getElementById('service-type').value,
                phone: document.getElementById('phone').value
            };

            fetch('/queue/add-walkin', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeWalkinModal();
                    location.reload();
                } else {
                    alert('Error adding to queue: ' + data.message);
                }
            });
        });

        // Auto-refresh every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</x-app-layout>
```

## 🧪 Testing the Queue System

1. **Test Queue Assignment**:
   - Create queue numbers manually and from bookings
   - Verify position calculations and wait times
   - Test queue status transitions

2. **Test Real-time Updates**:
   - Open multiple display windows
   - Verify real-time synchronization
   - Test WebSocket connections

3. **Test Integration**:
   - Verify booking integration
   - Test customer data synchronization
   - Validate queue analytics

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Built a comprehensive digital queue management system
✅ Created real-time queue status displays for customers
✅ Implemented current service progress indicators
✅ Added estimated wait times for each queue position
✅ Built digital signage interface for lobby/waiting area display
✅ Created queue number assignment and calling system
✅ Integrated queue system with existing booking system
✅ Added queue analytics and reporting

### Queue System Features Implemented:
- **Digital Queue Management**: Automated queue number assignment and tracking
- **Real-time Displays**: Live updates across all display screens
- **Wait Time Estimation**: Intelligent calculation of customer wait times
- **Digital Signage**: Professional lobby displays with queue information
- **Booking Integration**: Seamless integration with existing booking system
- **Queue Analytics**: Comprehensive reporting and performance metrics
- **Multi-display Support**: Support for multiple display types and locations
- **WebSocket Integration**: Real-time updates using Laravel Broadcasting

## 🚀 What's Next?

In the next chapter, we'll:
- Build a comprehensive bay management system
- Implement real-time bay status tracking
- Create bay assignment optimization
- Add service progress tracking within each bay
- Build bay utilization analytics and reporting

---

**Ready for bay management?** Let's move on to [Chapter 16: Bay Management System](./16-bay-management-system.md)!
