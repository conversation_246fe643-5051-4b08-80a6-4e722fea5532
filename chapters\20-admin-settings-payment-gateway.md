# Chapter 20: Admin Settings - Payment Gateway Configuration

Welcome to Chapter 20! In this chapter, we'll create a comprehensive admin settings interface that allows administrators to configure payment gateways, manage system settings, and control business operations from a centralized dashboard.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create a unified admin settings interface
- Implement payment gateway selection and configuration
- Build system-wide configuration management
- Add business settings and preferences
- Create regional payment gateway auto-selection
- Implement settings validation and security
- Build settings backup and restore functionality

## 📋 What We'll Cover

1. Database structure for settings management
2. Admin settings controller and middleware
3. Payment gateway configuration interface
4. System settings management
5. Business configuration panel
6. Regional settings and localization
7. Settings validation and security
8. Backup and restore functionality

## 🛠 Step 1: Database Structure for Settings

### 1.1 Create Settings Migration

```bash
php artisan make:migration create_admin_settings_table
```

Edit the migration:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('admin_settings', function (Blueprint $table) {
            $table->id();
            $table->string('category'); // payment, business, system, regional
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, boolean, integer, json, encrypted
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false); // Can be accessed by non-admin users
            $table->boolean('requires_restart')->default(false); // Requires app restart
            $table->json('validation_rules')->nullable();
            $table->timestamps();
            
            $table->index(['category', 'key']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('admin_settings');
    }
};
```

### 1.2 Create Payment Gateway Settings Migration

```bash
php artisan make:migration create_payment_gateway_settings_table
```

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_gateway_settings', function (Blueprint $table) {
            $table->id();
            $table->string('gateway_name'); // stripe, midtrans
            $table->boolean('is_enabled')->default(false);
            $table->boolean('is_default')->default(false);
            $table->json('configuration'); // Gateway-specific config
            $table->json('supported_countries')->nullable();
            $table->json('supported_currencies')->nullable();
            $table->string('environment')->default('sandbox'); // sandbox, production
            $table->integer('priority')->default(0); // Display order
            $table->timestamps();
            
            $table->unique(['gateway_name', 'environment']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_gateway_settings');
    }
};
```

## 🛠 Step 2: Models and Services

### 2.1 AdminSetting Model

```bash
php artisan make:model AdminSetting
```

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class AdminSetting extends Model
{
    protected $fillable = [
        'category',
        'key',
        'value',
        'type',
        'description',
        'is_public',
        'requires_restart',
        'validation_rules'
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'requires_restart' => 'boolean',
        'validation_rules' => 'array'
    ];

    // Accessor for value based on type
    public function getValueAttribute($value)
    {
        if (is_null($value)) {
            return null;
        }

        return match($this->type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'json' => json_decode($value, true),
            'encrypted' => Crypt::decrypt($value),
            default => $value
        };
    }

    // Mutator for value based on type
    public function setValueAttribute($value)
    {
        $this->attributes['value'] = match($this->type) {
            'boolean' => $value ? '1' : '0',
            'integer' => (string) $value,
            'json' => json_encode($value),
            'encrypted' => Crypt::encrypt($value),
            default => $value
        };
    }

    // Get setting value with caching
    public static function get($key, $default = null)
    {
        return Cache::remember("admin_setting_{$key}", 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    // Set setting value and clear cache
    public static function set($key, $value, $type = 'string')
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            ['value' => $value, 'type' => $type]
        );
        
        Cache::forget("admin_setting_{$key}");
        return $setting;
    }

    // Get settings by category
    public static function getByCategory($category)
    {
        return Cache::remember("admin_settings_{$category}", 3600, function () use ($category) {
            return static::where('category', $category)->get()->pluck('value', 'key');
        });
    }
}
```

### 2.2 PaymentGatewaySetting Model

```bash
php artisan make:model PaymentGatewaySetting
```

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PaymentGatewaySetting extends Model
{
    protected $fillable = [
        'gateway_name',
        'is_enabled',
        'is_default',
        'configuration',
        'supported_countries',
        'supported_currencies',
        'environment',
        'priority'
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'is_default' => 'boolean',
        'configuration' => 'array',
        'supported_countries' => 'array',
        'supported_currencies' => 'array'
    ];

    // Get enabled gateways
    public static function getEnabled()
    {
        return static::where('is_enabled', true)
                    ->orderBy('priority')
                    ->get();
    }

    // Get default gateway
    public static function getDefault()
    {
        return static::where('is_default', true)
                    ->where('is_enabled', true)
                    ->first();
    }

    // Get gateway for specific country
    public static function getForCountry($countryCode)
    {
        return static::where('is_enabled', true)
                    ->where(function ($query) use ($countryCode) {
                        $query->whereJsonContains('supported_countries', $countryCode)
                              ->orWhereNull('supported_countries');
                    })
                    ->orderBy('priority')
                    ->get();
    }
}
```

## 🛠 Step 3: Payment Gateway Service

### 3.1 Create Payment Gateway Service

```bash
php artisan make:service PaymentGatewayService
```

Create `app/Services/PaymentGatewayService.php`:

```php
<?php

namespace App\Services;

use App\Models\PaymentGatewaySetting;
use App\Services\Payment\StripePaymentService;
use App\Services\Payment\MidtransPaymentService;
use Illuminate\Support\Facades\Log;

class PaymentGatewayService
{
    protected $gateways = [
        'stripe' => StripePaymentService::class,
        'midtrans' => MidtransPaymentService::class,
    ];

    public function getAvailableGateways($countryCode = null)
    {
        if ($countryCode) {
            return PaymentGatewaySetting::getForCountry($countryCode);
        }

        return PaymentGatewaySetting::getEnabled();
    }

    public function getDefaultGateway()
    {
        return PaymentGatewaySetting::getDefault();
    }

    public function getGatewayService($gatewayName)
    {
        $setting = PaymentGatewaySetting::where('gateway_name', $gatewayName)
                                      ->where('is_enabled', true)
                                      ->first();

        if (!$setting) {
            throw new \Exception("Payment gateway '{$gatewayName}' is not enabled");
        }

        $serviceClass = $this->gateways[$gatewayName] ?? null;

        if (!$serviceClass) {
            throw new \Exception("Payment gateway service for '{$gatewayName}' not found");
        }

        return new $serviceClass($setting->configuration);
    }

    public function processPayment($gatewayName, $paymentData)
    {
        try {
            $service = $this->getGatewayService($gatewayName);
            return $service->createPayment($paymentData);
        } catch (\Exception $e) {
            Log::error("Payment processing failed", [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
                'data' => $paymentData
            ]);
            throw $e;
        }
    }

    public function selectBestGateway($customerCountry, $currency = 'USD')
    {
        $gateways = $this->getAvailableGateways($customerCountry);

        // Priority logic
        foreach ($gateways as $gateway) {
            if (in_array($currency, $gateway->supported_currencies ?? [])) {
                return $gateway;
            }
        }

        // Fallback to default
        return $this->getDefaultGateway();
    }
}
```

### 3.2 Create Payment Interface

Create `app/Services/Payment/PaymentInterface.php`:

```php
<?php

namespace App\Services\Payment;

interface PaymentInterface
{
    public function createPayment(array $paymentData);
    public function processWebhook(array $payload);
    public function refundPayment(string $paymentId, float $amount);
    public function getPaymentStatus(string $paymentId);
}
```

### 3.3 Update Stripe Service

Create `app/Services/Payment/StripePaymentService.php`:

```php
<?php

namespace App\Services\Payment;

use Stripe\StripeClient;

class StripePaymentService implements PaymentInterface
{
    protected $stripe;
    protected $config;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->stripe = new StripeClient($config['secret_key']);
    }

    public function createPayment(array $paymentData)
    {
        return $this->stripe->paymentIntents->create([
            'amount' => $paymentData['amount'] * 100, // Convert to cents
            'currency' => $paymentData['currency'],
            'metadata' => $paymentData['metadata'] ?? [],
        ]);
    }

    public function processWebhook(array $payload)
    {
        // Stripe webhook processing logic
        return $payload;
    }

    public function refundPayment(string $paymentId, float $amount)
    {
        return $this->stripe->refunds->create([
            'payment_intent' => $paymentId,
            'amount' => $amount * 100,
        ]);
    }

    public function getPaymentStatus(string $paymentId)
    {
        return $this->stripe->paymentIntents->retrieve($paymentId);
    }
}
```

### 3.4 Update Midtrans Service

Create `app/Services/Payment/MidtransPaymentService.php`:

```php
<?php

namespace App\Services\Payment;

use Midtrans\Config;
use Midtrans\Snap;

class MidtransPaymentService implements PaymentInterface
{
    protected $config;

    public function __construct(array $config)
    {
        $this->config = $config;
        Config::$serverKey = $config['server_key'];
        Config::$isProduction = $config['is_production'] ?? false;
        Config::$isSanitized = $config['is_sanitized'] ?? true;
        Config::$is3ds = $config['is_3ds'] ?? true;
    }

    public function createPayment(array $paymentData)
    {
        $params = [
            'transaction_details' => [
                'order_id' => $paymentData['order_id'],
                'gross_amount' => $paymentData['amount'],
            ],
            'customer_details' => $paymentData['customer'] ?? [],
            'item_details' => $paymentData['items'] ?? [],
        ];

        return Snap::createTransaction($params);
    }

    public function processWebhook(array $payload)
    {
        // Midtrans webhook processing logic
        return $payload;
    }

    public function refundPayment(string $paymentId, float $amount)
    {
        // Midtrans refund logic
        return ['status' => 'refund_requested'];
    }

    public function getPaymentStatus(string $paymentId)
    {
        return \Midtrans\Transaction::status($paymentId);
    }
}
```

## 🛠 Step 4: Admin Settings Controller

### 4.1 Create Admin Settings Controller

```bash
php artisan make:controller Admin/SettingsController
```

```php
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminSetting;
use App\Models\PaymentGatewaySetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    public function index()
    {
        $settings = AdminSetting::all()->groupBy('category');
        $paymentGateways = PaymentGatewaySetting::orderBy('priority')->get();

        return view('admin.settings.index', compact('settings', 'paymentGateways'));
    }

    public function paymentGateways()
    {
        $gateways = PaymentGatewaySetting::orderBy('priority')->get();
        return view('admin.settings.payment-gateways', compact('gateways'));
    }

    public function updatePaymentGateway(Request $request, $id)
    {
        $gateway = PaymentGatewaySetting::findOrFail($id);

        $validated = $request->validate([
            'is_enabled' => 'boolean',
            'is_default' => 'boolean',
            'configuration' => 'array',
            'supported_countries' => 'array',
            'supported_currencies' => 'array',
            'environment' => 'in:sandbox,production',
            'priority' => 'integer|min:0'
        ]);

        // Ensure only one default gateway
        if ($validated['is_default'] ?? false) {
            PaymentGatewaySetting::where('id', '!=', $id)
                                ->update(['is_default' => false]);
        }

        $gateway->update($validated);

        return redirect()->back()->with('success', 'Payment gateway updated successfully');
    }

    public function systemSettings()
    {
        $settings = AdminSetting::where('category', 'system')->get();
        return view('admin.settings.system', compact('settings'));
    }

    public function updateSystemSettings(Request $request)
    {
        foreach ($request->except('_token', '_method') as $key => $value) {
            AdminSetting::set($key, $value);
        }

        Cache::flush(); // Clear all cache

        return redirect()->back()->with('success', 'System settings updated successfully');
    }

    public function businessSettings()
    {
        $settings = AdminSetting::where('category', 'business')->get();
        return view('admin.settings.business', compact('settings'));
    }

    public function updateBusinessSettings(Request $request)
    {
        $validated = $request->validate([
            'business_name' => 'required|string|max:255',
            'business_address' => 'required|string',
            'business_phone' => 'required|string|max:20',
            'business_email' => 'required|email',
            'tax_rate' => 'numeric|min:0|max:100',
            'currency' => 'required|string|size:3',
            'timezone' => 'required|string',
        ]);

        foreach ($validated as $key => $value) {
            AdminSetting::set($key, $value, 'string');
        }

        return redirect()->back()->with('success', 'Business settings updated successfully');
    }
}
```

## 🛠 Step 5: Admin Settings Views

### 5.1 Main Settings Dashboard

Create `resources/views/admin/settings/index.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Admin Settings
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Settings Navigation -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <a href="{{ route('admin.settings.payment-gateways') }}"
                           class="bg-blue-50 hover:bg-blue-100 p-6 rounded-lg border border-blue-200 transition-colors">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Payment Gateways</h3>
                                    <p class="text-sm text-gray-600">Configure Stripe & Midtrans</p>
                                </div>
                            </div>
                        </a>

                        <a href="{{ route('admin.settings.business') }}"
                           class="bg-green-50 hover:bg-green-100 p-6 rounded-lg border border-green-200 transition-colors">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Business Settings</h3>
                                    <p class="text-sm text-gray-600">Company info & preferences</p>
                                </div>
                            </div>
                        </a>

                        <a href="{{ route('admin.settings.system') }}"
                           class="bg-purple-50 hover:bg-purple-100 p-6 rounded-lg border border-purple-200 transition-colors">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">System Settings</h3>
                                    <p class="text-sm text-gray-600">App configuration</p>
                                </div>
                            </div>
                        </a>

                        <a href="{{ route('admin.settings.regional') }}"
                           class="bg-orange-50 hover:bg-orange-100 p-6 rounded-lg border border-orange-200 transition-colors">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Regional Settings</h3>
                                    <p class="text-sm text-gray-600">Localization & regions</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Active Payment Gateways</div>
                                <div class="text-2xl font-bold text-gray-900">{{ $paymentGateways->where('is_enabled', true)->count() }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">System Status</div>
                                <div class="text-2xl font-bold text-green-600">Healthy</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Settings</div>
                                <div class="text-2xl font-bold text-gray-900">{{ $settings->flatten()->count() }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

### 5.2 Payment Gateway Settings View

Create `resources/views/admin/settings/payment-gateways.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Payment Gateway Settings
            </h2>
            <a href="{{ route('admin.settings.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Settings
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Payment Gateway Configuration -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Configure Payment Gateways</h3>

                    <div class="space-y-6">
                        @foreach($gateways as $gateway)
                            <div class="border border-gray-200 rounded-lg p-6">
                                <form method="POST" action="{{ route('admin.settings.payment-gateways.update', $gateway->id) }}">
                                    @csrf
                                    @method('PUT')

                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center space-x-3">
                                            @if($gateway->gateway_name === 'stripe')
                                                <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                                                    <span class="text-white font-bold text-lg">S</span>
                                                </div>
                                            @elseif($gateway->gateway_name === 'midtrans')
                                                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                                                    <span class="text-white font-bold text-lg">M</span>
                                                </div>
                                            @endif

                                            <div>
                                                <h4 class="text-lg font-medium text-gray-900 capitalize">
                                                    {{ $gateway->gateway_name }}
                                                </h4>
                                                <p class="text-sm text-gray-600">
                                                    Environment: {{ ucfirst($gateway->environment) }}
                                                </p>
                                            </div>
                                        </div>

                                        <div class="flex items-center space-x-4">
                                            <!-- Default Gateway Toggle -->
                                            <label class="flex items-center">
                                                <input type="checkbox" name="is_default" value="1"
                                                       {{ $gateway->is_default ? 'checked' : '' }}
                                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                <span class="ml-2 text-sm text-gray-600">Default</span>
                                            </label>

                                            <!-- Enable/Disable Toggle -->
                                            <label class="flex items-center">
                                                <input type="checkbox" name="is_enabled" value="1"
                                                       {{ $gateway->is_enabled ? 'checked' : '' }}
                                                       class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                                <span class="ml-2 text-sm text-gray-600">Enabled</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Configuration Fields -->
                                        <div>
                                            <h5 class="font-medium text-gray-900 mb-3">Configuration</h5>

                                            @if($gateway->gateway_name === 'stripe')
                                                <div class="space-y-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700">Publishable Key</label>
                                                        <input type="text" name="configuration[publishable_key]"
                                                               value="{{ $gateway->configuration['publishable_key'] ?? '' }}"
                                                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700">Secret Key</label>
                                                        <input type="password" name="configuration[secret_key]"
                                                               value="{{ $gateway->configuration['secret_key'] ?? '' }}"
                                                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700">Webhook Secret</label>
                                                        <input type="password" name="configuration[webhook_secret]"
                                                               value="{{ $gateway->configuration['webhook_secret'] ?? '' }}"
                                                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                    </div>
                                                </div>
                                            @elseif($gateway->gateway_name === 'midtrans')
                                                <div class="space-y-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700">Server Key</label>
                                                        <input type="password" name="configuration[server_key]"
                                                               value="{{ $gateway->configuration['server_key'] ?? '' }}"
                                                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700">Client Key</label>
                                                        <input type="text" name="configuration[client_key]"
                                                               value="{{ $gateway->configuration['client_key'] ?? '' }}"
                                                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                                    </div>
                                                    <div class="flex items-center">
                                                        <input type="checkbox" name="configuration[is_production]" value="1"
                                                               {{ ($gateway->configuration['is_production'] ?? false) ? 'checked' : '' }}
                                                               class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                                        <span class="ml-2 text-sm text-gray-600">Production Mode</span>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>

                                        <!-- Supported Countries & Currencies -->
                                        <div>
                                            <h5 class="font-medium text-gray-900 mb-3">Regional Support</h5>

                                            <div class="space-y-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700">Supported Countries</label>
                                                    <select name="supported_countries[]" multiple
                                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                        <option value="US" {{ in_array('US', $gateway->supported_countries ?? []) ? 'selected' : '' }}>United States</option>
                                                        <option value="ID" {{ in_array('ID', $gateway->supported_countries ?? []) ? 'selected' : '' }}>Indonesia</option>
                                                        <option value="MY" {{ in_array('MY', $gateway->supported_countries ?? []) ? 'selected' : '' }}>Malaysia</option>
                                                        <option value="SG" {{ in_array('SG', $gateway->supported_countries ?? []) ? 'selected' : '' }}>Singapore</option>
                                                        <option value="TH" {{ in_array('TH', $gateway->supported_countries ?? []) ? 'selected' : '' }}>Thailand</option>
                                                    </select>
                                                    <p class="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple</p>
                                                </div>

                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700">Supported Currencies</label>
                                                    <select name="supported_currencies[]" multiple
                                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                        <option value="USD" {{ in_array('USD', $gateway->supported_currencies ?? []) ? 'selected' : '' }}>USD</option>
                                                        <option value="IDR" {{ in_array('IDR', $gateway->supported_currencies ?? []) ? 'selected' : '' }}>IDR</option>
                                                        <option value="MYR" {{ in_array('MYR', $gateway->supported_currencies ?? []) ? 'selected' : '' }}>MYR</option>
                                                        <option value="SGD" {{ in_array('SGD', $gateway->supported_currencies ?? []) ? 'selected' : '' }}>SGD</option>
                                                        <option value="THB" {{ in_array('THB', $gateway->supported_currencies ?? []) ? 'selected' : '' }}>THB</option>
                                                    </select>
                                                </div>

                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700">Priority</label>
                                                    <input type="number" name="priority" min="0"
                                                           value="{{ $gateway->priority }}"
                                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                    <p class="text-xs text-gray-500 mt-1">Lower numbers have higher priority</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-6 flex justify-end">
                                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                            Update {{ ucfirst($gateway->gateway_name) }} Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Gateway Status Overview -->
            <div class="mt-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Gateway Status Overview</h3>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gateway</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Default</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Countries</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currencies</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($gateways as $gateway)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="text-sm font-medium text-gray-900 capitalize">
                                                    {{ $gateway->gateway_name }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($gateway->is_enabled)
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    Enabled
                                                </span>
                                            @else
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    Disabled
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            @if($gateway->is_default)
                                                <span class="text-blue-600 font-medium">Yes</span>
                                            @else
                                                <span class="text-gray-400">No</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                                            {{ $gateway->environment }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ implode(', ', $gateway->supported_countries ?? []) ?: 'All' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ implode(', ', $gateway->supported_currencies ?? []) ?: 'All' }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

## 🛠 Step 6: Routes Configuration

### 6.1 Add Admin Settings Routes

Add to `routes/web.php`:

```php
// Admin Settings Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // Main settings dashboard
    Route::get('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'index'])
        ->name('settings.index');

    // Payment Gateway Settings
    Route::get('/settings/payment-gateways', [App\Http\Controllers\Admin\SettingsController::class, 'paymentGateways'])
        ->name('settings.payment-gateways');
    Route::put('/settings/payment-gateways/{id}', [App\Http\Controllers\Admin\SettingsController::class, 'updatePaymentGateway'])
        ->name('settings.payment-gateways.update');

    // System Settings
    Route::get('/settings/system', [App\Http\Controllers\Admin\SettingsController::class, 'systemSettings'])
        ->name('settings.system');
    Route::put('/settings/system', [App\Http\Controllers\Admin\SettingsController::class, 'updateSystemSettings'])
        ->name('settings.system.update');

    // Business Settings
    Route::get('/settings/business', [App\Http\Controllers\Admin\SettingsController::class, 'businessSettings'])
        ->name('settings.business');
    Route::put('/settings/business', [App\Http\Controllers\Admin\SettingsController::class, 'updateBusinessSettings'])
        ->name('settings.business.update');

    // Regional Settings
    Route::get('/settings/regional', [App\Http\Controllers\Admin\SettingsController::class, 'regionalSettings'])
        ->name('settings.regional');
    Route::put('/settings/regional', [App\Http\Controllers\Admin\SettingsController::class, 'updateRegionalSettings'])
        ->name('settings.regional.update');
});
```

## 🛠 Step 7: Database Seeders

### 7.1 Create Payment Gateway Seeder

```bash
php artisan make:seeder PaymentGatewaySeeder
```

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaymentGatewaySetting;

class PaymentGatewaySeeder extends Seeder
{
    public function run(): void
    {
        // Stripe Configuration
        PaymentGatewaySetting::create([
            'gateway_name' => 'stripe',
            'is_enabled' => true,
            'is_default' => false,
            'configuration' => [
                'publishable_key' => env('STRIPE_KEY', ''),
                'secret_key' => env('STRIPE_SECRET', ''),
                'webhook_secret' => env('STRIPE_WEBHOOK_SECRET', ''),
            ],
            'supported_countries' => ['US', 'CA', 'GB', 'AU', 'SG', 'MY'],
            'supported_currencies' => ['USD', 'CAD', 'GBP', 'AUD', 'SGD', 'MYR'],
            'environment' => 'sandbox',
            'priority' => 1
        ]);

        // Midtrans Configuration
        PaymentGatewaySetting::create([
            'gateway_name' => 'midtrans',
            'is_enabled' => true,
            'is_default' => true, // Default for Indonesian market
            'configuration' => [
                'server_key' => env('MIDTRANS_SERVER_KEY', ''),
                'client_key' => env('MIDTRANS_CLIENT_KEY', ''),
                'is_production' => env('MIDTRANS_IS_PRODUCTION', false),
                'is_sanitized' => true,
                'is_3ds' => true,
            ],
            'supported_countries' => ['ID'],
            'supported_currencies' => ['IDR'],
            'environment' => 'sandbox',
            'priority' => 0 // Higher priority for Indonesian customers
        ]);
    }
}
```

### 7.2 Create Admin Settings Seeder

```bash
php artisan make:seeder AdminSettingsSeeder
```

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AdminSetting;

class AdminSettingsSeeder extends Seeder
{
    public function run(): void
    {
        $settings = [
            // Business Settings
            [
                'category' => 'business',
                'key' => 'business_name',
                'value' => 'Car Wash Pro',
                'type' => 'string',
                'description' => 'Business name displayed on receipts and invoices'
            ],
            [
                'category' => 'business',
                'key' => 'business_address',
                'value' => 'Jl. Sudirman No. 123, Jakarta, Indonesia',
                'type' => 'string',
                'description' => 'Business address for receipts and legal documents'
            ],
            [
                'category' => 'business',
                'key' => 'business_phone',
                'value' => '+62-21-1234-5678',
                'type' => 'string',
                'description' => 'Business contact phone number'
            ],
            [
                'category' => 'business',
                'key' => 'business_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'description' => 'Business contact email address'
            ],
            [
                'category' => 'business',
                'key' => 'tax_rate',
                'value' => '11.0',
                'type' => 'string',
                'description' => 'Default tax rate percentage (PPN for Indonesia)'
            ],
            [
                'category' => 'business',
                'key' => 'currency',
                'value' => 'IDR',
                'type' => 'string',
                'description' => 'Default currency code'
            ],
            [
                'category' => 'business',
                'key' => 'timezone',
                'value' => 'Asia/Jakarta',
                'type' => 'string',
                'description' => 'Business timezone'
            ],

            // System Settings
            [
                'category' => 'system',
                'key' => 'app_maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Enable maintenance mode'
            ],
            [
                'category' => 'system',
                'key' => 'max_upload_size',
                'value' => '10240',
                'type' => 'integer',
                'description' => 'Maximum file upload size in KB'
            ],
            [
                'category' => 'system',
                'key' => 'session_timeout',
                'value' => '120',
                'type' => 'integer',
                'description' => 'Session timeout in minutes'
            ],
            [
                'category' => 'system',
                'key' => 'auto_backup_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable automatic database backups'
            ],

            // Regional Settings
            [
                'category' => 'regional',
                'key' => 'default_country',
                'value' => 'ID',
                'type' => 'string',
                'description' => 'Default country code'
            ],
            [
                'category' => 'regional',
                'key' => 'default_language',
                'value' => 'id',
                'type' => 'string',
                'description' => 'Default language code'
            ],
            [
                'category' => 'regional',
                'key' => 'date_format',
                'value' => 'd/m/Y',
                'type' => 'string',
                'description' => 'Default date format'
            ],
            [
                'category' => 'regional',
                'key' => 'time_format',
                'value' => 'H:i',
                'type' => 'string',
                'description' => 'Default time format'
            ],
        ];

        foreach ($settings as $setting) {
            AdminSetting::create($setting);
        }
    }
}
```

## 🛠 Step 8: Updated Payment Controller

### 8.1 Create Unified Payment Controller

```bash
php artisan make:controller PaymentController
```

```php
<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Services\PaymentGatewayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $paymentGatewayService;

    public function __construct(PaymentGatewayService $paymentGatewayService)
    {
        $this->paymentGatewayService = $paymentGatewayService;
    }

    public function selectGateway(Booking $booking)
    {
        $customer = $booking->customer;
        $availableGateways = $this->paymentGatewayService->getAvailableGateways($customer->country ?? 'ID');

        return view('payments.select-gateway', compact('booking', 'availableGateways'));
    }

    public function processPayment(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'gateway' => 'required|string|in:stripe,midtrans',
            'payment_method' => 'nullable|string',
        ]);

        try {
            $paymentData = [
                'amount' => $booking->total_amount,
                'currency' => $booking->currency ?? 'IDR',
                'order_id' => $booking->booking_number,
                'customer' => [
                    'first_name' => $booking->customer->name,
                    'email' => $booking->customer->email,
                    'phone' => $booking->customer->phone,
                ],
                'items' => [
                    [
                        'id' => $booking->service->id,
                        'price' => $booking->total_amount,
                        'quantity' => 1,
                        'name' => $booking->service->name,
                    ]
                ],
                'metadata' => [
                    'booking_id' => $booking->id,
                ]
            ];

            $result = $this->paymentGatewayService->processPayment(
                $validated['gateway'],
                $paymentData
            );

            // Store payment attempt
            $booking->payments()->create([
                'gateway' => $validated['gateway'],
                'gateway_payment_id' => $result['id'] ?? $result->id,
                'amount' => $booking->total_amount,
                'currency' => $booking->currency ?? 'IDR',
                'status' => 'pending',
                'gateway_response' => $result,
            ]);

            // Redirect based on gateway
            if ($validated['gateway'] === 'stripe') {
                return view('payments.stripe.process', compact('booking', 'result'));
            } else {
                return view('payments.midtrans.process', compact('booking', 'result'));
            }

        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'booking_id' => $booking->id,
                'gateway' => $validated['gateway'],
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'Payment processing failed. Please try again.');
        }
    }

    public function success(Request $request)
    {
        $bookingId = $request->get('booking_id');
        $booking = Booking::findOrFail($bookingId);

        return view('payments.success', compact('booking'));
    }

    public function failed(Request $request)
    {
        $bookingId = $request->get('booking_id');
        $booking = Booking::findOrFail($bookingId);

        return view('payments.failed', compact('booking'));
    }
}
```

### 8.2 Create Gateway Selection View

Create `resources/views/payments/select-gateway.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Select Payment Method
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <!-- Booking Summary -->
                    <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Summary</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-600">Booking Number</p>
                                <p class="font-medium">{{ $booking->booking_number }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Service</p>
                                <p class="font-medium">{{ $booking->service->name }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Date & Time</p>
                                <p class="font-medium">{{ $booking->booking_date->format('d M Y, H:i') }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Total Amount</p>
                                <p class="font-medium text-lg text-green-600">
                                    {{ $booking->currency === 'IDR' ? 'Rp ' : '$' }}{{ number_format($booking->total_amount, 0) }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Gateway Selection -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-6">Choose Payment Method</h3>

                        <form method="POST" action="{{ route('payments.process', $booking) }}">
                            @csrf

                            <div class="space-y-4">
                                @foreach($availableGateways as $gateway)
                                    <label class="block">
                                        <input type="radio" name="gateway" value="{{ $gateway->gateway_name }}"
                                               class="sr-only peer"
                                               {{ $gateway->is_default ? 'checked' : '' }}>

                                        <div class="p-6 border-2 border-gray-200 rounded-lg cursor-pointer transition-all
                                                    peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-gray-300">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    @if($gateway->gateway_name === 'stripe')
                                                        <div class="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center">
                                                            <span class="text-white font-bold text-xl">S</span>
                                                        </div>
                                                        <div>
                                                            <h4 class="text-lg font-medium text-gray-900">Stripe</h4>
                                                            <p class="text-sm text-gray-600">Credit Card, Debit Card</p>
                                                            <p class="text-xs text-gray-500">International payments</p>
                                                        </div>
                                                    @elseif($gateway->gateway_name === 'midtrans')
                                                        <div class="w-16 h-16 bg-green-500 rounded-lg flex items-center justify-center">
                                                            <span class="text-white font-bold text-xl">M</span>
                                                        </div>
                                                        <div>
                                                            <h4 class="text-lg font-medium text-gray-900">Midtrans</h4>
                                                            <p class="text-sm text-gray-600">GoPay, OVO, DANA, Bank Transfer</p>
                                                            <p class="text-xs text-gray-500">Indonesian payment methods</p>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="flex items-center space-x-2">
                                                    @if($gateway->is_default)
                                                        <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                                                            Recommended
                                                        </span>
                                                    @endif

                                                    <div class="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-blue-500 peer-checked:bg-blue-500 flex items-center justify-center">
                                                        <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Supported Payment Methods -->
                                            <div class="mt-4 flex flex-wrap gap-2">
                                                @if($gateway->gateway_name === 'stripe')
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Visa</span>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Mastercard</span>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">American Express</span>
                                                @elseif($gateway->gateway_name === 'midtrans')
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">GoPay</span>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">OVO</span>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">DANA</span>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">ShopeePay</span>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Bank Transfer</span>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Indomaret</span>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Alfamart</span>
                                                @endif
                                            </div>
                                        </div>
                                    </label>
                                @endforeach
                            </div>

                            <div class="mt-8 flex justify-between">
                                <a href="{{ route('bookings.show', $booking) }}"
                                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded">
                                    Back to Booking
                                </a>

                                <button type="submit"
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded">
                                    Proceed to Payment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

## 🛠 Step 9: Environment Configuration

### 9.1 Update .env.example

Add to `.env.example`:

```env
# Payment Gateway Settings
STRIPE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

MIDTRANS_SERVER_KEY=your_midtrans_server_key
MIDTRANS_CLIENT_KEY=your_midtrans_client_key
MIDTRANS_IS_PRODUCTION=false

# Business Settings
BUSINESS_NAME="Car Wash Pro"
BUSINESS_ADDRESS="Jl. Sudirman No. 123, Jakarta, Indonesia"
BUSINESS_PHONE="+62-21-1234-5678"
BUSINESS_EMAIL="<EMAIL>"
DEFAULT_CURRENCY=IDR
DEFAULT_TIMEZONE="Asia/Jakarta"
TAX_RATE=11.0
```

## 🛠 Step 10: Testing and Usage

### 10.1 Run Migrations and Seeders

```bash
# Run migrations
php artisan migrate

# Run seeders
php artisan db:seed --class=PaymentGatewaySeeder
php artisan db:seed --class=AdminSettingsSeeder
```

### 10.2 Access Admin Settings

1. **Login as Admin** and navigate to `/admin/settings`
2. **Configure Payment Gateways**:
   - Enable/disable Stripe or Midtrans
   - Set default gateway
   - Configure API keys
   - Set supported countries and currencies
3. **Update Business Settings**:
   - Company information
   - Tax rates
   - Currency preferences
4. **System Configuration**:
   - Maintenance mode
   - Upload limits
   - Session timeouts

### 10.3 Payment Flow

1. **Customer selects service** and creates booking
2. **System automatically selects best gateway** based on customer location
3. **Customer can choose payment method** from available gateways
4. **Payment is processed** through selected gateway
5. **Admin can monitor** all payments through dashboard

## 🎯 Key Features Implemented

✅ **Unified Payment Gateway Management**
- Single interface for multiple payment providers
- Easy enable/disable functionality
- Priority-based gateway selection

✅ **Regional Auto-Selection**
- Automatic gateway selection based on customer location
- Currency-specific routing
- Country-specific payment methods

✅ **Admin Configuration Interface**
- Visual gateway management
- Real-time settings updates
- Secure credential storage

✅ **Business Settings Management**
- Company information configuration
- Tax rate management
- Regional preferences

✅ **System Settings Control**
- Maintenance mode toggle
- Upload limits configuration
- Session management

## 🚀 Next Steps

1. **Add more payment gateways** (PayPal, Razorpay, etc.)
2. **Implement payment analytics** and reporting
3. **Add webhook management** interface
4. **Create payment method testing** tools
5. **Add backup/restore** functionality for settings

This comprehensive admin settings system provides complete control over payment gateway configuration while maintaining flexibility for future enhancements!
