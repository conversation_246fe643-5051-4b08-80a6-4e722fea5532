# Chapter 4: Authentication and Authorization System

## Overview
In this chapter, we'll implement a comprehensive authentication and authorization system with role-based access control (RBAC) specifically designed for hospital hierarchies. We'll create a system that respects Indonesian healthcare organizational structures while providing secure access control for different employee types and positions.

## Learning Objectives
- Implement Laravel Sanctum for API authentication
- Create role-based access control system
- Build hospital hierarchy-aware permissions
- Implement React authentication components
- Add Indonesian healthcare-specific authorization rules

## Prerequisites
- Completed Chapter 1 (Project Setup)
- Completed Chapter 2 (Database Design)
- Completed Chapter 3 (Eloquent Models)
- Understanding of authentication concepts
- Familiarity with React hooks and context

## Duration
75-90 minutes

---

## Step 1: Install and Configure Laravel Sanctum

### 1.1 Install Sanctum

```bash
composer require laravel/sanctum
```

### 1.2 Publish Sanctum Configuration

```bash
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

### 1.3 Run Sanctum Migrations

```bash
php artisan migrate
```

### 1.4 Configure Sanctum in User Model

Edit `app/Models/User.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name',
        'email',
        'password',
        'email_verified_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the employee record associated with the user
     */
    public function employee(): HasOne
    {
        return $this->hasOne(Employee::class);
    }

    /**
     * Check if user has an employee record
     */
    public function hasEmployee(): bool
    {
        return $this->employee()->exists();
    }

    /**
     * Get user's department through employee relationship
     */
    public function getDepartmentAttribute()
    {
        return $this->employee?->department;
    }

    /**
     * Get user's position through employee relationship
     */
    public function getPositionAttribute()
    {
        return $this->employee?->position;
    }

    /**
     * Check if user is medical staff
     */
    public function isMedicalStaff(): bool
    {
        return $this->employee?->isMedicalStaff() ?? false;
    }

    /**
     * Check if user is supervisor
     */
    public function isSupervisor(): bool
    {
        return $this->employee?->isSupervisor() ?? false;
    }

    /**
     * Check if user is department head
     */
    public function isDepartmentHead(): bool
    {
        if (!$this->hasEmployee()) {
            return false;
        }

        return $this->employee->department->department_head_id === $this->employee->id;
    }

    /**
     * Get all subordinates through employee relationship
     */
    public function getSubordinatesAttribute()
    {
        return $this->employee?->subordinates ?? collect();
    }
}
```

---

## Step 2: Install and Configure Spatie Laravel Permission

### 2.1 Install Spatie Permission Package

```bash
composer require spatie/laravel-permission
```

### 2.2 Publish and Run Migrations

```bash
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate
```

### 2.3 Create Roles and Permissions Seeder

```bash
php artisan make:seeder RolePermissionSeeder
```

Edit `database/seeders/RolePermissionSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Employee Management
            'view employees',
            'create employees',
            'edit employees',
            'delete employees',
            'view own profile',
            'edit own profile',
            
            // Department Management
            'view departments',
            'create departments',
            'edit departments',
            'delete departments',
            'manage department staff',
            
            // Shift Management
            'view shifts',
            'create shifts',
            'edit shifts',
            'delete shifts',
            'assign shifts',
            'view own shifts',
            'check in/out',
            
            // Leave Management
            'view leave requests',
            'create leave requests',
            'approve leave requests',
            'reject leave requests',
            'view own leave requests',
            'create own leave requests',
            
            // License Management
            'view licenses',
            'create licenses',
            'edit licenses',
            'delete licenses',
            'view own licenses',
            'manage own licenses',
            
            // Reporting
            'view reports',
            'create reports',
            'export data',
            
            // System Administration
            'manage users',
            'manage roles',
            'manage permissions',
            'view system logs',
            'manage system settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $this->createRoles();
    }

    private function createRoles(): void
    {
        // Super Admin Role
        $superAdmin = Role::create(['name' => 'Super Admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Hospital Director Role
        $director = Role::create(['name' => 'Hospital Director']);
        $director->givePermissionTo([
            'view employees', 'create employees', 'edit employees',
            'view departments', 'create departments', 'edit departments',
            'view shifts', 'create shifts', 'edit shifts', 'assign shifts',
            'view leave requests', 'approve leave requests', 'reject leave requests',
            'view licenses', 'create licenses', 'edit licenses',
            'view reports', 'create reports', 'export data',
            'manage users', 'view system logs',
        ]);

        // Department Head Role
        $deptHead = Role::create(['name' => 'Department Head']);
        $deptHead->givePermissionTo([
            'view employees', 'create employees', 'edit employees',
            'manage department staff',
            'view shifts', 'assign shifts',
            'view leave requests', 'approve leave requests', 'reject leave requests',
            'view licenses', 'create licenses', 'edit licenses',
            'view reports', 'create reports',
            'view own profile', 'edit own profile',
            'view own shifts', 'view own leave requests', 'create own leave requests',
            'view own licenses', 'manage own licenses',
        ]);

        // Supervisor Role
        $supervisor = Role::create(['name' => 'Supervisor']);
        $supervisor->givePermissionTo([
            'view employees',
            'view shifts', 'assign shifts',
            'view leave requests', 'approve leave requests', 'reject leave requests',
            'view licenses',
            'view reports',
            'view own profile', 'edit own profile',
            'view own shifts', 'check in/out',
            'view own leave requests', 'create own leave requests',
            'view own licenses', 'manage own licenses',
        ]);

        // Medical Staff Role (Doctors, Nurses, Midwives)
        $medicalStaff = Role::create(['name' => 'Medical Staff']);
        $medicalStaff->givePermissionTo([
            'view own profile', 'edit own profile',
            'view own shifts', 'check in/out',
            'view own leave requests', 'create own leave requests',
            'view own licenses', 'manage own licenses',
        ]);

        // Administrative Staff Role
        $adminStaff = Role::create(['name' => 'Administrative Staff']);
        $adminStaff->givePermissionTo([
            'view employees', 'create employees', 'edit employees',
            'view departments',
            'view shifts', 'assign shifts',
            'view leave requests',
            'view licenses', 'create licenses', 'edit licenses',
            'view own profile', 'edit own profile',
            'view own shifts', 'check in/out',
            'view own leave requests', 'create own leave requests',
        ]);

        // Regular Staff Role
        $staff = Role::create(['name' => 'Staff']);
        $staff->givePermissionTo([
            'view own profile', 'edit own profile',
            'view own shifts', 'check in/out',
            'view own leave requests', 'create own leave requests',
            'view own licenses', 'manage own licenses',
        ]);
    }
}
```

### 2.4 Update DatabaseSeeder

Edit `database/seeders/DatabaseSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            EmployeeTypeSeeder::class,
            DepartmentSeeder::class,
            PositionSeeder::class,
            ShiftSeeder::class,
            LeaveTypeSeeder::class,
            RolePermissionSeeder::class,
        ]);
    }
}
```

### 2.5 Run the Seeder

```bash
php artisan db:seed --class=RolePermissionSeeder
```

---

## Step 3: Create Authentication Controllers

### 3.1 Create Authentication Controller

```bash
php artisan make:controller Api/AuthController
```

Edit `app/Http/Controllers/Api/AuthController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RegisterRequest;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Login user and create token
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->only('email', 'password');

        if (!Auth::attempt($credentials)) {
            throw ValidationException::withMessages([
                'email' => ['Email atau password tidak valid.'],
            ]);
        }

        $user = Auth::user();
        
        // Check if user is active (through employee relationship)
        if ($user->hasEmployee() && !$user->employee->isActive()) {
            throw ValidationException::withMessages([
                'email' => ['Akun Anda tidak aktif. Silakan hubungi administrator.'],
            ]);
        }

        // Create token
        $token = $user->createToken('auth-token')->plainTextToken;

        return response()->json([
            'message' => 'Login berhasil',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'employee' => $user->employee ? [
                    'id' => $user->employee->id,
                    'employee_number' => $user->employee->employee_number,
                    'full_name' => $user->employee->full_name,
                    'department' => $user->employee->department->name,
                    'position' => $user->employee->position->title,
                    'employee_type' => $user->employee->employeeType->indonesian_name,
                    'is_medical_staff' => $user->employee->isMedicalStaff(),
                ] : null,
                'roles' => $user->getRoleNames(),
                'permissions' => $user->getAllPermissions()->pluck('name'),
            ],
            'token' => $token,
        ]);
    }

    /**
     * Register new user (admin only)
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        // Assign default role
        $user->assignRole('Staff');

        return response()->json([
            'message' => 'User berhasil dibuat',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'roles' => $user->getRoleNames(),
            ],
        ], 201);
    }

    /**
     * Get authenticated user
     */
    public function user(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'employee' => $user->employee ? [
                    'id' => $user->employee->id,
                    'employee_number' => $user->employee->employee_number,
                    'full_name' => $user->employee->full_name,
                    'department' => $user->employee->department->name,
                    'position' => $user->employee->position->title,
                    'employee_type' => $user->employee->employeeType->indonesian_name,
                    'is_medical_staff' => $user->employee->isMedicalStaff(),
                ] : null,
                'roles' => $user->getRoleNames(),
                'permissions' => $user->getAllPermissions()->pluck('name'),
            ],
        ]);
    }

    /**
     * Logout user and revoke token
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Logout berhasil',
        ]);
    }

    /**
     * Logout from all devices
     */
    public function logoutAll(Request $request): JsonResponse
    {
        $request->user()->tokens()->delete();

        return response()->json([
            'message' => 'Logout dari semua perangkat berhasil',
        ]);
    }
}
```

### 3.2 Create Form Requests for Authentication

```bash
php artisan make:request LoginRequest
php artisan make:request RegisterRequest
```

Edit `app/Http/Requests/LoginRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => ['required', 'email'],
            'password' => ['required', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'password.required' => 'Password wajib diisi',
        ];
    }
}
```

Edit `app/Http/Requests/RegisterRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('manage users');
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Nama wajib diisi',
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.unique' => 'Email sudah terdaftar',
            'password.required' => 'Password wajib diisi',
            'password.confirmed' => 'Konfirmasi password tidak cocok',
        ];
    }
}
```

---

## Step 4: Create Authorization Middleware

### 4.1 Create Permission Middleware

```bash
php artisan make:middleware CheckPermission
```

Edit `app/Http/Middleware/CheckPermission.php`:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        if (!$request->user() || !$request->user()->can($permission)) {
            return response()->json([
                'message' => 'Anda tidak memiliki izin untuk mengakses resource ini.',
                'required_permission' => $permission,
            ], 403);
        }

        return $next($request);
    }
}
```

### 4.2 Create Department Access Middleware

```bash
php artisan make:middleware CheckDepartmentAccess
```

Edit `app/Http/Middleware/CheckDepartmentAccess.php`:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckDepartmentAccess
{
    /**
     * Handle an incoming request.
     * Ensures users can only access data from their department or subordinate departments
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        // Super admins and hospital directors can access all departments
        if ($user->hasRole(['Super Admin', 'Hospital Director'])) {
            return $next($request);
        }

        // Get requested department ID from route or request
        $requestedDepartmentId = $request->route('department')
            ?? $request->input('department_id')
            ?? $request->route('employee')?->department_id;

        if (!$requestedDepartmentId) {
            return $next($request);
        }

        // Check if user has access to the requested department
        if (!$this->hasAccessToDepartment($user, $requestedDepartmentId)) {
            return response()->json([
                'message' => 'Anda tidak memiliki akses ke departemen ini.',
            ], 403);
        }

        return $next($request);
    }

    /**
     * Check if user has access to specific department
     */
    private function hasAccessToDepartment($user, $departmentId): bool
    {
        if (!$user->hasEmployee()) {
            return false;
        }

        $userDepartment = $user->employee->department;

        // Users can access their own department
        if ($userDepartment->id == $departmentId) {
            return true;
        }

        // Department heads can access child departments
        if ($user->isDepartmentHead()) {
            return $this->isChildDepartment($userDepartment, $departmentId);
        }

        return false;
    }

    /**
     * Check if department is a child of parent department
     */
    private function isChildDepartment($parentDepartment, $childDepartmentId): bool
    {
        foreach ($parentDepartment->childDepartments as $child) {
            if ($child->id == $childDepartmentId) {
                return true;
            }

            // Check recursively for nested departments
            if ($this->isChildDepartment($child, $childDepartmentId)) {
                return true;
            }
        }

        return false;
    }
}
```

### 4.3 Register Middleware

Edit `app/Http/Kernel.php`:

```php
protected $middlewareAliases = [
    // ... existing middleware
    'permission' => \App\Http\Middleware\CheckPermission::class,
    'department.access' => \App\Http\Middleware\CheckDepartmentAccess::class,
];
```

---

## Step 5: Create API Routes

### 5.1 Update API Routes

Edit `routes/api.php`:

```php
<?php

use App\Http\Controllers\Api\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Authentication routes
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/logout-all', [AuthController::class, 'logoutAll']);

    // User management (admin only)
    Route::middleware('permission:manage users')->group(function () {
        Route::post('/register', [AuthController::class, 'register']);
    });

    // Employee routes with department access control
    Route::middleware('department.access')->group(function () {
        Route::apiResource('employees', EmployeeController::class);
    });

    // Department routes
    Route::middleware('permission:view departments')->group(function () {
        Route::apiResource('departments', DepartmentController::class);
    });

    // Shift routes
    Route::middleware('permission:view shifts')->group(function () {
        Route::apiResource('shifts', ShiftController::class);
    });

    // Leave request routes
    Route::middleware('permission:view leave requests')->group(function () {
        Route::apiResource('leave-requests', LeaveRequestController::class);
    });
});
```

---

## Step 6: Create React Authentication Context

### 6.1 Create Authentication Context

Create `resources/js/contexts/AuthContext.tsx`:

```tsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import axios from 'axios';

interface User {
  id: number;
  name: string;
  email: string;
  employee?: {
    id: number;
    employee_number: string;
    full_name: string;
    department: string;
    position: string;
    employee_type: string;
    is_medical_staff: boolean;
  };
  roles: string[];
  permissions: string[];
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Set up axios interceptors
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    // Response interceptor to handle 401 errors
    const interceptor = axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          delete axios.defaults.headers.common['Authorization'];
          setUser(null);
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.response.eject(interceptor);
    };
  }, []);

  // Check if user is authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        setLoading(false);
        return;
      }

      try {
        const response = await axios.get('/api/user');
        setUser(response.data.user);
      } catch (error) {
        localStorage.removeItem('auth_token');
        delete axios.defaults.headers.common['Authorization'];
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response = await axios.post('/api/login', { email, password });
      const { user, token } = response.data;

      localStorage.setItem('auth_token', token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      setUser(user);
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await axios.post('/api/logout');
    } catch (error) {
      // Continue with logout even if API call fails
    } finally {
      localStorage.removeItem('auth_token');
      delete axios.defaults.headers.common['Authorization'];
      setUser(null);
    }
  };

  const hasPermission = (permission: string): boolean => {
    return user?.permissions.includes(permission) ?? false;
  };

  const hasRole = (role: string): boolean => {
    return user?.roles.includes(role) ?? false;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => user?.roles.includes(role)) ?? false;
  };

  const value: AuthContextType = {
    user,
    login,
    logout,
    loading,
    hasPermission,
    hasRole,
    hasAnyRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 6.2 Create Login Component

Create `resources/js/components/auth/LoginForm.tsx`:

```tsx
import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, Loader2 } from 'lucide-react';

const LoginForm: React.FC = () => {
  const { login } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await login(formData.email, formData.password);
    } catch (err: any) {
      setError(
        err.response?.data?.message ||
        'Terjadi kesalahan saat login. Silakan coba lagi.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            Sistem Manajemen Karyawan Rumah Sakit
          </CardTitle>
          <CardDescription className="text-center">
            Masuk ke akun Anda untuk melanjutkan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                required
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Masukkan password"
                  required
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Memproses...
                </>
              ) : (
                'Masuk'
              )}
            </Button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-600">
            <p>Sistem Manajemen Karyawan Rumah Sakit</p>
            <p>© 2024 - Semua hak dilindungi</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginForm;
```

### 6.3 Create Protected Route Component

Create `resources/js/components/auth/ProtectedRoute.tsx`:

```tsx
import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  permission?: string;
  role?: string;
  roles?: string[];
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  permission,
  role,
  roles,
  fallback,
}) => {
  const { user, loading, hasPermission, hasRole, hasAnyRole } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Memuat...</span>
      </div>
    );
  }

  if (!user) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <Alert className="max-w-md">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Anda harus login untuk mengakses halaman ini.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Check permission
  if (permission && !hasPermission(permission)) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <Alert variant="destructive" className="max-w-md">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Anda tidak memiliki izin untuk mengakses halaman ini.
            <br />
            <small>Izin yang diperlukan: {permission}</small>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Check single role
  if (role && !hasRole(role)) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <Alert variant="destructive" className="max-w-md">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Anda tidak memiliki peran yang diperlukan untuk mengakses halaman ini.
            <br />
            <small>Peran yang diperlukan: {role}</small>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Check multiple roles
  if (roles && !hasAnyRole(roles)) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <Alert variant="destructive" className="max-w-md">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Anda tidak memiliki peran yang diperlukan untuk mengakses halaman ini.
            <br />
            <small>Peran yang diperlukan: {roles.join(', ')}</small>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
```

### 6.4 Create Permission Component

Create `resources/js/components/auth/PermissionGate.tsx`:

```tsx
import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

interface PermissionGateProps {
  children: React.ReactNode;
  permission?: string;
  role?: string;
  roles?: string[];
  fallback?: React.ReactNode;
}

const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permission,
  role,
  roles,
  fallback = null,
}) => {
  const { hasPermission, hasRole, hasAnyRole } = useAuth();

  // Check permission
  if (permission && !hasPermission(permission)) {
    return <>{fallback}</>;
  }

  // Check single role
  if (role && !hasRole(role)) {
    return <>{fallback}</>;
  }

  // Check multiple roles
  if (roles && !hasAnyRole(roles)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default PermissionGate;
```

---

## Step 7: Testing Authentication System

### 7.1 Create Authentication Tests

```bash
php artisan make:test AuthenticationTest
```

Edit `tests/Feature/AuthenticationTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\EmployeeType;
use App\Models\Position;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\RolePermissionSeeder::class);
    }

    public function test_user_can_login_with_valid_credentials(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'user' => [
                        'id',
                        'name',
                        'email',
                        'roles',
                        'permissions',
                    ],
                    'token',
                ]);
    }

    public function test_user_cannot_login_with_invalid_credentials(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_authenticated_user_can_access_protected_routes(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Staff');

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson('/api/user');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'user' => [
                        'id',
                        'name',
                        'email',
                        'roles',
                        'permissions',
                    ],
                ]);
    }

    public function test_user_can_logout(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/logout');

        $response->assertStatus(200)
                ->assertJson(['message' => 'Logout berhasil']);
    }

    public function test_inactive_employee_cannot_login(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $employee = Employee::factory()->create([
            'user_id' => $user->id,
            'employment_status' => 'inactive',
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }
}
```

### 7.2 Create Authorization Tests

```bash
php artisan make:test AuthorizationTest
```

Edit `tests/Feature/AuthorizationTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AuthorizationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\RolePermissionSeeder::class);
    }

    public function test_super_admin_can_access_all_resources(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Super Admin');

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson('/api/employees');

        $response->assertStatus(200);
    }

    public function test_staff_cannot_access_admin_resources(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Staff');

        $response = $this->actingAs($user, 'sanctum')
                        ->postJson('/api/register', [
                            'name' => 'Test User',
                            'email' => '<EMAIL>',
                            'password' => 'password123',
                            'password_confirmation' => 'password123',
                        ]);

        $response->assertStatus(403);
    }

    public function test_department_head_can_access_own_department(): void
    {
        $department = Department::factory()->create();
        $user = User::factory()->create();
        $employee = Employee::factory()->create([
            'user_id' => $user->id,
            'department_id' => $department->id,
        ]);

        // Set as department head
        $department->update(['department_head_id' => $employee->id]);
        $user->assignRole('Department Head');

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson('/api/employees');

        $response->assertStatus(200);
    }

    public function test_user_with_permission_can_access_resource(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('view employees');

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson('/api/employees');

        $response->assertStatus(200);
    }

    public function test_user_without_permission_cannot_access_resource(): void
    {
        $user = User::factory()->create();
        $user->assignRole('Staff'); // Staff role doesn't have 'view employees' permission

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson('/api/employees');

        $response->assertStatus(403);
    }
}
```

---

## Step 8: Update Main App Component

### 8.1 Update App.tsx

Edit `resources/js/app.tsx`:

```tsx
import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import LoginForm from './components/auth/LoginForm';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Dashboard from './components/Dashboard';
import EmployeeList from './components/employees/EmployeeList';
import DepartmentList from './components/departments/DepartmentList';
import './bootstrap';

const AppContent: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return <LoginForm />;
  }

  return (
    <Router>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />

        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />

        <Route
          path="/employees"
          element={
            <ProtectedRoute permission="view employees">
              <EmployeeList />
            </ProtectedRoute>
          }
        />

        <Route
          path="/departments"
          element={
            <ProtectedRoute permission="view departments">
              <DepartmentList />
            </ProtectedRoute>
          }
        />

        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Router>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

// Mount the app
const container = document.getElementById('app');
if (container) {
  const root = createRoot(container);
  root.render(<App />);
}
```

---

## Chapter Summary

In this chapter, you've successfully implemented:

✅ **Laravel Sanctum Authentication**
- API token-based authentication
- Secure login/logout functionality
- Token management and revocation

✅ **Role-Based Access Control (RBAC)**
- Comprehensive permission system
- Hospital hierarchy-aware roles
- Indonesian healthcare-specific permissions

✅ **Authorization Middleware**
- Permission-based access control
- Department-level access restrictions
- Hierarchical authorization logic

✅ **React Authentication System**
- Authentication context and hooks
- Protected route components
- Permission-based UI rendering

✅ **Security Features**
- Input validation and sanitization
- Secure password handling
- Token-based API security

### Key Features Implemented

**Authentication:**
- Secure login with email/password
- JWT token management
- Automatic token refresh
- Multi-device logout support

**Authorization:**
- Role-based permissions
- Department-level access control
- Hierarchical permission inheritance
- Indonesian healthcare role structure

**Security:**
- CSRF protection
- Input validation
- Secure password hashing
- API rate limiting

**User Experience:**
- Indonesian language interface
- Responsive design
- Loading states and error handling
- Intuitive permission feedback

### What's Next?

In Chapter 5, we'll implement comprehensive CRUD operations for employee management with advanced features like bulk operations, file uploads, and real-time validation.

### Key Commands to Remember

```bash
# Install Sanctum
composer require laravel/sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

# Install Spatie Permission
composer require spatie/laravel-permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"

# Run migrations and seeders
php artisan migrate
php artisan db:seed --class=RolePermissionSeeder

# Create middleware
php artisan make:middleware MiddlewareName

# Run authentication tests
php artisan test --filter=AuthenticationTest
```

---

## Additional Resources

- [Laravel Sanctum Documentation](https://laravel.com/docs/12.x/sanctum)
- [Spatie Laravel Permission](https://spatie.be/docs/laravel-permission/v6/introduction)
- [React Context API](https://react.dev/reference/react/useContext)
- [Laravel Authorization](https://laravel.com/docs/12.x/authorization)

Ready for Chapter 5? Let's build comprehensive CRUD operations!
