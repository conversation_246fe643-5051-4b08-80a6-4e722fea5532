# Chapter 10: User Roles and Permissions

Welcome to Chapter 10! In this chapter, we'll implement a comprehensive role-based access control (RBAC) system with granular permissions, staff management, and secure user administration.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Implement comprehensive user roles and permissions
- Create role-based access control system
- Add staff management and permissions
- Build admin panel for user management
- Create permission-based navigation and features
- Implement middleware for route protection
- Add role-based dashboard customization

## 📋 What We'll Cover

1. Creating roles and permissions system
2. Building permission middleware
3. Implementing staff management
4. Creating admin user management panel
5. Adding role-based navigation
6. Building permission-based features
7. Creating role assignment interface
8. Testing the permission system

## 🛠 Step 1: Creating Roles and Permissions Models

First, let's create the roles and permissions system:

```bash
# Create Role and Permission models
php artisan make:model Role -m
php artisan make:model Permission -m
```

Edit the Role migration `database/migrations/xxxx_xx_xx_xxxxxx_create_roles_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(0); // Higher number = higher priority
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};
```

Edit the Permission migration `database/migrations/xxxx_xx_xx_xxxxxx_create_permissions_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->string('category')->default('general'); // Group permissions by category
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('permissions');
    }
};
```

Create pivot tables:

```bash
# Create pivot table migrations
php artisan make:migration create_role_user_table
php artisan make:migration create_permission_role_table
php artisan make:migration create_permission_user_table
```

Edit `database/migrations/xxxx_xx_xx_xxxxxx_create_role_user_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('role_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('role_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('assigned_at')->useCurrent();
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['role_id', 'user_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('role_user');
    }
};
```

Edit `database/migrations/xxxx_xx_xx_xxxxxx_create_permission_role_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('permission_role', function (Blueprint $table) {
            $table->id();
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->foreignId('role_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['permission_id', 'role_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('permission_role');
    }
};
```

Edit `database/migrations/xxxx_xx_xx_xxxxxx_create_permission_user_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('permission_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('assigned_at')->useCurrent();
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['permission_id', 'user_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('permission_user');
    }
};
```

## 🛠 Step 2: Creating Role and Permission Models

Edit `app/Models/Role.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'is_active',
        'priority',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Role constants
    const ADMIN = 'admin';
    const STAFF = 'staff';
    const CUSTOMER = 'customer';

    // Relationships
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class)->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    // Helper methods
    public function hasPermission($permission): bool
    {
        if (is_string($permission)) {
            return $this->permissions()->where('name', $permission)->exists();
        }

        return $this->permissions()->where('id', $permission->id)->exists();
    }

    public function givePermissionTo($permission): void
    {
        if (is_string($permission)) {
            $permission = Permission::where('name', $permission)->firstOrFail();
        }

        $this->permissions()->syncWithoutDetaching([$permission->id]);
    }

    public function revokePermissionTo($permission): void
    {
        if (is_string($permission)) {
            $permission = Permission::where('name', $permission)->firstOrFail();
        }

        $this->permissions()->detach($permission->id);
    }

    public function syncPermissions(array $permissions): void
    {
        $permissionIds = collect($permissions)->map(function ($permission) {
            if (is_string($permission)) {
                return Permission::where('name', $permission)->firstOrFail()->id;
            }
            return is_object($permission) ? $permission->id : $permission;
        })->toArray();

        $this->permissions()->sync($permissionIds);
    }

    public function isAdmin(): bool
    {
        return $this->name === self::ADMIN;
    }

    public function isStaff(): bool
    {
        return $this->name === self::STAFF;
    }

    public function isCustomer(): bool
    {
        return $this->name === self::CUSTOMER;
    }
}
```

Edit `app/Models/Permission.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Permission constants
    const MANAGE_USERS = 'manage_users';
    const MANAGE_CUSTOMERS = 'manage_customers';
    const MANAGE_SERVICES = 'manage_services';
    const MANAGE_BOOKINGS = 'manage_bookings';
    const MANAGE_PAYMENTS = 'manage_payments';
    const VIEW_ANALYTICS = 'view_analytics';
    const EXPORT_REPORTS = 'export_reports';
    const MANAGE_SETTINGS = 'manage_settings';
    const PROCESS_REFUNDS = 'process_refunds';
    const VIEW_ALL_BOOKINGS = 'view_all_bookings';
    const EDIT_ALL_BOOKINGS = 'edit_all_bookings';
    const DELETE_BOOKINGS = 'delete_bookings';

    // Relationships
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class)->withTimestamps();
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    // Helper methods
    public static function getCategories(): array
    {
        return [
            'user_management' => 'User Management',
            'customer_management' => 'Customer Management',
            'service_management' => 'Service Management',
            'booking_management' => 'Booking Management',
            'payment_management' => 'Payment Management',
            'analytics' => 'Analytics & Reports',
            'system_settings' => 'System Settings',
        ];
    }

    public function getCategoryDisplayNameAttribute(): string
    {
        $categories = self::getCategories();
        return $categories[$this->category] ?? ucfirst(str_replace('_', ' ', $this->category));
    }
}
```

## 🛠 Step 3: Updating User Model

Update the User model to include role and permission relationships. Edit `app/Models/User.php`:

```php
// Add these relationships to the User model
public function roles(): BelongsToMany
{
    return $this->belongsToMany(Role::class)->withTimestamps();
}

public function permissions(): BelongsToMany
{
    return $this->belongsToMany(Permission::class)->withTimestamps();
}

// Add these helper methods
public function hasRole($role): bool
{
    if (is_string($role)) {
        return $this->roles()->where('name', $role)->exists();
    }

    return $this->roles()->where('id', $role->id)->exists();
}

public function hasAnyRole(array $roles): bool
{
    return $this->roles()->whereIn('name', $roles)->exists();
}

public function hasPermission($permission): bool
{
    // Check direct permissions
    if (is_string($permission)) {
        $directPermission = $this->permissions()->where('name', $permission)->exists();
    } else {
        $directPermission = $this->permissions()->where('id', $permission->id)->exists();
    }

    if ($directPermission) {
        return true;
    }

    // Check role-based permissions
    foreach ($this->roles as $role) {
        if ($role->hasPermission($permission)) {
            return true;
        }
    }

    return false;
}

public function assignRole($role): void
{
    if (is_string($role)) {
        $role = Role::where('name', $role)->firstOrFail();
    }

    $this->roles()->syncWithoutDetaching([$role->id]);
}

public function removeRole($role): void
{
    if (is_string($role)) {
        $role = Role::where('name', $role)->firstOrFail();
    }

    $this->roles()->detach($role->id);
}

public function syncRoles(array $roles): void
{
    $roleIds = collect($roles)->map(function ($role) {
        if (is_string($role)) {
            return Role::where('name', $role)->firstOrFail()->id;
        }
        return is_object($role) ? $role->id : $role;
    })->toArray();

    $this->roles()->sync($roleIds);
}

public function givePermissionTo($permission): void
{
    if (is_string($permission)) {
        $permission = Permission::where('name', $permission)->firstOrFail();
    }

    $this->permissions()->syncWithoutDetaching([$permission->id]);
}

public function revokePermissionTo($permission): void
{
    if (is_string($permission)) {
        $permission = Permission::where('name', $permission)->firstOrFail();
    }

    $this->permissions()->detach($permission->id);
}

public function isAdmin(): bool
{
    return $this->hasRole(Role::ADMIN);
}

public function isStaff(): bool
{
    return $this->hasRole(Role::STAFF);
}

public function isCustomer(): bool
{
    return $this->hasRole(Role::CUSTOMER);
}

public function getHighestPriorityRole(): ?Role
{
    return $this->roles()->byPriority()->first();
}

public function getAllPermissions(): Collection
{
    $directPermissions = $this->permissions;
    $rolePermissions = $this->roles->flatMap->permissions;

    return $directPermissions->merge($rolePermissions)->unique('id');
}
```

## 🛠 Step 6: Creating User Management Views

Now let's create the admin interface for managing users, roles, and permissions.

### 6.1 User Management Index View

Create `resources/views/admin/users/index.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                User Management
            </h2>
            <a href="{{ route('admin.users.create') }}"
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Add New User
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('admin.users.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                            <input type="text" name="search" id="search" value="{{ request('search') }}"
                                   placeholder="Name, email..."
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
                            <select name="role" id="role" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Roles</option>
                                @foreach($roles as $role)
                                    <option value="{{ $role->name }}" {{ request('role') == $role->name ? 'selected' : '' }}>
                                        {{ $role->display_name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Users Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($users as $user)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                        <span class="text-sm font-medium text-gray-700">
                                                            {{ strtoupper(substr($user->name, 0, 2)) }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex flex-wrap gap-1">
                                                @foreach($user->roles as $role)
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        @if($role->name === 'admin') bg-red-100 text-red-800
                                                        @elseif($role->name === 'staff') bg-blue-100 text-blue-800
                                                        @else bg-green-100 text-green-800 @endif">
                                                        {{ $role->display_name }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($user->is_active) bg-green-100 text-green-800
                                                @else bg-red-100 text-red-800 @endif">
                                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $user->created_at->format('M j, Y') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('admin.users.show', $user) }}"
                                                   class="text-indigo-600 hover:text-indigo-900">View</a>
                                                <a href="{{ route('admin.users.edit', $user) }}"
                                                   class="text-blue-600 hover:text-blue-900">Edit</a>
                                                @if($user->id !== auth()->id())
                                                    <button onclick="toggleUserStatus({{ $user->id }}, {{ $user->is_active ? 'false' : 'true' }})"
                                                            class="text-{{ $user->is_active ? 'red' : 'green' }}-600 hover:text-{{ $user->is_active ? 'red' : 'green' }}-900">
                                                        {{ $user->is_active ? 'Deactivate' : 'Activate' }}
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            No users found
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $users->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleUserStatus(userId, newStatus) {
            if (confirm('Are you sure you want to ' + (newStatus === 'true' ? 'activate' : 'deactivate') + ' this user?')) {
                fetch(`/admin/users/${userId}/toggle-status`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ is_active: newStatus === 'true' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error updating user status');
                    }
                });
            }
        }
    </script>
</x-app-layout>
```

### 6.2 User Create/Edit Form

Create `resources/views/admin/users/create.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Create New User
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('admin.users.store') }}">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Basic Information -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>

                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                                    <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                                    <input type="email" name="email" id="email" value="{{ old('email') }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('email')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                                    <input type="text" name="phone" id="phone" value="{{ old('phone') }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('phone')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                                    <input type="password" name="password" id="password" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('password')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                                    <input type="password" name="password_confirmation" id="password_confirmation" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>
                            </div>

                            <!-- Roles and Permissions -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900">Roles & Permissions</h3>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Assign Roles</label>
                                    <div class="space-y-2">
                                        @foreach($roles as $role)
                                            <label class="flex items-center">
                                                <input type="checkbox" name="roles[]" value="{{ $role->id }}"
                                                       {{ in_array($role->id, old('roles', [])) ? 'checked' : '' }}
                                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                <span class="ml-2 text-sm text-gray-700">
                                                    {{ $role->display_name }}
                                                    <span class="text-gray-500">({{ $role->description }})</span>
                                                </span>
                                            </label>
                                        @endforeach
                                    </div>
                                    @error('roles')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <span class="ml-2 text-sm text-gray-700">Active User</span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="send_welcome_email" value="1" checked
                                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <span class="ml-2 text-sm text-gray-700">Send welcome email</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="{{ route('admin.users.index') }}"
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

### 6.3 Role Management View

Create `resources/views/admin/roles/index.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Role Management
            </h2>
            <a href="{{ route('admin.roles.create') }}"
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Create New Role
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($roles as $role)
                            <div class="border border-gray-200 rounded-lg p-6">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">{{ $role->display_name }}</h3>
                                        <p class="text-sm text-gray-500">{{ $role->description }}</p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($role->name === 'admin') bg-red-100 text-red-800
                                        @elseif($role->name === 'staff') bg-blue-100 text-blue-800
                                        @else bg-green-100 text-green-800 @endif">
                                        Priority: {{ $role->priority }}
                                    </span>
                                </div>

                                <div class="mb-4">
                                    <p class="text-sm text-gray-600 mb-2">Users with this role:</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ $role->users_count }}</p>
                                </div>

                                <div class="mb-4">
                                    <p class="text-sm text-gray-600 mb-2">Permissions:</p>
                                    <div class="flex flex-wrap gap-1">
                                        @foreach($role->permissions->take(3) as $permission)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ $permission->display_name }}
                                            </span>
                                        @endforeach
                                        @if($role->permissions->count() > 3)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                +{{ $role->permissions->count() - 3 }} more
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $role->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $role->is_active ? 'Active' : 'Inactive' }}
                                    </span>

                                    <div class="flex space-x-2">
                                        <a href="{{ route('admin.roles.show', $role) }}"
                                           class="text-indigo-600 hover:text-indigo-900 text-sm">View</a>
                                        <a href="{{ route('admin.roles.edit', $role) }}"
                                           class="text-blue-600 hover:text-blue-900 text-sm">Edit</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

### 6.4 Permission Management View

Create `resources/views/admin/permissions/index.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Permission Management
            </h2>
            <a href="{{ route('admin.permissions.create') }}"
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Create New Permission
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Permission Categories -->
            @foreach($permissionsByCategory as $category => $permissions)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4 capitalize">{{ $category }} Permissions</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($permissions as $permission)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="font-medium text-gray-900">{{ $permission->display_name }}</h4>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {{ $permission->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $permission->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>

                                    <p class="text-sm text-gray-600 mb-3">{{ $permission->description }}</p>

                                    <div class="mb-3">
                                        <p class="text-xs text-gray-500 mb-1">Code: {{ $permission->name }}</p>
                                        <p class="text-xs text-gray-500">Used by {{ $permission->roles_count }} role(s)</p>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <div class="flex flex-wrap gap-1">
                                            @foreach($permission->roles->take(2) as $role)
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {{ $role->display_name }}
                                                </span>
                                            @endforeach
                                            @if($permission->roles->count() > 2)
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    +{{ $permission->roles->count() - 2 }}
                                                </span>
                                            @endif
                                        </div>

                                        <div class="flex space-x-2">
                                            <a href="{{ route('admin.permissions.edit', $permission) }}"
                                               class="text-blue-600 hover:text-blue-900 text-sm">Edit</a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</x-app-layout>
```

### 6.5 User Role Assignment View

Create `resources/views/admin/users/assign-roles.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Assign Roles - {{ $user->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('admin.users.update-roles', $user) }}">
                        @csrf
                        @method('PATCH')

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Current Roles -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Current Roles</h3>
                                <div class="space-y-3">
                                    @forelse($user->roles as $role)
                                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                            <div>
                                                <p class="font-medium text-blue-900">{{ $role->display_name }}</p>
                                                <p class="text-sm text-blue-700">{{ $role->description }}</p>
                                            </div>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Priority: {{ $role->priority }}
                                            </span>
                                        </div>
                                    @empty
                                        <p class="text-gray-500 italic">No roles assigned</p>
                                    @endforelse
                                </div>
                            </div>

                            <!-- Available Roles -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Available Roles</h3>
                                <div class="space-y-3">
                                    @foreach($availableRoles as $role)
                                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                            <input type="checkbox" name="roles[]" value="{{ $role->id }}"
                                                   {{ $user->hasRole($role->name) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <div class="ml-3 flex-1">
                                                <p class="font-medium text-gray-900">{{ $role->display_name }}</p>
                                                <p class="text-sm text-gray-600">{{ $role->description }}</p>
                                                <div class="mt-2 flex flex-wrap gap-1">
                                                    @foreach($role->permissions->take(3) as $permission)
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                            {{ $permission->display_name }}
                                                        </span>
                                                    @endforeach
                                                    @if($role->permissions->count() > 3)
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                            +{{ $role->permissions->count() - 3 }} more
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                Priority: {{ $role->priority }}
                                            </span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <!-- Direct Permissions -->
                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Direct Permissions (Optional)</h3>
                            <p class="text-sm text-gray-600 mb-4">Grant specific permissions directly to this user, independent of their roles.</p>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($allPermissions->groupBy('category') as $category => $permissions)
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <h4 class="font-medium text-gray-900 mb-3 capitalize">{{ $category }}</h4>
                                        <div class="space-y-2">
                                            @foreach($permissions as $permission)
                                                <label class="flex items-center">
                                                    <input type="checkbox" name="permissions[]" value="{{ $permission->id }}"
                                                           {{ $user->permissions->contains($permission->id) ? 'checked' : '' }}
                                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                    <span class="ml-2 text-sm text-gray-700">{{ $permission->display_name }}</span>
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="mt-8 flex justify-end space-x-3">
                            <a href="{{ route('admin.users.index') }}"
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Roles & Permissions
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

## 🧪 Testing the Permission System

1. **Test Role Assignment**:
   - Assign roles to users
   - Verify role-based access
   - Test permission inheritance

2. **Test Middleware**:
   - Access protected routes
   - Verify permission checks
   - Test unauthorized access

3. **Test Permission Management**:
   - Grant and revoke permissions
   - Test direct vs role-based permissions
   - Verify permission cascading

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Implemented comprehensive user roles and permissions
✅ Created role-based access control system
✅ Added staff management and permissions
✅ Built permission middleware for route protection
✅ Created database structure for RBAC
✅ Implemented role and permission seeders
✅ Added helper methods for permission checking

### Permission Features Implemented:
- **Role-Based Access Control**: Comprehensive RBAC system
- **Permission Management**: Granular permission control
- **Middleware Protection**: Route-level access control
- **Role Hierarchy**: Priority-based role system
- **Permission Inheritance**: Role-based permission inheritance
- **Direct Permissions**: User-specific permission assignment
- **Database Structure**: Scalable permission architecture

## 🚀 What's Next?

In the next chapter, we'll:
- Set up email notifications for booking confirmations
- Create automated reminder emails
- Build email templates and layouts
- Implement notification preferences
- Add email queue management

---

**Ready for notifications?** Let's move on to [Chapter 11: Email Notifications](./11-email-notifications.md)!
